<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MetalRateSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'phone',
        'metals',
        'frequency',
        'notification_channels',
        'is_active',
        'last_sent_at',
        'subscribed_at',
    ];

    protected $casts = [
        'metals' => 'array',
        'notification_channels' => 'array',
        'is_active' => 'boolean',
        'last_sent_at' => 'datetime',
        'subscribed_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($subscription) {
            if (!$subscription->subscribed_at) {
                $subscription->subscribed_at = now();
            }
        });
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByFrequency($query, string $frequency)
    {
        return $query->where('frequency', $frequency);
    }

    public function scopeByMetal($query, string $metal)
    {
        return $query->whereJsonContains('metals', $metal);
    }

    public function scopeDueForUpdate($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('last_sent_at')
              ->orWhere(function ($subQ) {
                  $subQ->where('frequency', 'hourly')
                       ->where('last_sent_at', '<', now()->subHour());
              })
              ->orWhere(function ($subQ) {
                  $subQ->where('frequency', 'daily')
                       ->where('last_sent_at', '<', now()->subDay());
              })
              ->orWhere(function ($subQ) {
                  $subQ->where('frequency', 'weekly')
                       ->where('last_sent_at', '<', now()->subWeek());
              });
        });
    }
}
