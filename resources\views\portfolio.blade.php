@extends('layouts.app')

@section('title', 'Portfolio - Our Best Work & Case Studies | Bhavitech')
@section('description', 'Explore our portfolio of successful web development, mobile app, and digital marketing projects. See how we\'ve helped businesses grow and succeed.')

@section('content')
<!-- Hero Section -->
<section class="relative py-20 lg:py-32 gradient-bg overflow-hidden">
    <div class="absolute inset-0">
        <div class="absolute inset-0 bg-black opacity-40"></div>
        <div class="absolute top-20 left-10 w-72 h-72 bg-yellow-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow"></div>
        <div class="absolute bottom-20 right-10 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow animation-delay-200"></div>
    </div>
    
    <div class="relative z-10 container-custom">
        <div class="max-w-4xl mx-auto text-center text-white">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">
                Our <span class="text-gradient bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">Portfolio</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-gray-200 animate-slide-up animation-delay-200">
                Discover how we've helped businesses transform their digital presence and achieve remarkable growth
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up animation-delay-400">
                <a href="{{ route('contact') }}" class="btn-primary text-lg px-8 py-4">Start Your Project</a>
                <a href="#case-studies" class="btn-secondary bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 text-lg px-8 py-4">View Case Studies</a>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Filter -->
<section class="py-16 bg-white">
    <div class="container-custom" x-data="portfolioFilter">
        <div class="text-center mb-12">
            <h2 class="text-4xl md:text-5xl font-bold mb-4">Featured Projects</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                Browse through our diverse portfolio of successful projects across various industries
            </p>
            
            <!-- Filter Buttons -->
            <div class="flex flex-wrap justify-center gap-4 mb-12">
                <button @click="setFilter('all')" :class="isActive('all') ? 'bg-primary-600 text-white shadow-lg transform scale-105' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'" class="px-6 py-3 rounded-lg font-medium transition-all duration-200">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        All Projects
                    </span>
                </button>
                <button @click="setFilter('web')" :class="isActive('web') ? 'bg-primary-600 text-white shadow-lg transform scale-105' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'" class="px-6 py-3 rounded-lg font-medium transition-all duration-200">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                        </svg>
                        Web Development
                    </span>
                </button>
                <button @click="setFilter('mobile')" :class="isActive('mobile') ? 'bg-primary-600 text-white shadow-lg transform scale-105' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'" class="px-6 py-3 rounded-lg font-medium transition-all duration-200">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                        </svg>
                        Mobile Apps
                    </span>
                </button>
                <button @click="setFilter('marketing')" :class="isActive('marketing') ? 'bg-primary-600 text-white shadow-lg transform scale-105' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'" class="px-6 py-3 rounded-lg font-medium transition-all duration-200">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        Digital Marketing
                    </span>
                </button>
                <button @click="setFilter('design')" :class="isActive('design') ? 'bg-primary-600 text-white shadow-lg transform scale-105' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'" class="px-6 py-3 rounded-lg font-medium transition-all duration-200">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                        </svg>
                        Graphic Design
                    </span>
                </button>
            </div>
        </div>

        <!-- Portfolio Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- E-commerce Website Project -->
            <div x-show="activeFilter === 'all' || activeFilter === 'web'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" class="card group hover:scale-105 transition-transform duration-300">
                <div class="relative overflow-hidden rounded-lg mb-4">
                    <div class="w-full h-48 bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <div class="text-white text-center">
                            <div class="text-xl font-bold">E-commerce</div>
                            <div class="text-sm opacity-90">Website</div>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-primary-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                            <h4 class="text-lg font-semibold mb-2">View Project</h4>
                            <p class="text-sm">Click to see details</p>
                        </div>
                    </div>
                </div>
                <h3 class="text-xl font-semibold mb-2">Fashion E-commerce Platform</h3>
                <p class="text-gray-600 mb-3">Complete e-commerce solution with payment gateway integration and inventory management.</p>
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">Laravel</span>
                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">MySQL</span>
                    <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">Payment Gateway</span>
                </div>
                <div class="text-sm text-gray-500">
                    <strong>Results:</strong> 300% increase in online sales
                </div>
            </div>

            <!-- Mobile App Project -->
            <div x-show="activeFilter === 'all' || activeFilter === 'mobile'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" class="card group hover:scale-105 transition-transform duration-300">
                <div class="relative overflow-hidden rounded-lg mb-4">
                    <div class="w-full h-48 bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <div class="text-white text-center">
                            <div class="text-xl font-bold">Food Delivery</div>
                            <div class="text-sm opacity-90">Mobile App</div>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-primary-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                            <h4 class="text-lg font-semibold mb-2">View Project</h4>
                            <p class="text-sm">Click to see details</p>
                        </div>
                    </div>
                </div>
                <h3 class="text-xl font-semibold mb-2">Food Delivery Mobile App</h3>
                <p class="text-gray-600 mb-3">Cross-platform mobile app with real-time tracking and payment integration.</p>
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">React Native</span>
                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Firebase</span>
                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">GPS Tracking</span>
                </div>
                <div class="text-sm text-gray-500">
                    <strong>Results:</strong> 50K+ downloads in first month
                </div>
            </div>

            <!-- Digital Marketing Project -->
            <div x-show="activeFilter === 'all' || activeFilter === 'marketing'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" class="card group hover:scale-105 transition-transform duration-300">
                <div class="relative overflow-hidden rounded-lg mb-4">
                    <div class="w-full h-48 bg-gradient-to-br from-amber-500 to-orange-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <div class="text-white text-center">
                            <div class="text-xl font-bold">SEO</div>
                            <div class="text-sm opacity-90">Campaign</div>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-primary-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                            <h4 class="text-lg font-semibold mb-2">View Project</h4>
                            <p class="text-sm">Click to see details</p>
                        </div>
                    </div>
                </div>
                <h3 class="text-xl font-semibold mb-2">Healthcare SEO Campaign</h3>
                <p class="text-gray-600 mb-3">Comprehensive SEO strategy that improved search rankings and organic traffic.</p>
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">SEO</span>
                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Content Marketing</span>
                    <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">Local SEO</span>
                </div>
                <div class="text-sm text-gray-500">
                    <strong>Results:</strong> 250% increase in organic traffic
                </div>
            </div>

            <!-- Brand Identity Project -->
            <div x-show="activeFilter === 'all' || activeFilter === 'design'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" class="card group hover:scale-105 transition-transform duration-300">
                <div class="relative overflow-hidden rounded-lg mb-4">
                    <div class="w-full h-48 bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <div class="text-white text-center">
                            <div class="text-xl font-bold">Brand</div>
                            <div class="text-sm opacity-90">Identity</div>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-primary-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                            <h4 class="text-lg font-semibold mb-2">View Project</h4>
                            <p class="text-sm">Click to see details</p>
                        </div>
                    </div>
                </div>
                <h3 class="text-xl font-semibold mb-2">Tech Startup Brand Identity</h3>
                <p class="text-gray-600 mb-3">Complete brand identity design including logo, guidelines, and marketing materials.</p>
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">Logo Design</span>
                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Brand Guidelines</span>
                    <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">Print Design</span>
                </div>
                <div class="text-sm text-gray-500">
                    <strong>Results:</strong> 400% increase in brand recognition
                </div>
            </div>

            <!-- Corporate Website -->
            <div x-show="activeFilter === 'all' || activeFilter === 'web'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" class="card group hover:scale-105 transition-transform duration-300">
                <div class="relative overflow-hidden rounded-lg mb-4">
                    <div class="w-full h-48 bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <div class="text-white text-center">
                            <div class="text-xl font-bold">Corporate</div>
                            <div class="text-sm opacity-90">Website</div>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-primary-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                            <h4 class="text-lg font-semibold mb-2">View Project</h4>
                            <p class="text-sm">Click to see details</p>
                        </div>
                    </div>
                </div>
                <h3 class="text-xl font-semibold mb-2">Manufacturing Company Website</h3>
                <p class="text-gray-600 mb-3">Professional corporate website with CMS and multi-language support.</p>
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">WordPress</span>
                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Multi-language</span>
                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 text-sm rounded-full">CMS</span>
                </div>
                <div class="text-sm text-gray-500">
                    <strong>Results:</strong> 180% increase in lead generation
                </div>
            </div>

            <!-- Social Media Campaign -->
            <div x-show="activeFilter === 'all' || activeFilter === 'marketing'" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" class="card group hover:scale-105 transition-transform duration-300">
                <div class="relative overflow-hidden rounded-lg mb-4">
                    <div class="w-full h-48 bg-gradient-to-br from-cyan-500 to-blue-500 flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <div class="text-white text-center">
                            <div class="text-xl font-bold">Social Media</div>
                            <div class="text-sm opacity-90">Campaign</div>
                        </div>
                    </div>
                    <div class="absolute inset-0 bg-primary-600 bg-opacity-0 group-hover:bg-opacity-80 transition-all duration-300 flex items-center justify-center">
                        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-white text-center">
                            <h4 class="text-lg font-semibold mb-2">View Project</h4>
                            <p class="text-sm">Click to see details</p>
                        </div>
                    </div>
                </div>
                <h3 class="text-xl font-semibold mb-2">Restaurant Social Media Campaign</h3>
                <p class="text-gray-600 mb-3">Comprehensive social media strategy across multiple platforms with content creation.</p>
                <div class="flex flex-wrap gap-2 mb-4">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">Facebook</span>
                    <span class="px-3 py-1 bg-green-100 text-green-800 text-sm rounded-full">Instagram</span>
                    <span class="px-3 py-1 bg-purple-100 text-purple-800 text-sm rounded-full">Content Creation</span>
                </div>
                <div class="text-sm text-gray-500">
                    <strong>Results:</strong> 500% increase in social engagement
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Client Testimonials -->
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold mb-4">What Our Clients Say</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Don't just take our word for it - hear from the businesses we've helped transform
            </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Testimonial 1 -->
            <div class="card">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">"Bhavitech transformed our online presence completely. Our website traffic increased by 300% and we're getting more qualified leads than ever before."</p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                        <span class="text-primary-600 font-semibold">RS</span>
                    </div>
                    <div>
                        <div class="font-semibold">Rajesh Sharma</div>
                        <div class="text-sm text-gray-500">CEO, TechCorp Solutions</div>
                    </div>
                </div>
            </div>

            <!-- Testimonial 2 -->
            <div class="card">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">"The mobile app they developed for us exceeded all expectations. User engagement is through the roof and our customer satisfaction has improved dramatically."</p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                        <span class="text-primary-600 font-semibold">PK</span>
                    </div>
                    <div>
                        <div class="font-semibold">Priya Krishnan</div>
                        <div class="text-sm text-gray-500">Founder, FoodieHub</div>
                    </div>
                </div>
            </div>

            <!-- Testimonial 3 -->
            <div class="card">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-gray-600 mb-4">"Their digital marketing expertise helped us reach new customers and grow our business by 400%. The ROI has been incredible."</p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mr-4">
                        <span class="text-primary-600 font-semibold">AM</span>
                    </div>
                    <div>
                        <div class="font-semibold">Arjun Mehta</div>
                        <div class="text-sm text-gray-500">Director, GreenTech Industries</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding gradient-bg">
    <div class="container-custom text-center">
        <div class="max-w-3xl mx-auto text-white">
            <h2 class="text-4xl md:text-5xl font-bold mb-6">Ready to Join Our Success Stories?</h2>
            <p class="text-xl mb-8 text-gray-200">
                Let's discuss your project and create a solution that drives real results for your business
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact') }}" class="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    Start Your Project
                </a>
                <a href="tel:+917010860889" class="btn-secondary border-white text-white hover:bg-white hover:text-primary-600">
                    Call Now: +91 7010860889
                </a>
            </div>
        </div>
    </div>
</section>


@endsection
