<?php

namespace App\Services;

use App\Models\Lead;
use App\Models\LeadInteraction;
use App\Models\AnalyticsEvent;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class LeadIntelligenceService
{
    public function generateLeadInsights(Lead $lead): array
    {
        return [
            'lead_profile' => $this->buildLeadProfile($lead),
            'engagement_timeline' => $this->getEngagementTimeline($lead),
            'behavioral_patterns' => $this->analyzeBehavioralPatterns($lead),
            'intent_signals' => $this->detectIntentSignals($lead),
            'conversion_probability' => $this->calculateConversionProbability($lead),
            'optimal_contact_time' => $this->determineOptimalContactTime($lead),
            'personalization_data' => $this->getPersonalizationData($lead),
            'competitive_analysis' => $this->analyzeCompetitivePosition($lead),
            'risk_factors' => $this->identifyRiskFactors($lead),
            'opportunity_score' => $this->calculateOpportunityScore($lead),
        ];
    }

    protected function buildLeadProfile(Lead $lead): array
    {
        $profile = [
            'basic_info' => [
                'name' => $lead->name,
                'company' => $lead->company,
                'email' => $lead->email,
                'phone' => $lead->phone,
                'service_interest' => $lead->service_interest,
                'source' => $lead->source,
                'created_at' => $lead->created_at,
            ],
            'demographics' => [
                'estimated_company_size' => $this->estimateCompanySize($lead),
                'industry_category' => $this->categorizeIndustry($lead),
                'geographic_region' => $this->determineGeographicRegion($lead),
                'business_maturity' => $this->assessBusinessMaturity($lead),
            ],
            'digital_footprint' => [
                'website_visits' => $this->getWebsiteVisitCount($lead),
                'pages_viewed' => $this->getUniquePageViews($lead),
                'session_count' => $this->getSessionCount($lead),
                'total_time_on_site' => $this->getTotalTimeOnSite($lead),
                'device_preferences' => $this->getDevicePreferences($lead),
            ],
            'communication_preferences' => [
                'preferred_channel' => $this->identifyPreferredChannel($lead),
                'response_patterns' => $this->analyzeResponsePatterns($lead),
                'engagement_level' => $this->calculateEngagementLevel($lead),
            ],
        ];

        return $profile;
    }

    protected function getEngagementTimeline(Lead $lead): array
    {
        $timeline = [];

        // Get all interactions
        $interactions = $lead->interactions()
            ->orderBy('created_at')
            ->get();

        // Get website events
        $websiteEvents = [];
        if ($lead->visitor_id) {
            $websiteEvents = AnalyticsEvent::where('visitor_id', $lead->visitor_id)
                ->orderBy('created_at')
                ->get();
        }

        // Merge and sort all events
        $allEvents = collect();

        foreach ($interactions as $interaction) {
            $allEvents->push([
                'type' => 'interaction',
                'event' => $interaction->type,
                'description' => $interaction->description,
                'channel' => $interaction->channel,
                'timestamp' => $interaction->created_at,
                'metadata' => $interaction->metadata,
            ]);
        }

        foreach ($websiteEvents as $event) {
            $allEvents->push([
                'type' => 'website',
                'event' => $event->event_name,
                'description' => "Visited: {$event->page_url}",
                'channel' => 'website',
                'timestamp' => $event->created_at,
                'metadata' => [
                    'page_url' => $event->page_url,
                    'utm_source' => $event->utm_source,
                    'device_type' => $event->device_type,
                ],
            ]);
        }

        return $allEvents->sortBy('timestamp')->values()->toArray();
    }

    protected function analyzeBehavioralPatterns(Lead $lead): array
    {
        return [
            'activity_patterns' => $this->getActivityPatterns($lead),
            'content_preferences' => $this->analyzeContentPreferences($lead),
            'engagement_frequency' => $this->calculateEngagementFrequency($lead),
            'session_behavior' => $this->analyzeSessionBehavior($lead),
            'conversion_path' => $this->mapConversionPath($lead),
        ];
    }

    protected function detectIntentSignals(Lead $lead): array
    {
        $signals = [
            'high_intent' => [],
            'medium_intent' => [],
            'low_intent' => [],
        ];

        // High intent signals
        if ($this->hasQuoteRequests($lead)) {
            $signals['high_intent'][] = 'Quote request submitted';
        }

        if ($this->hasPricingPageViews($lead)) {
            $signals['high_intent'][] = 'Multiple pricing page views';
        }

        if ($this->hasDirectContactAttempts($lead)) {
            $signals['high_intent'][] = 'Direct contact attempts';
        }

        // Medium intent signals
        if ($this->hasServicePageViews($lead)) {
            $signals['medium_intent'][] = 'Service page exploration';
        }

        if ($this->hasPortfolioViews($lead)) {
            $signals['medium_intent'][] = 'Portfolio/case study views';
        }

        if ($this->hasEmailEngagement($lead)) {
            $signals['medium_intent'][] = 'Email engagement';
        }

        // Low intent signals
        if ($this->hasBlogViews($lead)) {
            $signals['low_intent'][] = 'Blog content consumption';
        }

        if ($this->hasSocialMediaActivity($lead)) {
            $signals['low_intent'][] = 'Social media interaction';
        }

        return $signals;
    }

    protected function calculateConversionProbability(Lead $lead): array
    {
        $factors = [
            'score_factor' => min(1.0, $lead->score / 100),
            'engagement_factor' => $this->calculateEngagementFactor($lead),
            'intent_factor' => $this->calculateIntentFactor($lead),
            'timing_factor' => $this->calculateTimingFactor($lead),
            'fit_factor' => $this->calculateFitFactor($lead),
        ];

        $weights = [
            'score_factor' => 0.3,
            'engagement_factor' => 0.25,
            'intent_factor' => 0.25,
            'timing_factor' => 0.1,
            'fit_factor' => 0.1,
        ];

        $probability = 0;
        foreach ($factors as $factor => $value) {
            $probability += $value * $weights[$factor];
        }

        return [
            'probability_percentage' => round($probability * 100, 2),
            'confidence_level' => $this->determineConfidenceLevel($probability),
            'contributing_factors' => $factors,
            'key_indicators' => $this->getKeyConversionIndicators($lead),
        ];
    }

    protected function determineOptimalContactTime(Lead $lead): array
    {
        $interactions = $lead->interactions()
            ->where('created_at', '>=', now()->subDays(30))
            ->get();

        $hourlyActivity = [];
        $dailyActivity = [];

        foreach ($interactions as $interaction) {
            $hour = $interaction->created_at->hour;
            $day = $interaction->created_at->dayOfWeek;

            $hourlyActivity[$hour] = ($hourlyActivity[$hour] ?? 0) + 1;
            $dailyActivity[$day] = ($dailyActivity[$day] ?? 0) + 1;
        }

        $optimalHour = !empty($hourlyActivity) ? array_keys($hourlyActivity, max($hourlyActivity))[0] : 10;
        $optimalDay = !empty($dailyActivity) ? array_keys($dailyActivity, max($dailyActivity))[0] : 2; // Tuesday

        return [
            'optimal_hour' => $optimalHour,
            'optimal_day' => $optimalDay,
            'timezone' => 'Asia/Kolkata',
            'next_optimal_time' => $this->calculateNextOptimalTime($optimalHour, $optimalDay),
            'activity_patterns' => [
                'hourly' => $hourlyActivity,
                'daily' => $dailyActivity,
            ],
        ];
    }

    protected function getPersonalizationData(Lead $lead): array
    {
        return [
            'service_focus' => $this->determineServiceFocus($lead),
            'communication_style' => $this->inferCommunicationStyle($lead),
            'decision_making_stage' => $this->assessDecisionMakingStage($lead),
            'pain_points' => $this->identifyPainPoints($lead),
            'value_propositions' => $this->suggestValuePropositions($lead),
            'content_recommendations' => $this->recommendContent($lead),
        ];
    }

    protected function analyzeCompetitivePosition(Lead $lead): array
    {
        return [
            'competitive_research' => $this->detectCompetitiveResearch($lead),
            'market_position' => $this->assessMarketPosition($lead),
            'differentiation_opportunities' => $this->identifyDifferentiationOpportunities($lead),
            'competitive_advantages' => $this->highlightCompetitiveAdvantages($lead),
        ];
    }

    protected function identifyRiskFactors(Lead $lead): array
    {
        $risks = [];

        // Low engagement risk
        if ($this->calculateEngagementLevel($lead) < 0.3) {
            $risks[] = [
                'type' => 'low_engagement',
                'severity' => 'medium',
                'description' => 'Lead shows low engagement across channels',
                'mitigation' => 'Increase personalized outreach and value-driven content',
            ];
        }

        // Stale lead risk
        $daysSinceLastInteraction = $lead->interactions()->latest()->first()?->created_at?->diffInDays(now()) ?? 999;
        if ($daysSinceLastInteraction > 14) {
            $risks[] = [
                'type' => 'stale_lead',
                'severity' => 'high',
                'description' => 'No recent interaction for over 2 weeks',
                'mitigation' => 'Immediate re-engagement campaign required',
            ];
        }

        // Budget mismatch risk
        if ($this->detectBudgetMismatch($lead)) {
            $risks[] = [
                'type' => 'budget_mismatch',
                'severity' => 'medium',
                'description' => 'Potential budget constraints detected',
                'mitigation' => 'Present flexible pricing options and ROI justification',
            ];
        }

        return $risks;
    }

    protected function calculateOpportunityScore(Lead $lead): array
    {
        $factors = [
            'revenue_potential' => $this->estimateRevenuePotential($lead),
            'strategic_value' => $this->assessStrategicValue($lead),
            'conversion_likelihood' => $this->calculateConversionProbability($lead)['probability_percentage'] / 100,
            'timeline_urgency' => $this->assessTimelineUrgency($lead),
            'relationship_potential' => $this->evaluateRelationshipPotential($lead),
        ];

        $opportunityScore = array_sum($factors) / count($factors) * 100;

        return [
            'opportunity_score' => round($opportunityScore, 2),
            'opportunity_grade' => $this->gradeOpportunity($opportunityScore),
            'contributing_factors' => $factors,
            'investment_recommendation' => $this->recommendInvestmentLevel($opportunityScore),
        ];
    }

    // Helper methods for various calculations
    protected function estimateCompanySize(Lead $lead): string
    {
        $company = strtolower($lead->company ?? '');
        
        if (str_contains($company, 'ltd') || str_contains($company, 'corporation') || str_contains($company, 'pvt')) {
            return 'large';
        } elseif (str_contains($company, 'solutions') || str_contains($company, 'technologies')) {
            return 'medium';
        }
        
        return 'small';
    }

    protected function categorizeIndustry(Lead $lead): string
    {
        $company = strtolower($lead->company ?? '');
        $service = strtolower($lead->service_interest ?? '');
        
        if (str_contains($company, 'tech') || str_contains($service, 'software')) {
            return 'technology';
        } elseif (str_contains($company, 'retail') || str_contains($service, 'ecommerce')) {
            return 'retail';
        } elseif (str_contains($company, 'health') || str_contains($company, 'medical')) {
            return 'healthcare';
        }
        
        return 'general';
    }

    protected function determineGeographicRegion(Lead $lead): string
    {
        $location = strtolower($lead->city ?? $lead->state ?? '');
        
        if (str_contains($location, 'salem') || str_contains($location, 'coimbatore')) {
            return 'local';
        } elseif (str_contains($location, 'chennai') || str_contains($location, 'bangalore')) {
            return 'metro';
        } elseif (str_contains($location, 'tamil nadu')) {
            return 'state';
        }
        
        return 'national';
    }

    protected function getWebsiteVisitCount(Lead $lead): int
    {
        if (!$lead->visitor_id) return 0;
        
        return AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->where('event_name', 'page_view')
            ->count();
    }

    protected function getUniquePageViews(Lead $lead): int
    {
        if (!$lead->visitor_id) return 0;
        
        return AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->where('event_name', 'page_view')
            ->distinct('page_url')
            ->count();
    }

    protected function getSessionCount(Lead $lead): int
    {
        if (!$lead->visitor_id) return 0;
        
        return AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->distinct('session_id')
            ->count();
    }

    protected function getTotalTimeOnSite(Lead $lead): int
    {
        if (!$lead->visitor_id) return 0;
        
        return AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->sum('session_duration') ?? 0;
    }

    protected function getDevicePreferences(Lead $lead): array
    {
        if (!$lead->visitor_id) return [];
        
        return AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->select('device_type', DB::raw('count(*) as count'))
            ->groupBy('device_type')
            ->pluck('count', 'device_type')
            ->toArray();
    }

    protected function identifyPreferredChannel(Lead $lead): string
    {
        $channels = $lead->interactions()
            ->select('channel', DB::raw('count(*) as count'))
            ->groupBy('channel')
            ->pluck('count', 'channel')
            ->toArray();

        return !empty($channels) ? array_keys($channels, max($channels))[0] : 'email';
    }

    protected function calculateEngagementLevel(Lead $lead): float
    {
        $totalInteractions = $lead->interactions()->count();
        $websiteEvents = $lead->visitor_id ? 
            AnalyticsEvent::where('visitor_id', $lead->visitor_id)->count() : 0;
        
        $totalEngagement = $totalInteractions + ($websiteEvents * 0.1); // Weight website events less
        
        // Normalize to 0-1 scale
        return min(1.0, $totalEngagement / 20);
    }

    protected function hasQuoteRequests(Lead $lead): bool
    {
        return $lead->interactions()
            ->where('type', 'quote_request')
            ->exists();
    }

    protected function hasPricingPageViews(Lead $lead): bool
    {
        if (!$lead->visitor_id) return false;
        
        return AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->where('page_url', 'like', '%pricing%')
            ->count() >= 2;
    }

    protected function hasDirectContactAttempts(Lead $lead): bool
    {
        return $lead->interactions()
            ->whereIn('type', ['phone_call', 'email_reply', 'whatsapp_reply'])
            ->exists();
    }

    protected function hasServicePageViews(Lead $lead): bool
    {
        if (!$lead->visitor_id) return false;
        
        return AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->where('page_url', 'like', '%/services/%')
            ->exists();
    }

    protected function hasPortfolioViews(Lead $lead): bool
    {
        if (!$lead->visitor_id) return false;
        
        return AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->where('page_url', 'like', '%portfolio%')
            ->orWhere('page_url', 'like', '%case-study%')
            ->exists();
    }

    protected function hasEmailEngagement(Lead $lead): bool
    {
        return $lead->interactions()
            ->where('channel', 'email')
            ->whereIn('type', ['email_open', 'email_click'])
            ->exists();
    }

    protected function hasBlogViews(Lead $lead): bool
    {
        if (!$lead->visitor_id) return false;
        
        return AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->where('page_url', 'like', '%blog%')
            ->exists();
    }

    protected function hasSocialMediaActivity(Lead $lead): bool
    {
        return $lead->interactions()
            ->where('channel', 'social_media')
            ->exists();
    }

    protected function calculateEngagementFactor(Lead $lead): float
    {
        return $this->calculateEngagementLevel($lead);
    }

    protected function calculateIntentFactor(Lead $lead): float
    {
        $intentScore = 0;
        
        if ($this->hasQuoteRequests($lead)) $intentScore += 0.4;
        if ($this->hasPricingPageViews($lead)) $intentScore += 0.3;
        if ($this->hasDirectContactAttempts($lead)) $intentScore += 0.2;
        if ($this->hasServicePageViews($lead)) $intentScore += 0.1;
        
        return min(1.0, $intentScore);
    }

    protected function calculateTimingFactor(Lead $lead): float
    {
        $daysSinceCreated = $lead->created_at->diffInDays(now());
        
        // Optimal timing is 1-7 days
        if ($daysSinceCreated <= 7) return 1.0;
        if ($daysSinceCreated <= 14) return 0.8;
        if ($daysSinceCreated <= 30) return 0.6;
        return 0.4;
    }

    protected function calculateFitFactor(Lead $lead): float
    {
        $fitScore = 0;
        
        // Company size fit
        $companySize = $this->estimateCompanySize($lead);
        if (in_array($companySize, ['medium', 'large'])) $fitScore += 0.4;
        
        // Industry fit
        $industry = $this->categorizeIndustry($lead);
        if ($industry === 'technology') $fitScore += 0.3;
        
        // Geographic fit
        $region = $this->determineGeographicRegion($lead);
        if (in_array($region, ['local', 'metro'])) $fitScore += 0.3;
        
        return min(1.0, $fitScore);
    }

    protected function determineConfidenceLevel(float $probability): string
    {
        if ($probability >= 0.8) return 'very_high';
        if ($probability >= 0.6) return 'high';
        if ($probability >= 0.4) return 'medium';
        if ($probability >= 0.2) return 'low';
        return 'very_low';
    }

    protected function getKeyConversionIndicators(Lead $lead): array
    {
        $indicators = [];
        
        if ($this->hasQuoteRequests($lead)) {
            $indicators[] = 'Quote request submitted';
        }
        
        if ($lead->score >= 70) {
            $indicators[] = 'High lead score';
        }
        
        if ($this->calculateEngagementLevel($lead) >= 0.7) {
            $indicators[] = 'High engagement level';
        }
        
        return $indicators;
    }

    protected function calculateNextOptimalTime(int $hour, int $day): Carbon
    {
        $nextWeek = now()->addWeek()->startOfWeek();
        return $nextWeek->addDays($day)->setHour($hour);
    }

    protected function assessBusinessMaturity(Lead $lead): string
    {
        $company = strtolower($lead->company ?? '');
        
        if (str_contains($company, 'startup') || str_contains($company, 'new')) {
            return 'startup';
        } elseif (str_contains($company, 'established') || str_contains($company, 'ltd')) {
            return 'established';
        }
        
        return 'growing';
    }

    protected function getActivityPatterns(Lead $lead): array
    {
        $interactions = $lead->interactions()
            ->where('created_at', '>=', now()->subDays(30))
            ->get();

        $patterns = [
            'most_active_hour' => null,
            'most_active_day' => null,
            'activity_frequency' => 'low',
            'engagement_trend' => 'stable',
        ];

        if ($interactions->isNotEmpty()) {
            $hourCounts = $interactions->groupBy(fn($i) => $i->created_at->hour)->map->count();
            $patterns['most_active_hour'] = $hourCounts->keys()->first();

            $dayCounts = $interactions->groupBy(fn($i) => $i->created_at->dayOfWeek)->map->count();
            $patterns['most_active_day'] = $dayCounts->keys()->first();

            $patterns['activity_frequency'] = $interactions->count() > 10 ? 'high' : 
                ($interactions->count() > 5 ? 'medium' : 'low');
        }

        return $patterns;
    }

    protected function analyzeContentPreferences(Lead $lead): array
    {
        if (!$lead->visitor_id) {
            return ['preferences' => [], 'top_content' => []];
        }

        $pageViews = AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->where('event_name', 'page_view')
            ->select('page_url', DB::raw('count(*) as views'))
            ->groupBy('page_url')
            ->orderBy('views', 'desc')
            ->limit(5)
            ->get();

        $preferences = [];
        foreach ($pageViews as $view) {
            if (str_contains($view->page_url, '/services/')) {
                $preferences[] = 'service_information';
            } elseif (str_contains($view->page_url, '/blog/')) {
                $preferences[] = 'educational_content';
            } elseif (str_contains($view->page_url, '/portfolio/')) {
                $preferences[] = 'case_studies';
            }
        }

        return [
            'preferences' => array_unique($preferences),
            'top_content' => $pageViews->pluck('page_url')->toArray(),
        ];
    }

    protected function calculateEngagementFrequency(Lead $lead): string
    {
        $interactions = $lead->interactions()
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        if ($interactions >= 15) return 'very_high';
        if ($interactions >= 10) return 'high';
        if ($interactions >= 5) return 'medium';
        if ($interactions >= 2) return 'low';
        return 'very_low';
    }

    protected function analyzeSessionBehavior(Lead $lead): array
    {
        if (!$lead->visitor_id) {
            return ['avg_session_duration' => 0, 'pages_per_session' => 0, 'bounce_rate' => 100];
        }

        $sessions = AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->select('session_id', DB::raw('count(*) as page_views'), DB::raw('avg(session_duration) as avg_duration'))
            ->groupBy('session_id')
            ->get();

        $avgDuration = $sessions->avg('avg_duration') ?? 0;
        $avgPagesPerSession = $sessions->avg('page_views') ?? 0;
        $bounceRate = $sessions->where('page_views', 1)->count() / max(1, $sessions->count()) * 100;

        return [
            'avg_session_duration' => round($avgDuration / 60, 2), // Convert to minutes
            'pages_per_session' => round($avgPagesPerSession, 1),
            'bounce_rate' => round($bounceRate, 1),
        ];
    }

    protected function mapConversionPath(Lead $lead): array
    {
        $timeline = $this->getEngagementTimeline($lead);
        
        $path = [];
        foreach ($timeline as $event) {
            if ($event['type'] === 'website' && str_contains($event['description'], 'Visited:')) {
                $path[] = [
                    'step' => 'website_visit',
                    'page' => $event['metadata']['page_url'] ?? 'unknown',
                    'timestamp' => $event['timestamp'],
                ];
            } elseif ($event['type'] === 'interaction') {
                $path[] = [
                    'step' => $event['event'],
                    'channel' => $event['channel'],
                    'timestamp' => $event['timestamp'],
                ];
            }
        }

        return array_slice($path, 0, 10); // Return first 10 steps
    }

    protected function detectBudgetMismatch(Lead $lead): bool
    {
        $companySize = $this->estimateCompanySize($lead);
        $serviceInterest = strtolower($lead->service_interest ?? '');
        
        // Small companies requesting expensive services might have budget constraints
        if ($companySize === 'small' && in_array($serviceInterest, ['mobile-development', 'enterprise-software'])) {
            return true;
        }
        
        return false;
    }

    protected function estimateRevenuePotential(Lead $lead): float
    {
        $baseValue = match ($lead->service_interest) {
            'web-development' => 0.5,
            'mobile-development' => 1.0,
            'digital-marketing' => 0.3,
            'graphic-design' => 0.2,
            default => 0.4,
        };

        $companySize = $this->estimateCompanySize($lead);
        $sizeMultiplier = match ($companySize) {
            'large' => 1.5,
            'medium' => 1.0,
            'small' => 0.7,
            default => 0.5,
        };

        return min(1.0, $baseValue * $sizeMultiplier);
    }

    protected function assessStrategicValue(Lead $lead): float
    {
        $value = 0;
        
        // Industry strategic value
        $industry = $this->categorizeIndustry($lead);
        if ($industry === 'technology') $value += 0.4;
        
        // Geographic strategic value
        $region = $this->determineGeographicRegion($lead);
        if ($region === 'local') $value += 0.3;
        
        // Referral potential
        $companySize = $this->estimateCompanySize($lead);
        if ($companySize === 'large') $value += 0.3;
        
        return min(1.0, $value);
    }

    protected function assessTimelineUrgency(Lead $lead): float
    {
        $urgencyKeywords = ['urgent', 'asap', 'immediately', 'rush', 'deadline'];
        $description = strtolower($lead->message ?? '');
        
        foreach ($urgencyKeywords as $keyword) {
            if (str_contains($description, $keyword)) {
                return 1.0;
            }
        }
        
        // Check for recent high-intent activities
        $recentQuoteRequests = $lead->interactions()
            ->where('type', 'quote_request')
            ->where('created_at', '>=', now()->subDays(7))
            ->exists();
            
        return $recentQuoteRequests ? 0.8 : 0.4;
    }

    protected function evaluateRelationshipPotential(Lead $lead): float
    {
        $potential = 0;
        
        // Engagement consistency
        $engagementLevel = $this->calculateEngagementLevel($lead);
        $potential += $engagementLevel * 0.4;
        
        // Response quality
        $responseSpeed = $this->analyzeResponsePatterns($lead)['avg_response_time'] ?? 24;
        $potential += ($responseSpeed <= 4 ? 0.3 : 0.1);
        
        // Communication style
        $communicationStyle = $this->inferCommunicationStyle($lead);
        $potential += ($communicationStyle === 'professional' ? 0.3 : 0.2);
        
        return min(1.0, $potential);
    }

    protected function gradeOpportunity(float $score): string
    {
        if ($score >= 80) return 'A+';
        if ($score >= 70) return 'A';
        if ($score >= 60) return 'B+';
        if ($score >= 50) return 'B';
        if ($score >= 40) return 'C';
        return 'D';
    }

    protected function recommendInvestmentLevel(float $score): string
    {
        if ($score >= 70) return 'high';
        if ($score >= 50) return 'medium';
        if ($score >= 30) return 'low';
        return 'minimal';
    }

    protected function analyzeResponsePatterns(Lead $lead): array
    {
        $interactions = $lead->interactions()
            ->orderBy('created_at')
            ->get();

        if ($interactions->count() < 2) {
            return ['avg_response_time' => null, 'consistency' => 'insufficient_data'];
        }

        $responseTimes = [];
        for ($i = 1; $i < $interactions->count(); $i++) {
            $timeDiff = $interactions[$i]->created_at->diffInHours($interactions[$i-1]->created_at);
            $responseTimes[] = $timeDiff;
        }

        $avgResponseTime = array_sum($responseTimes) / count($responseTimes);
        $consistency = (max($responseTimes) - min($responseTimes)) <= 24 ? 'consistent' : 'variable';

        return [
            'avg_response_time' => round($avgResponseTime, 1),
            'consistency' => $consistency,
            'response_pattern' => $avgResponseTime <= 4 ? 'fast' : ($avgResponseTime <= 24 ? 'moderate' : 'slow'),
        ];
    }

    protected function inferCommunicationStyle(Lead $lead): string
    {
        $interactions = $lead->interactions()
            ->whereNotNull('description')
            ->get();

        $professionalKeywords = ['please', 'thank you', 'regards', 'sincerely', 'appreciate'];
        $casualKeywords = ['hi', 'hey', 'thanks', 'cool', 'awesome'];

        $professionalCount = 0;
        $casualCount = 0;

        foreach ($interactions as $interaction) {
            $description = strtolower($interaction->description);
            
            foreach ($professionalKeywords as $keyword) {
                if (str_contains($description, $keyword)) {
                    $professionalCount++;
                    break;
                }
            }
            
            foreach ($casualKeywords as $keyword) {
                if (str_contains($description, $keyword)) {
                    $casualCount++;
                    break;
                }
            }
        }

        if ($professionalCount > $casualCount) return 'professional';
        if ($casualCount > $professionalCount) return 'casual';
        return 'neutral';
    }

    protected function assessDecisionMakingStage(Lead $lead): string
    {
        if ($this->hasQuoteRequests($lead)) return 'decision';
        if ($this->hasPricingPageViews($lead)) return 'evaluation';
        if ($this->hasServicePageViews($lead)) return 'consideration';
        return 'awareness';
    }

    protected function identifyPainPoints(Lead $lead): array
    {
        $painPoints = [];
        $serviceInterest = strtolower($lead->service_interest ?? '');
        
        switch ($serviceInterest) {
            case 'web-development':
                $painPoints = ['outdated_website', 'poor_user_experience', 'low_conversion_rates'];
                break;
            case 'mobile-development':
                $painPoints = ['no_mobile_presence', 'poor_app_performance', 'user_retention_issues'];
                break;
            case 'digital-marketing':
                $painPoints = ['low_online_visibility', 'poor_lead_generation', 'ineffective_campaigns'];
                break;
            case 'graphic-design':
                $painPoints = ['inconsistent_branding', 'poor_visual_identity', 'unprofessional_materials'];
                break;
            default:
                $painPoints = ['digital_transformation_needs', 'competitive_disadvantage'];
        }
        
        return $painPoints;
    }

    protected function suggestValuePropositions(Lead $lead): array
    {
        $serviceInterest = strtolower($lead->service_interest ?? '');
        $companySize = $this->estimateCompanySize($lead);
        
        $baseProps = [
            'web-development' => ['responsive_design', 'seo_optimization', 'fast_loading'],
            'mobile-development' => ['cross_platform', 'native_performance', 'app_store_optimization'],
            'digital-marketing' => ['roi_focused', 'data_driven', 'multi_channel'],
            'graphic-design' => ['brand_consistency', 'professional_quality', 'quick_turnaround'],
        ];
        
        $props = $baseProps[$serviceInterest] ?? ['quality_delivery', 'competitive_pricing'];
        
        if ($companySize === 'small') {
            $props[] = 'affordable_packages';
        } elseif ($companySize === 'large') {
            $props[] = 'enterprise_solutions';
        }
        
        return $props;
    }

    protected function recommendContent(Lead $lead): array
    {
        $stage = $this->assessDecisionMakingStage($lead);
        $serviceInterest = $lead->service_interest;
        
        $recommendations = [];
        
        switch ($stage) {
            case 'awareness':
                $recommendations = [
                    "Introduction to {$serviceInterest} services",
                    'Industry trends and insights',
                    'Educational blog posts',
                ];
                break;
            case 'consideration':
                $recommendations = [
                    "{$serviceInterest} case studies",
                    'Service comparison guides',
                    'Client testimonials',
                ];
                break;
            case 'evaluation':
                $recommendations = [
                    'Detailed service proposals',
                    'Pricing information',
                    'ROI calculators',
                ];
                break;
            case 'decision':
                $recommendations = [
                    'Custom quotes',
                    'Implementation timelines',
                    'Contract terms',
                ];
                break;
        }
        
        return $recommendations;
    }

    protected function detectCompetitiveResearch(Lead $lead): array
    {
        // This would analyze if the lead is researching competitors
        // For now, return sample data
        return [
            'competitor_research_detected' => false,
            'competitors_mentioned' => [],
            'comparison_points' => [],
        ];
    }

    protected function assessMarketPosition(Lead $lead): string
    {
        $companySize = $this->estimateCompanySize($lead);
        $industry = $this->categorizeIndustry($lead);
        
        if ($companySize === 'large' && $industry === 'technology') {
            return 'market_leader';
        } elseif ($companySize === 'medium') {
            return 'growing_player';
        }
        
        return 'emerging_business';
    }

    protected function identifyDifferentiationOpportunities(Lead $lead): array
    {
        return [
            'local_expertise',
            'personalized_service',
            'competitive_pricing',
            'quick_turnaround',
            'ongoing_support',
        ];
    }

    protected function highlightCompetitiveAdvantages(Lead $lead): array
    {
        return [
            'proven_track_record',
            'local_market_knowledge',
            'comprehensive_services',
            'client_focused_approach',
            'innovative_solutions',
        ];
    }
}
