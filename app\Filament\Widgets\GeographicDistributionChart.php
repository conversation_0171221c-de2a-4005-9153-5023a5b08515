<?php

namespace App\Filament\Widgets;

use App\Models\Contact;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class GeographicDistributionChart extends ChartWidget
{
    protected static ?string $heading = 'Geographic Distribution of Contacts';
    
    protected static ?int $sort = 2;
    
    protected function getData(): array
    {
        // Get state-wise distribution
        $stateData = Contact::select('state', DB::raw('COUNT(*) as count'))
            ->whereNotNull('state')
            ->where('state', '!=', '')
            ->groupBy('state')
            ->orderByDesc('count')
            ->limit(10)
            ->get();
        
        $labels = [];
        $data = [];
        $colors = [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
            '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
        ];
        
        foreach ($stateData as $index => $item) {
            $labels[] = $item->state;
            $data[] = $item->count;
        }
        
        return [
            'datasets' => [
                [
                    'label' => 'Contacts by State',
                    'data' => $data,
                    'backgroundColor' => array_slice($colors, 0, count($data)),
                    'borderColor' => array_slice($colors, 0, count($data)),
                    'borderWidth' => 1,
                ],
            ],
            'labels' => $labels,
        ];
    }
    
    protected function getType(): string
    {
        return 'doughnut';
    }
    
    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            return context.label + ": " + context.parsed.toLocaleString() + " contacts";
                        }'
                    ]
                ]
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
