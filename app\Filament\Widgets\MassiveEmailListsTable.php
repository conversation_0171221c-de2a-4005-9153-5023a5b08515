<?php

namespace App\Filament\Widgets;

use App\Models\ContactList;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Database\Eloquent\Builder;

class MassiveEmailListsTable extends BaseWidget
{
    protected static ?string $heading = 'Massive Email Lists (1 Lakh+ Contacts)';
    
    protected static ?int $sort = 4;
    
    protected int | string | array $columnSpan = 'full';
    
    public function table(Table $table): Table
    {
        return $table
            ->query(
                ContactList::query()
                    ->whereIn('id', function ($query) {
                        $query->select('contact_list_id')
                            ->from('contact_list_members')
                            ->groupBy('contact_list_id')
                            ->havingRaw('COUNT(*) >= 100000');
                    })
                    ->withCount('contacts')
                    ->orderByDesc('contacts_count')
            )
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                    
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'static' => 'info',
                        'dynamic' => 'success',
                        default => 'gray',
                    }),
                    
                Tables\Columns\TextColumn::make('contacts_count')
                    ->label('Total Contacts')
                    ->numeric()
                    ->sortable()
                    ->formatStateUsing(fn (int $state): string => number_format($state))
                    ->badge()
                    ->color(fn (int $state): string => match (true) {
                        $state >= 10000000 => 'success', // 1 crore+
                        $state >= 1000000 => 'warning',  // 10 lakh+
                        $state >= 100000 => 'info',      // 1 lakh+
                        default => 'gray',
                    }),
                    
                Tables\Columns\TextColumn::make('subscribed_count')
                    ->label('Subscribed')
                    ->getStateUsing(fn (ContactList $record): int => 
                        $record->contacts()->wherePivot('status', 'subscribed')->count()
                    )
                    ->numeric()
                    ->formatStateUsing(fn (int $state): string => number_format($state)),
                    
                Tables\Columns\TextColumn::make('geographic_spread')
                    ->label('Geographic Spread')
                    ->getStateUsing(function (ContactList $record): string {
                        $stateCount = $record->contacts()
                            ->distinct('state')
                            ->whereNotNull('state')
                            ->count();
                        
                        $cityCount = $record->contacts()
                            ->distinct('city')
                            ->whereNotNull('city')
                            ->count();
                        
                        return "{$stateCount} states, {$cityCount} cities";
                    }),
                    
                Tables\Columns\TextColumn::make('engagement_rate')
                    ->label('Engagement %')
                    ->getStateUsing(function (ContactList $record): string {
                        $total = $record->contacts()->count();
                        if ($total === 0) return '0%';
                        
                        $engaged = $record->contacts()
                            ->where('engagement_score', '>=', 70)
                            ->count();
                        
                        return number_format(($engaged / $total) * 100, 1) . '%';
                    })
                    ->badge()
                    ->color(fn (string $state): string => 
                        match (true) {
                            ((float) str_replace('%', '', $state)) >= 70 => 'success',
                            ((float) str_replace('%', '', $state)) >= 40 => 'warning',
                            default => 'danger',
                        }
                    ),
                    
                Tables\Columns\TextColumn::make('estimated_send_time')
                    ->label('Est. Send Time')
                    ->getStateUsing(function (ContactList $record): string {
                        $totalContacts = $record->contacts()->count();
                        $dailyCapacity = 2000; // Assuming 4 accounts × 500 emails
                        
                        if ($totalContacts === 0 || $dailyCapacity === 0) {
                            return 'N/A';
                        }
                        
                        $days = ceil($totalContacts / $dailyCapacity);
                        
                        if ($days <= 30) {
                            return $days . ' days';
                        } elseif ($days <= 365) {
                            $months = ceil($days / 30);
                            return $months . ' months';
                        } else {
                            $years = round($days / 365, 1);
                            return $years . ' years';
                        }
                    })
                    ->badge()
                    ->color(fn (string $state): string => 
                        match (true) {
                            str_contains($state, 'days') => 'success',
                            str_contains($state, 'months') => 'warning',
                            str_contains($state, 'years') => 'danger',
                            default => 'gray',
                        }
                    ),
                    
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(),
            ])
            ->actions([
                Tables\Actions\Action::make('view_details')
                    ->icon('heroicon-o-eye')
                    ->url(fn (ContactList $record): string => "/admin/email-lists/{$record->id}"),
                    
                Tables\Actions\Action::make('export')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function (ContactList $record) {
                        // This would trigger the export functionality
                        return redirect()->route('filament.admin.resources.email-lists.export', $record);
                    }),
            ])
            ->defaultSort('contacts_count', 'desc')
            ->paginated([10, 25, 50]);
    }
}
