<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContactListResource\Pages;
use App\Models\ContactList;
use App\Models\Contact;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;

class ContactListResource extends Resource
{
    protected static ?string $model = ContactList::class;
    protected static ?string $navigationIcon = 'heroicon-o-list-bullet';
    protected static ?string $navigationGroup = 'Contact Management';
    protected static ?string $navigationLabel = 'Contact Lists';
    protected static ?int $navigationSort = 2;
    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('List Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(1000)
                            ->rows(3),
                        Forms\Components\Select::make('type')
                            ->options([
                                'static' => 'Static List',
                                'dynamic' => 'Dynamic List (Smart Segments)',
                            ])
                            ->default('static')
                            ->required()
                            ->reactive(),
                        Forms\Components\Select::make('status')
                            ->options([
                                'active' => 'Active',
                                'inactive' => 'Inactive',
                                'archived' => 'Archived',
                            ])
                            ->default('active')
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('Smart Segment Rules')
                    ->schema([
                        Forms\Components\Repeater::make('segment_rules')
                            ->schema([
                                Forms\Components\Select::make('field')
                                    ->options([
                                        'status' => 'Status',
                                        'source' => 'Source',
                                        'engagement_score' => 'Engagement Score',
                                        'last_opened_at' => 'Last Opened',
                                        'last_clicked_at' => 'Last Clicked',
                                        'city' => 'City',
                                        'state' => 'State',
                                        'country' => 'Country',
                                        'company' => 'Company',
                                        'tags' => 'Tags',
                                    ])
                                    ->required(),
                                Forms\Components\Select::make('operator')
                                    ->options([
                                        'equals' => 'Equals',
                                        'not_equals' => 'Not Equals',
                                        'contains' => 'Contains',
                                        'not_contains' => 'Does Not Contain',
                                        'greater_than' => 'Greater Than',
                                        'less_than' => 'Less Than',
                                        'is_null' => 'Is Empty',
                                        'is_not_null' => 'Is Not Empty',
                                        'in_last_days' => 'In Last X Days',
                                    ])
                                    ->required(),
                                Forms\Components\TextInput::make('value')
                                    ->label('Value'),
                            ])
                            ->columns(3)
                            ->addActionLabel('Add Rule')
                            ->collapsible(),
                    ])
                    ->visible(fn (Forms\Get $get): bool => $get('type') === 'dynamic'),

                Forms\Components\Section::make('List Settings')
                    ->schema([
                        Forms\Components\Toggle::make('double_opt_in')
                            ->label('Require Double Opt-in')
                            ->helperText('Contacts must confirm their subscription via email'),
                        Forms\Components\Toggle::make('auto_cleanup')
                            ->label('Auto Cleanup')
                            ->helperText('Automatically remove bounced and unsubscribed contacts'),
                        Forms\Components\Select::make('cleanup_frequency')
                            ->label('Cleanup Frequency')
                            ->options([
                                'daily' => 'Daily',
                                'weekly' => 'Weekly',
                                'monthly' => 'Monthly',
                            ])
                            ->default('weekly')
                            ->visible(fn (Forms\Get $get): bool => $get('auto_cleanup')),
                        Forms\Components\TagsInput::make('tags')
                            ->placeholder('Add tags for organization...'),
                    ])->columns(2),

                Forms\Components\Section::make('Manual Contact Selection')
                    ->schema([
                        Forms\Components\Select::make('contacts')
                            ->relationship('contacts', 'email')
                            ->multiple()
                            ->preload()
                            ->searchable(['first_name', 'last_name', 'email', 'company'])
                            ->getOptionLabelFromRecordUsing(fn (Contact $record): string => 
                                "{$record->full_name} ({$record->email})" . 
                                ($record->company ? " - {$record->company}" : '')
                            ),
                    ])
                    ->visible(fn (Forms\Get $get): bool => $get('type') === 'static'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('description')
                    ->limit(50)
                    ->toggleable(),
                Tables\Columns\BadgeColumn::make('type')
                    ->colors([
                        'primary' => 'static',
                        'success' => 'dynamic',
                    ]),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'success' => 'active',
                        'warning' => 'inactive',
                        'secondary' => 'archived',
                    ]),
                Tables\Columns\TextColumn::make('contacts_count')
                    ->label('Contacts')
                    ->counts('contacts')
                    ->sortable()
                    ->badge()
                    ->color('primary'),
                Tables\Columns\TextColumn::make('subscribed_contacts_count')
                    ->label('Subscribed')
                    ->getStateUsing(fn (ContactList $record): int => 
                        $record->contacts()->where('status', 'subscribed')->count()
                    )
                    ->badge()
                    ->color('success'),
                Tables\Columns\IconColumn::make('double_opt_in')
                    ->boolean()
                    ->toggleable(),
                Tables\Columns\IconColumn::make('auto_cleanup')
                    ->boolean()
                    ->toggleable(),
                Tables\Columns\TagsColumn::make('tags')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'static' => 'Static',
                        'dynamic' => 'Dynamic',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'archived' => 'Archived',
                    ]),
                Tables\Filters\Filter::make('has_contacts')
                    ->query(fn (Builder $query): Builder => $query->has('contacts'))
                    ->label('Has Contacts'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('refresh_dynamic')
                    ->label('Refresh')
                    ->icon('heroicon-o-arrow-path')
                    ->color('primary')
                    ->visible(fn (ContactList $record): bool => $record->type === 'dynamic')
                    ->action(function (ContactList $record): void {
                        // Refresh dynamic list logic would go here
                        Notification::make()
                            ->title('List refreshed successfully')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('export')
                    ->label('Export')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('secondary')
                    ->action(function (ContactList $record): void {
                        // Export logic would go here
                        Notification::make()
                            ->title('Export started')
                            ->body('You will receive an email when the export is ready.')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('activate')
                        ->label('Activate')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function (Collection $records): void {
                            $records->each->update(['status' => 'active']);
                            Notification::make()
                                ->title('Lists activated successfully')
                                ->success()
                                ->send();
                        }),
                    BulkAction::make('deactivate')
                        ->label('Deactivate')
                        ->icon('heroicon-o-x-mark')
                        ->color('warning')
                        ->action(function (Collection $records): void {
                            $records->each->update(['status' => 'inactive']);
                            Notification::make()
                                ->title('Lists deactivated successfully')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('List Overview')
                    ->schema([
                        Infolists\Components\TextEntry::make('name'),
                        Infolists\Components\TextEntry::make('description'),
                        Infolists\Components\TextEntry::make('type')
                            ->badge(),
                        Infolists\Components\TextEntry::make('status')
                            ->badge(),
                        Infolists\Components\TextEntry::make('contacts_count')
                            ->label('Total Contacts'),
                        Infolists\Components\TextEntry::make('subscribed_count')
                            ->label('Subscribed Contacts')
                            ->getStateUsing(fn (ContactList $record): int => 
                                $record->contacts()->where('status', 'subscribed')->count()
                            ),
                    ])->columns(3),

                Infolists\Components\Section::make('Settings')
                    ->schema([
                        Infolists\Components\IconEntry::make('double_opt_in')
                            ->boolean(),
                        Infolists\Components\IconEntry::make('auto_cleanup')
                            ->boolean(),
                        Infolists\Components\TextEntry::make('cleanup_frequency')
                            ->visible(fn (ContactList $record): bool => $record->auto_cleanup),
                        Infolists\Components\TextEntry::make('tags')
                            ->badge()
                            ->separator(',')
                            ->formatStateUsing(fn ($state) => is_array($state) ? $state : []),
                    ])->columns(2),

                Infolists\Components\Section::make('Segment Rules')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('segment_rules')
                            ->schema([
                                Infolists\Components\TextEntry::make('field'),
                                Infolists\Components\TextEntry::make('operator'),
                                Infolists\Components\TextEntry::make('value'),
                            ])
                            ->columns(3)
                            ->formatStateUsing(fn ($state) => is_array($state) ? $state : []),
                    ])
                    ->visible(fn (ContactList $record): bool => $record->type === 'dynamic'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContactLists::route('/'),
            'create' => Pages\CreateContactList::route('/create'),
            'view' => Pages\ViewContactList::route('/{record}'),
            'edit' => Pages\EditContactList::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', 'active')->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'primary';
    }
}
