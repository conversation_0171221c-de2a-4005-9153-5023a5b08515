<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Lead;
use App\Models\EmailCampaign;
use App\Models\WhatsAppCampaign;
use App\Models\SocialMediaPost;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class ApiIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $admin;
    protected User $customer;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);
        
        $this->customer = User::factory()->create([
            'role' => 'customer',
            'email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function it_can_authenticate_users_via_api()
    {
        // Test login
        $response = $this->postJson('/api/auth/login', [
            'email' => $this->admin->email,
            'password' => 'password',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'user' => [
                'id',
                'name',
                'email',
                'role',
            ],
            'token',
        ]);

        $token = $response->json('token');
        $this->assertNotEmpty($token);

        // Test authenticated request
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/user');

        $response->assertStatus(200);
        $response->assertJson([
            'email' => $this->admin->email,
        ]);
    }

    /** @test */
    public function it_can_manage_leads_via_api()
    {
        Sanctum::actingAs($this->admin);

        // Test creating a lead
        $leadData = [
            'name' => 'API Test Lead',
            'email' => '<EMAIL>',
            'phone' => '+919876543210',
            'company' => 'API Corp',
            'source' => 'api',
            'service_interest' => 'web_development',
            'message' => 'API test message',
        ];

        $response = $this->postJson('/api/leads', $leadData);
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'email',
                'score',
                'status',
            ]
        ]);

        $leadId = $response->json('data.id');

        // Test retrieving leads
        $response = $this->getJson('/api/leads');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'email',
                    'status',
                    'score',
                ]
            ],
            'meta' => [
                'current_page',
                'total',
            ]
        ]);

        // Test retrieving single lead
        $response = $this->getJson("/api/leads/{$leadId}");
        $response->assertStatus(200);
        $response->assertJson([
            'data' => [
                'name' => 'API Test Lead',
                'email' => '<EMAIL>',
            ]
        ]);

        // Test updating lead
        $response = $this->putJson("/api/leads/{$leadId}", [
            'status' => 'contacted',
            'notes' => 'Updated via API',
        ]);
        $response->assertStatus(200);
        $response->assertJson([
            'data' => [
                'status' => 'contacted',
            ]
        ]);

        // Test lead scoring endpoint
        $response = $this->postJson("/api/leads/{$leadId}/score");
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'score',
            'breakdown' => [
                'source_score',
                'service_score',
                'company_score',
                'engagement_score',
            ]
        ]);
    }

    /** @test */
    public function it_can_manage_email_campaigns_via_api()
    {
        Sanctum::actingAs($this->admin);

        // Test creating email campaign
        $campaignData = [
            'name' => 'API Test Campaign',
            'subject' => 'Test Subject',
            'content' => '<h1>Test Content</h1>',
            'type' => 'newsletter',
        ];

        $response = $this->postJson('/api/email-campaigns', $campaignData);
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'subject',
                'status',
            ]
        ]);

        $campaignId = $response->json('data.id');

        // Test retrieving campaigns
        $response = $this->getJson('/api/email-campaigns');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'subject',
                    'status',
                    'open_rate',
                    'click_rate',
                ]
            ]
        ]);

        // Test sending test email
        $response = $this->postJson("/api/email-campaigns/{$campaignId}/test", [
            'email' => '<EMAIL>',
        ]);
        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Test email sent successfully',
        ]);

        // Test campaign analytics
        $response = $this->getJson("/api/email-campaigns/{$campaignId}/analytics");
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'recipients_count',
            'delivered_count',
            'opened_count',
            'clicked_count',
            'open_rate',
            'click_rate',
        ]);
    }

    /** @test */
    public function it_can_manage_whatsapp_campaigns_via_api()
    {
        Sanctum::actingAs($this->admin);

        // Test creating WhatsApp campaign
        $campaignData = [
            'name' => 'API WhatsApp Campaign',
            'message' => 'Hello from API!',
            'type' => 'broadcast',
            'sender_number' => '+919876543210',
        ];

        $response = $this->postJson('/api/whatsapp-campaigns', $campaignData);
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'message',
                'status',
            ]
        ]);

        $campaignId = $response->json('data.id');

        // Test sending test message
        $response = $this->postJson("/api/whatsapp-campaigns/{$campaignId}/test", [
            'phone' => '+919876543211',
        ]);
        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Test message sent successfully',
        ]);
    }

    /** @test */
    public function it_can_access_analytics_via_api()
    {
        Sanctum::actingAs($this->admin);

        // Create test data
        Lead::factory()->count(10)->create();
        EmailCampaign::factory()->count(5)->create();

        // Test dashboard analytics
        $response = $this->getJson('/api/analytics/dashboard');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_leads',
            'new_leads_today',
            'conversion_rate',
            'avg_lead_score',
            'email_performance' => [
                'total_campaigns',
                'avg_open_rate',
                'avg_click_rate',
            ],
            'whatsapp_performance' => [
                'total_campaigns',
                'avg_delivery_rate',
            ],
            'social_performance' => [
                'total_posts',
                'avg_engagement',
            ],
        ]);

        // Test lead analytics
        $response = $this->getJson('/api/analytics/leads');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'lead_sources',
            'service_interests',
            'status_distribution',
            'score_distribution',
            'conversion_funnel',
        ]);

        // Test marketing analytics
        $response = $this->getJson('/api/analytics/marketing');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'email_campaigns' => [
                'total',
                'performance_metrics',
            ],
            'whatsapp_campaigns' => [
                'total',
                'performance_metrics',
            ],
            'social_media' => [
                'total_posts',
                'engagement_metrics',
            ],
        ]);

        // Test trend analysis
        $response = $this->getJson('/api/analytics/trends?metric=leads&period=30');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
            'labels',
            'summary' => [
                'total',
                'growth_rate',
                'trend',
            ],
        ]);
    }

    /** @test */
    public function it_can_access_customer_portal_via_api()
    {
        Sanctum::actingAs($this->customer);

        // Test customer dashboard
        $response = $this->getJson('/api/customer/dashboard');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'projects' => [
                'active',
                'completed',
                'total',
            ],
            'invoices' => [
                'pending',
                'paid',
                'total_amount',
            ],
            'support_tickets' => [
                'open',
                'closed',
                'total',
            ],
        ]);

        // Test customer projects
        $response = $this->getJson('/api/customer/projects');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'status',
                    'progress',
                    'start_date',
                    'end_date',
                ]
            ]
        ]);

        // Test customer invoices
        $response = $this->getJson('/api/customer/invoices');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'number',
                    'amount',
                    'status',
                    'due_date',
                ]
            ]
        ]);
    }

    /** @test */
    public function it_can_access_metal_rates_via_api()
    {
        // Test public metal rates endpoint
        $response = $this->getJson('/api/metal-rates');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'gold' => [
                'price_per_gram',
                'price_per_ounce',
                'change_24h',
                'change_percentage',
            ],
            'silver' => [
                'price_per_gram',
                'price_per_ounce',
                'change_24h',
                'change_percentage',
            ],
            'platinum' => [
                'price_per_gram',
                'price_per_ounce',
                'change_24h',
                'change_percentage',
            ],
            'last_updated',
            'source',
        ]);

        // Test historical rates
        $response = $this->getJson('/api/metal-rates/historical?metal=gold&period=30');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'date',
                    'price',
                    'change',
                ]
            ],
            'summary' => [
                'highest',
                'lowest',
                'average',
                'volatility',
            ],
        ]);

        // Test rate alerts (requires authentication)
        Sanctum::actingAs($this->customer);
        
        $response = $this->postJson('/api/metal-rates/alerts', [
            'metal' => 'gold',
            'target_price' => 50000,
            'condition' => 'above',
            'email' => $this->customer->email,
        ]);
        $response->assertStatus(201);
        $response->assertJson([
            'message' => 'Alert created successfully',
        ]);
    }

    /** @test */
    public function it_handles_api_rate_limiting()
    {
        // Test rate limiting for unauthenticated requests
        for ($i = 0; $i < 65; $i++) {
            $response = $this->getJson('/api/metal-rates');
            if ($i < 60) {
                $response->assertStatus(200);
            } else {
                $response->assertStatus(429); // Too Many Requests
                break;
            }
        }
    }

    /** @test */
    public function it_handles_api_validation_errors()
    {
        Sanctum::actingAs($this->admin);

        // Test invalid lead creation
        $response = $this->postJson('/api/leads', [
            'name' => '',
            'email' => 'invalid-email',
            'phone' => 'invalid-phone',
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name', 'email']);

        // Test invalid campaign creation
        $response = $this->postJson('/api/email-campaigns', [
            'name' => '',
            'subject' => '',
            'content' => '',
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name', 'subject', 'content']);
    }

    /** @test */
    public function it_handles_api_authorization()
    {
        // Test unauthorized access
        $response = $this->getJson('/api/leads');
        $response->assertStatus(401);

        // Test customer trying to access admin endpoints
        Sanctum::actingAs($this->customer);
        
        $response = $this->getJson('/api/leads');
        $response->assertStatus(403);

        $response = $this->postJson('/api/email-campaigns', []);
        $response->assertStatus(403);
    }

    /** @test */
    public function it_provides_proper_api_documentation_endpoints()
    {
        // Test API documentation endpoint
        $response = $this->getJson('/api/documentation');
        $response->assertStatus(200);

        // Test API health check
        $response = $this->getJson('/api/health');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'timestamp',
            'services' => [
                'database',
                'cache',
                'queue',
            ],
        ]);

        // Test API version info
        $response = $this->getJson('/api/version');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'version',
            'build',
            'environment',
        ]);
    }
}
