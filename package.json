{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@alpinejs/collapse": "^3.14.9", "@alpinejs/focus": "^3.14.9", "@alpinejs/intersect": "^3.14.9", "@alpinejs/mask": "^3.14.9", "@alpinejs/morph": "^3.14.9", "@alpinejs/persist": "^3.14.9", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "alpinejs": "^3.14.9", "aos": "^2.3.4", "autoprefixer": "^10.4.21", "axios": "^1.6.4", "chart.js": "^4.4.1", "choices.js": "^10.2.0", "d3": "^7.8.5", "flatpickr": "^4.6.13", "gsap": "^3.12.5", "laravel-vite-plugin": "^1.0", "lottie-web": "^5.12.2", "postcss": "^8.5.6", "prismjs": "^1.29.0", "quill": "^1.3.7", "sortablejs": "^1.15.2", "swiper": "^11.0.5", "tailwindcss": "^3.4.17", "vite": "^5.0"}, "dependencies": {"laravel-echo": "^2.2.0", "pusher-js": "^8.4.0"}}