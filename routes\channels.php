<?php

use Illuminate\Support\Facades\Broadcast;
use App\Models\ChatSession;

/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

// Chat session channels - allow access to session participants
Broadcast::channel('chat-session.{sessionId}', function ($user, $sessionId) {
    // Allow access if user is authenticated and involved in the session
    if ($user) {
        $session = ChatSession::where('session_id', $sessionId)->first();
        return $session && ($session->user_id === $user->id || $session->agent_id === $user->id);
    }
    
    // For anonymous users, allow access if they have the session in their browser
    // This will be handled by the frontend authentication
    return true;
});

// Admin channels for agents to monitor all chats
Broadcast::channel('admin-chat-monitor', function ($user) {
    // Only allow authenticated users with admin/agent role
    return $user && ($user->hasRole('admin') || $user->hasRole('agent'));
});
