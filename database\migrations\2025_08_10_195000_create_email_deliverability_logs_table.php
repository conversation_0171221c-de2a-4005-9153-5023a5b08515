<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations for email deliverability monitoring.
     */
    public function up(): void
    {
        Schema::create('email_deliverability_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('email_account_id')->constrained('email_accounts')->onDelete('cascade');
            $table->foreignId('contact_id')->nullable()->constrained('contacts')->onDelete('set null');
            $table->foreignId('email_campaign_id')->nullable()->constrained('email_campaigns')->onDelete('set null');
            $table->string('email_address');
            $table->enum('status', [
                'sent', 'delivered', 'bounced', 'failed', 'deferred', 'rejected'
            ])->default('sent');
            $table->enum('bounce_type', [
                'hard', 'soft', 'complaint', 'suppression'
            ])->nullable();
            $table->text('bounce_reason')->nullable();
            $table->decimal('spam_score', 4, 2)->nullable();
            $table->timestamp('delivery_time')->nullable();
            $table->timestamp('opened_at')->nullable();
            $table->timestamp('clicked_at')->nullable();
            $table->timestamp('unsubscribed_at')->nullable();
            $table->timestamp('complained_at')->nullable();
            $table->json('metadata')->nullable(); // Store additional tracking data
            $table->timestamps();

            // Indexes for performance with massive email volumes
            $table->index(['email_account_id', 'status'], 'idx_deliverability_account_status');
            $table->index(['status', 'created_at'], 'idx_deliverability_status_date');
            $table->index(['email_address', 'status'], 'idx_deliverability_email_status');
            $table->index(['bounce_type', 'created_at'], 'idx_deliverability_bounce_date');
            $table->index('spam_score', 'idx_deliverability_spam_score');
            $table->index('delivery_time', 'idx_deliverability_delivery_time');
            $table->index(['opened_at', 'email_account_id'], 'idx_deliverability_opened');
            $table->index(['clicked_at', 'email_account_id'], 'idx_deliverability_clicked');
        });

        // Create sender reputation tracking table
        Schema::create('sender_reputation_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('email_account_id')->constrained('email_accounts')->onDelete('cascade');
            $table->date('date');
            $table->integer('emails_sent')->default(0);
            $table->integer('emails_delivered')->default(0);
            $table->integer('emails_bounced')->default(0);
            $table->integer('emails_opened')->default(0);
            $table->integer('emails_clicked')->default(0);
            $table->integer('complaints')->default(0);
            $table->integer('unsubscribes')->default(0);
            $table->decimal('deliverability_rate', 5, 2)->default(0);
            $table->decimal('bounce_rate', 5, 2)->default(0);
            $table->decimal('open_rate', 5, 2)->default(0);
            $table->decimal('click_rate', 5, 2)->default(0);
            $table->decimal('complaint_rate', 5, 2)->default(0);
            $table->decimal('average_spam_score', 4, 2)->default(0);
            $table->enum('reputation_status', [
                'excellent', 'good', 'fair', 'poor', 'critical'
            ])->default('good');
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->unique(['email_account_id', 'date'], 'unique_account_date_reputation');
            $table->index(['date', 'reputation_status'], 'idx_reputation_date_status');
            $table->index(['email_account_id', 'reputation_status'], 'idx_reputation_account_status');
        });

        // Create email warming schedule table
        Schema::create('email_warming_schedules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('email_account_id')->constrained('email_accounts')->onDelete('cascade');
            $table->enum('status', ['active', 'paused', 'completed', 'failed'])->default('active');
            $table->integer('current_day')->default(1);
            $table->integer('total_days')->default(30);
            $table->integer('daily_limit_start')->default(10);
            $table->integer('daily_limit_target')->default(500);
            $table->integer('current_daily_limit')->default(10);
            $table->integer('emails_sent_today')->default(0);
            $table->date('warming_start_date');
            $table->date('warming_end_date')->nullable();
            $table->json('daily_targets')->nullable(); // Store daily sending targets
            $table->json('performance_metrics')->nullable(); // Store daily performance
            $table->text('notes')->nullable();
            $table->timestamps();

            $table->index(['email_account_id', 'status'], 'idx_warming_account_status');
            $table->index(['status', 'current_day'], 'idx_warming_status_day');
            $table->index('warming_start_date', 'idx_warming_start_date');
        });

        // Create blacklist monitoring table
        Schema::create('blacklist_monitoring', function (Blueprint $table) {
            $table->id();
            $table->foreignId('email_account_id')->constrained('email_accounts')->onDelete('cascade');
            $table->string('blacklist_name'); // SURBL, URIBL, Spamhaus, etc.
            $table->string('ip_address')->nullable();
            $table->string('domain');
            $table->enum('status', ['clean', 'listed', 'unknown'])->default('clean');
            $table->timestamp('last_checked_at');
            $table->timestamp('listed_at')->nullable();
            $table->timestamp('delisted_at')->nullable();
            $table->text('listing_reason')->nullable();
            $table->text('delisting_instructions')->nullable();
            $table->timestamps();

            $table->index(['email_account_id', 'status'], 'idx_blacklist_account_status');
            $table->index(['domain', 'blacklist_name'], 'idx_blacklist_domain_name');
            $table->index(['status', 'last_checked_at'], 'idx_blacklist_status_checked');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blacklist_monitoring');
        Schema::dropIfExists('email_warming_schedules');
        Schema::dropIfExists('sender_reputation_logs');
        Schema::dropIfExists('email_deliverability_logs');
    }
};
