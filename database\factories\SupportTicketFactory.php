<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SupportTicket>
 */
class SupportTicketFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'customer_id' => User::factory()->create(['role' => 'customer'])->id,
            'assigned_to' => User::factory()->create(['role' => 'user'])->id,
            'ticket_number' => 'TKT-' . date('Y') . '-' . $this->faker->unique()->numberBetween(100000, 999999),
            'title' => $this->faker->randomElement([
                'Website loading issue',
                'Feature request for mobile app',
                'Payment gateway integration',
                'SEO optimization query',
                'Design modification request',
                'Bug report - contact form',
                'Performance optimization',
                'Content update request',
                'Database connectivity issue',
                'Email delivery problem'
            ]),
            'description' => $this->faker->paragraphs(2, true),
            'priority' => $this->faker->randomElement(['low', 'medium', 'high', 'urgent']),
            'category' => $this->faker->randomElement(['technical', 'billing', 'general', 'feature_request']),
            'status' => $this->faker->randomElement(['open', 'in_progress', 'resolved', 'closed']),
            'last_response_at' => $this->faker->optional(0.8)->dateTimeBetween('-7 days', 'now'),
            'metadata' => [
                'browser' => $this->faker->randomElement(['Chrome', 'Firefox', 'Safari', 'Edge']),
                'os' => $this->faker->randomElement(['Windows', 'macOS', 'Linux', 'iOS', 'Android']),
                'url' => $this->faker->optional()->url(),
                'user_agent' => $this->faker->userAgent(),
            ],
        ];
    }

    public function open()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'open',
                'last_response_at' => null,
            ];
        });
    }

    public function inProgress()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'in_progress',
                'last_response_at' => $this->faker->dateTimeBetween('-3 days', 'now'),
            ];
        });
    }

    public function resolved()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'resolved',
                'last_response_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
            ];
        });
    }

    public function urgent()
    {
        return $this->state(function (array $attributes) {
            return [
                'priority' => 'urgent',
                'status' => 'in_progress',
            ];
        });
    }
}
