<!-- Live Chat Widget -->
<div x-data="liveChat" x-init="init()" class="fixed bottom-6 right-6 z-50">
    <!-- Chat Toggle Button -->
    <button @click="toggle()"
            :class="isOpen ? 'bg-red-500 hover:bg-red-600' : (connected ? 'bg-green-600 hover:bg-green-700' : 'bg-primary-600 hover:bg-primary-700')"
            class="w-14 h-14 rounded-full text-white shadow-lg transition-all duration-300 flex items-center justify-center chat-widget relative">

        <!-- Connection indicator -->
        <div x-show="connected && !isOpen" class="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>

        <!-- Loading spinner -->
        <div x-show="loading" class="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>

        <!-- Chat icon -->
        <svg x-show="!isOpen && !loading" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>

        <!-- Close icon -->
        <svg x-show="isOpen && !loading" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
    </button>

    <!-- Chat Window -->
    <div x-show="isOpen"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95 translate-y-4"
         x-transition:enter-end="opacity-100 transform scale-100 translate-y-0"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100 translate-y-0"
         x-transition:leave-end="opacity-0 transform scale-95 translate-y-4"
         class="absolute bottom-16 right-0 w-80 h-96 bg-white rounded-lg shadow-2xl border border-gray-200 flex flex-col overflow-hidden">

        <!-- Chat Header -->
        <div class="bg-primary-600 text-white p-4 flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center mr-3">
                    <span class="text-primary-600 font-semibold text-sm">BT</span>
                </div>
                <div>
                    <h3 class="font-semibold">Bhavitech Support</h3>
                    <p class="text-xs text-primary-100" x-text="connected ? 'Connected - We\'re here to help!' : 'Connecting...'"></p>
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <!-- Connection status -->
                <div x-show="connected" class="w-2 h-2 bg-green-400 rounded-full"></div>
                <button @click="toggle()" class="text-primary-100 hover:text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Error Message -->
        <div x-show="error" class="p-3 bg-red-100 border-l-4 border-red-500 text-red-700 text-sm">
            <p x-text="error"></p>
            <button @click="error = null" class="text-red-500 hover:text-red-700 text-xs underline mt-1">Dismiss</button>
        </div>

        <!-- Chat Messages -->
        <div class="flex-1 p-4 overflow-y-auto space-y-3" id="chat-messages">
            <!-- Loading state -->
            <div x-show="loading && messages.length === 0" class="flex justify-center items-center h-32">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                <span class="ml-2 text-gray-600">Starting chat...</span>
            </div>

            <!-- Messages -->
            <template x-for="message in messages" :key="message.messageId || message.timestamp">
                <div :class="message.isUser ? 'flex justify-end' : 'flex justify-start'">
                    <div class="max-w-xs">
                        <!-- Sender label for non-user messages -->
                        <div x-show="!message.isUser" class="text-xs text-gray-500 mb-1 px-1" x-text="getSenderLabel(message)"></div>

                        <div :class="getMessageClass(message)" class="px-3 py-2 rounded-lg text-sm">
                            <p x-text="message.text" class="whitespace-pre-wrap"></p>
                            <p :class="message.isUser ? 'text-primary-100' : 'text-gray-500'"
                               class="text-xs mt-1"
                               x-text="formatTime(message.timestamp)"></p>
                        </div>
                    </div>
                </div>
            </template>

            <!-- Typing Indicator -->
            <div x-show="isTyping" class="flex justify-start">
                <div class="bg-gray-100 text-gray-800 max-w-xs px-3 py-2 rounded-lg text-sm">
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="p-4 border-t border-gray-200">
            <form @submit.prevent="sendMessage()" class="flex space-x-2">
                <input x-model="newMessage"
                       type="text"
                       placeholder="Type your message..."
                       :disabled="!connected || loading"
                       @input="sendTypingIndicator(true)"
                       @blur="sendTypingIndicator(false)"
                       class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm disabled:bg-gray-100 disabled:cursor-not-allowed">
                <button type="submit"
                        :disabled="!newMessage.trim() || !connected || loading"
                        :class="(newMessage.trim() && connected && !loading) ? 'bg-primary-600 hover:bg-primary-700' : 'bg-gray-300 cursor-not-allowed'"
                        class="px-3 py-2 text-white rounded-lg transition-colors">
                    <div x-show="loading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <svg x-show="!loading" class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </form>
            <p class="text-xs text-gray-500 mt-2">
                For immediate assistance, call
                <a href="tel:+917010860889" class="text-primary-600 hover:underline">+91 7010860889</a>
            </p>
        </div>
    </div>
</div>
