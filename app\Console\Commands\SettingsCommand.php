<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\SettingsService;
use App\Models\Setting;

class SettingsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settings:manage
                            {action : Action to perform (get|set|list|export|import|clear-cache)}
                            {key? : Setting key (required for get/set)}
                            {value? : Setting value (required for set)}
                            {--category= : Filter by category (for list)}
                            {--file= : File path (for export/import)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage system settings from command line';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'get':
                $this->getSetting();
                break;
            case 'set':
                $this->setSetting();
                break;
            case 'list':
                $this->listSettings();
                break;
            case 'export':
                $this->exportSettings();
                break;
            case 'import':
                $this->importSettings();
                break;
            case 'clear-cache':
                $this->clearCache();
                break;
            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: get, set, list, export, import, clear-cache');
                return 1;
        }

        return 0;
    }

    private function getSetting()
    {
        $key = $this->argument('key');
        if (!$key) {
            $this->error('Key is required for get action');
            return;
        }

        $value = SettingsService::get($key);
        if ($value === null) {
            $this->warn("Setting '{$key}' not found");
        } else {
            $this->info("Setting '{$key}': {$value}");
        }
    }

    private function setSetting()
    {
        $key = $this->argument('key');
        $value = $this->argument('value');

        if (!$key || $value === null) {
            $this->error('Key and value are required for set action');
            return;
        }

        try {
            SettingsService::set($key, $value);
            $this->info("Setting '{$key}' updated successfully");
        } catch (\Exception $e) {
            $this->error("Failed to set setting: {$e->getMessage()}");
        }
    }

    private function listSettings()
    {
        $category = $this->option('category');

        $query = Setting::where('is_active', true);
        if ($category) {
            $query->where('category', $category);
        }

        $settings = $query->orderBy('category')->orderBy('sort_order')->get();

        if ($settings->isEmpty()) {
            $this->warn('No settings found');
            return;
        }

        $this->table(
            ['Key', 'Value', 'Category', 'Type', 'Description'],
            $settings->map(function ($setting) {
                return [
                    $setting->key,
                    $setting->is_encrypted ? '••••••••' : \Str::limit($setting->value, 30),
                    $setting->category,
                    $setting->type,
                    \Str::limit($setting->description, 50),
                ];
            })
        );
    }

    private function exportSettings()
    {
        $file = $this->option('file') ?: 'settings_export.json';

        try {
            $settings = SettingsService::exportSettings();
            file_put_contents($file, json_encode($settings, JSON_PRETTY_PRINT));
            $this->info("Settings exported to: {$file}");
        } catch (\Exception $e) {
            $this->error("Failed to export settings: {$e->getMessage()}");
        }
    }

    private function importSettings()
    {
        $file = $this->option('file');
        if (!$file) {
            $this->error('File path is required for import action');
            return;
        }

        if (!file_exists($file)) {
            $this->error("File not found: {$file}");
            return;
        }

        try {
            $settings = json_decode(file_get_contents($file), true);
            if (!$settings) {
                $this->error('Invalid JSON file');
                return;
            }

            SettingsService::importSettings($settings);
            $this->info("Settings imported from: {$file}");
        } catch (\Exception $e) {
            $this->error("Failed to import settings: {$e->getMessage()}");
        }
    }

    private function clearCache()
    {
        try {
            SettingsService::clearCache();
            $this->info('Settings cache cleared successfully');
        } catch (\Exception $e) {
            $this->error("Failed to clear cache: {$e->getMessage()}");
        }
    }
}
