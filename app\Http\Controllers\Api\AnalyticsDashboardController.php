<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\AdvancedAnalyticsService;
use App\Services\RealTimeAnalyticsService;
use App\Models\AnalyticsEvent;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AnalyticsDashboardController extends Controller
{
    public function __construct(
        protected AdvancedAnalyticsService $analyticsService,
        protected RealTimeAnalyticsService $realTimeService
    ) {}

    public function overview(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
        ]);

        $overview = $this->analyticsService->getDashboardOverview($validated['days'] ?? 30);

        return response()->json([
            'success' => true,
            'data' => $overview,
        ]);
    }

    public function realTime(): JsonResponse
    {
        $realTimeData = $this->realTimeService->getRealTimeDashboard();

        return response()->json([
            'success' => true,
            'data' => $realTimeData,
        ]);
    }

    public function leadAnalytics(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
            'source' => 'nullable|string',
            'service' => 'nullable|string',
        ]);

        $startDate = now()->subDays($validated['days'] ?? 30);
        $leadAnalytics = $this->analyticsService->getLeadAnalytics($startDate);

        // Apply filters if provided
        if (isset($validated['source']) || isset($validated['service'])) {
            $leadAnalytics = $this->applyLeadFilters($leadAnalytics, $validated);
        }

        return response()->json([
            'success' => true,
            'data' => $leadAnalytics,
        ]);
    }

    public function marketingPerformance(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
            'channel' => 'nullable|in:email,whatsapp,social_media',
        ]);

        $startDate = now()->subDays($validated['days'] ?? 30);
        $performance = $this->analyticsService->getMarketingPerformance($startDate);

        // Filter by channel if specified
        if (isset($validated['channel'])) {
            $performance = [$validated['channel'] => $performance[$validated['channel']]];
        }

        return response()->json([
            'success' => true,
            'data' => $performance,
        ]);
    }

    public function conversionFunnel(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
        ]);

        $startDate = now()->subDays($validated['days'] ?? 30);
        $funnel = $this->analyticsService->getConversionFunnel($startDate);

        return response()->json([
            'success' => true,
            'data' => $funnel,
        ]);
    }

    public function revenueAnalytics(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
            'service' => 'nullable|string',
        ]);

        $startDate = now()->subDays($validated['days'] ?? 30);
        $revenue = $this->analyticsService->getRevenueAnalytics($startDate);

        return response()->json([
            'success' => true,
            'data' => $revenue,
        ]);
    }

    public function trafficAnalytics(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
            'source' => 'nullable|string',
        ]);

        $startDate = now()->subDays($validated['days'] ?? 30);
        $traffic = $this->analyticsService->getTrafficAnalytics($startDate);

        return response()->json([
            'success' => true,
            'data' => $traffic,
        ]);
    }

    public function engagementMetrics(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
        ]);

        $startDate = now()->subDays($validated['days'] ?? 30);
        $engagement = $this->analyticsService->getEngagementMetrics($startDate);

        return response()->json([
            'success' => true,
            'data' => $engagement,
        ]);
    }

    public function growthTrends(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
        ]);

        $startDate = now()->subDays($validated['days'] ?? 30);
        $growth = $this->analyticsService->getGrowthTrends($startDate);

        return response()->json([
            'success' => true,
            'data' => $growth,
        ]);
    }

    public function liveVisitors(): JsonResponse
    {
        $visitors = $this->realTimeService->getLiveVisitors();

        return response()->json([
            'success' => true,
            'data' => $visitors,
        ]);
    }

    public function activeSessions(): JsonResponse
    {
        $sessions = $this->realTimeService->getActiveSessions();

        return response()->json([
            'success' => true,
            'data' => $sessions,
        ]);
    }

    public function recentLeads(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'minutes' => 'nullable|integer|min:5|max:1440',
        ]);

        $leads = $this->realTimeService->getRecentLeads($validated['minutes'] ?? 60);

        return response()->json([
            'success' => true,
            'data' => $leads,
        ]);
    }

    public function liveEvents(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'minutes' => 'nullable|integer|min:5|max:60',
        ]);

        $events = $this->realTimeService->getLiveEvents($validated['minutes'] ?? 15);

        return response()->json([
            'success' => true,
            'data' => $events,
        ]);
    }

    public function currentCampaigns(): JsonResponse
    {
        $campaigns = $this->realTimeService->getCurrentCampaigns();

        return response()->json([
            'success' => true,
            'data' => $campaigns,
        ]);
    }

    public function realTimeMetrics(): JsonResponse
    {
        $metrics = $this->realTimeService->getRealTimeMetrics();

        return response()->json([
            'success' => true,
            'data' => $metrics,
        ]);
    }

    public function geographicActivity(): JsonResponse
    {
        $geographic = $this->realTimeService->getGeographicActivity();

        return response()->json([
            'success' => true,
            'data' => $geographic,
        ]);
    }

    public function deviceBreakdown(): JsonResponse
    {
        $devices = $this->realTimeService->getDeviceBreakdown();

        return response()->json([
            'success' => true,
            'data' => $devices,
        ]);
    }

    public function alerts(): JsonResponse
    {
        $alerts = $this->realTimeService->getAlerts();

        return response()->json([
            'success' => true,
            'data' => $alerts,
        ]);
    }

    public function trackEvent(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'event_name' => 'required|string',
            'page_url' => 'required|url',
            'visitor_id' => 'required|string',
            'session_id' => 'required|string',
            'utm_source' => 'nullable|string',
            'utm_medium' => 'nullable|string',
            'utm_campaign' => 'nullable|string',
            'device_type' => 'nullable|string',
            'browser' => 'nullable|string',
            'city' => 'nullable|string',
            'country' => 'nullable|string',
            'metadata' => 'nullable|array',
        ]);

        $this->realTimeService->trackRealTimeEvent($validated);

        return response()->json([
            'success' => true,
            'message' => 'Event tracked successfully',
        ]);
    }

    public function exportData(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => 'required|in:overview,leads,marketing,traffic,revenue',
            'days' => 'nullable|integer|min:1|max:365',
            'format' => 'nullable|in:json,csv,excel',
        ]);

        $days = $validated['days'] ?? 30;
        $format = $validated['format'] ?? 'json';

        $data = match ($validated['type']) {
            'overview' => $this->analyticsService->getDashboardOverview($days),
            'leads' => $this->analyticsService->getLeadAnalytics(now()->subDays($days)),
            'marketing' => $this->analyticsService->getMarketingPerformance(now()->subDays($days)),
            'traffic' => $this->analyticsService->getTrafficAnalytics(now()->subDays($days)),
            'revenue' => $this->analyticsService->getRevenueAnalytics(now()->subDays($days)),
        };

        if ($format === 'json') {
            return response()->json([
                'success' => true,
                'data' => $data,
            ]);
        }

        // For CSV/Excel export, you would implement file generation here
        return response()->json([
            'success' => true,
            'message' => 'Export functionality would be implemented here',
            'data' => $data,
        ]);
    }

    public function predictiveInsights(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => 'required|in:lead_forecast,revenue_projection,growth_prediction',
            'period' => 'nullable|in:week,month,quarter,year',
        ]);

        $forecastingService = app(\App\Services\ForecastingService::class);

        $insights = match ($validated['type']) {
            'lead_forecast' => $forecastingService->forecastLeads($validated['period'] ?? 'month'),
            'revenue_projection' => $forecastingService->projectRevenue($validated['period'] ?? 'month'),
            'growth_prediction' => $forecastingService->predictGrowth($validated['period'] ?? 'month'),
        };

        return response()->json([
            'success' => true,
            'data' => $insights,
        ]);
    }

    public function customReport(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'metrics' => 'required|array',
            'metrics.*' => 'in:leads,conversions,revenue,traffic,engagement',
            'dimensions' => 'nullable|array',
            'dimensions.*' => 'in:source,service,device,location,time',
            'filters' => 'nullable|array',
            'date_range' => 'required|array',
            'date_range.start' => 'required|date',
            'date_range.end' => 'required|date|after:date_range.start',
        ]);

        $reportService = app(\App\Services\CustomReportService::class);
        $report = $reportService->generateCustomReport($validated);

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    protected function applyLeadFilters(array $leadAnalytics, array $filters): array
    {
        // This would apply additional filtering logic
        // For now, returning the original data
        return $leadAnalytics;
    }
}
