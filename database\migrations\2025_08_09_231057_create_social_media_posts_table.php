<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('social_media_posts', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable();
            $table->text('content');
            $table->json('media_urls')->nullable(); // Images, videos, etc.
            $table->json('hashtags')->nullable();
            $table->enum('status', ['draft', 'scheduled', 'published', 'failed', 'cancelled'])->default('draft');
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('published_at')->nullable();
            $table->json('platform_posts')->nullable(); // Store platform-specific post IDs
            $table->json('analytics')->nullable(); // Engagement metrics
            $table->string('campaign_id')->nullable(); // Link to marketing campaign
            $table->enum('post_type', ['text', 'image', 'video', 'carousel', 'story', 'reel'])->default('text');
            $table->json('targeting')->nullable(); // Audience targeting options
            $table->boolean('boost_post')->default(false);
            $table->decimal('boost_budget', 8, 2)->nullable();
            $table->json('cross_post_platforms')->nullable(); // Which platforms to post to
            $table->text('failure_reason')->nullable();
            $table->integer('retry_count')->default(0);
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();

            $table->index(['status', 'scheduled_at']);
            $table->index(['created_by', 'status']);
            $table->index('campaign_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('social_media_posts');
    }
};
