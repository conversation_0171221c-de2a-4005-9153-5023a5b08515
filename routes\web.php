<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\CustomerPortalController;
use App\Http\Controllers\DashboardController;

// Homepage
Route::get('/', function () {
    return view('home');
})->name('home');

// Service Pages
Route::get('/services/web-development', function () {
    return view('services.web-development');
})->name('services.web-development');

Route::get('/services/mobile-development', function () {
    return view('services.mobile-development');
})->name('services.mobile-development');

Route::get('/services/digital-marketing', function () {
    return view('services.digital-marketing');
})->name('services.digital-marketing');

Route::get('/services/graphic-design', function () {
    return view('services.graphic-design');
})->name('services.graphic-design');

// Other Pages
Route::get('/portfolio', function () {
    return view('portfolio');
})->name('portfolio');

Route::get('/about', function () {
    return view('about');
})->name('about');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

Route::get('/quote', function () {
    return view('quote');
})->name('quote');

// Form Handlers
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
Route::post('/quote', [ContactController::class, 'storeQuote'])->name('quote.store');

// Authentication Routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [RegisterController::class, 'register']);

// Password Reset Routes
Route::get('/forgot-password', function () {
    return view('auth.forgot-password');
})->middleware('guest')->name('password.request');

Route::post('/forgot-password', [App\Http\Controllers\Auth\ForgotPasswordController::class, 'sendResetLinkEmail'])
    ->middleware('guest')->name('password.email');

Route::get('/reset-password/{token}', function ($token) {
    return view('auth.reset-password', ['token' => $token]);
})->middleware('guest')->name('password.reset');

Route::post('/reset-password', [App\Http\Controllers\Auth\ResetPasswordController::class, 'reset'])
    ->middleware('guest')->name('password.update');

// Dashboard Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Customer Portal Routes - Only for customers
    Route::prefix('customer')->name('customer.')->middleware(['auth', 'customer'])->group(function () {
        Route::get('/dashboard', [CustomerPortalController::class, 'dashboard'])->name('dashboard');
        Route::get('/projects', [CustomerPortalController::class, 'projects'])->name('projects');
        Route::get('/projects/{project}', [CustomerPortalController::class, 'showProject'])->name('projects.show');
        Route::get('/invoices', [CustomerPortalController::class, 'invoices'])->name('invoices');
        Route::get('/invoices/{invoice}', [CustomerPortalController::class, 'showInvoice'])->name('invoices.show');
        Route::get('/support', [CustomerPortalController::class, 'support'])->name('support');
        Route::post('/support', [CustomerPortalController::class, 'createTicket'])->name('support.store');
        Route::get('/profile', [CustomerPortalController::class, 'profile'])->name('profile');
        Route::put('/profile', [CustomerPortalController::class, 'updateProfile'])->name('profile.update');
    });
});

// Email tracking routes
Route::prefix('email')->group(function () {
    Route::get('track/open/{token}', [\App\Http\Controllers\EmailTrackingController::class, 'trackOpen'])
        ->name('email.track.open');
    Route::get('track/click/{token}', [\App\Http\Controllers\EmailTrackingController::class, 'trackClick'])
        ->name('email.track.click');
    Route::get('unsubscribe/{token}', [\App\Http\Controllers\EmailTrackingController::class, 'unsubscribe'])
        ->name('email.unsubscribe');
    Route::post('unsubscribe/{token}', [\App\Http\Controllers\EmailTrackingController::class, 'handleUnsubscribe'])
        ->name('email.unsubscribe.handle');
});
