# Bhavitech Logo Assets

This directory contains all the logo and branding assets for the Bhavitech website.

## Logo Files

### Main Logo
- `logo.svg` - Original Bhavitech lotus logo with pink/magenta and green colors (200x200)
- `logo-white.svg` - White version for dark backgrounds (200x200)

### Favicon
- `favicon.svg` - Simplified favicon version (32x32)
- `favicon-16x16.png` - PNG favicon 16x16 (placeholder - convert from SVG)
- `favicon-32x32.png` - PNG favicon 32x32 (placeholder - convert from SVG)
- `apple-touch-icon.png` - Apple touch icon 180x180 (placeholder - convert from SVG)

### Social Media
- `bhavitech-og-image.svg` - Open Graph image for Facebook/LinkedIn (1200x630)
- `bhavitech-twitter-image.svg` - Twitter card image (1200x600)

## Logo Design

The original Bhavitech logo features an elegant lotus flower design with:
- **Center petal**: Bright Pink/Magenta (#E91E63) - represents innovation and creativity
- **Side petals**: Light pink (#F8BBD9) - represents growth and expansion
- **Bottom leaves**: Green (#8BC34A) - represents stability and nature

The design symbolizes growth, transformation, and digital blooming - perfectly representing Bhavitech's mission to help businesses flourish in the digital landscape.

## Usage

### In HTML
```html
<!-- Main logo -->
<img src="{{ asset('images/logo.svg') }}" alt="Bhavitech Logo">

<!-- White logo for dark backgrounds -->
<img src="{{ asset('images/logo-white.svg') }}" alt="Bhavitech Logo">
```

### In CSS
```css
.logo {
    background-image: url('/images/logo.svg');
}
```

## Notes

- All SVG files are scalable and work well at any size
- PNG files are currently placeholders - convert SVGs to PNG for production use
- The favicon.svg is automatically used by modern browsers
- Social media images include company name and tagline
