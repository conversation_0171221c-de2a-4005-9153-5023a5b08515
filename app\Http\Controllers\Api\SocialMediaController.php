<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SocialMediaPost;
use App\Models\SocialMediaAccount;
use App\Services\SocialMediaService;
use App\Services\ContentGenerationService;
use App\Services\SocialMediaAnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class SocialMediaController extends Controller
{
    public function __construct(
        protected SocialMediaService $socialMediaService,
        protected ContentGenerationService $contentService,
        protected SocialMediaAnalyticsService $analyticsService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $posts = SocialMediaPost::with(['creator'])
            ->when($request->status, fn($q) => $q->where('status', $request->status))
            ->when($request->platform, fn($q) => $q->whereJsonContains('cross_post_platforms', $request->platform))
            ->when($request->search, fn($q) => $q->where('content', 'like', "%{$request->search}%"))
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $posts,
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'nullable|string|max:255',
            'content' => 'required|string',
            'media_urls' => 'nullable|array',
            'hashtags' => 'nullable|array',
            'platforms' => 'required|array|min:1',
            'platforms.*' => 'in:facebook,instagram,linkedin,twitter,youtube',
            'post_type' => 'required|in:text,image,video,carousel,story',
            'scheduled_at' => 'nullable|date|after:now',
            'targeting' => 'nullable|array',
            'boost_post' => 'nullable|boolean',
            'boost_budget' => 'nullable|numeric|min:0',
        ]);

        $post = $this->socialMediaService->createPost($validated);

        if ($validated['scheduled_at'] ?? null) {
            $this->socialMediaService->schedulePost($post, Carbon::parse($validated['scheduled_at']));
        }

        return response()->json([
            'success' => true,
            'message' => 'Social media post created successfully',
            'data' => $post,
        ], 201);
    }

    public function show(SocialMediaPost $post): JsonResponse
    {
        $post->load(['creator']);

        return response()->json([
            'success' => true,
            'data' => $post,
        ]);
    }

    public function update(Request $request, SocialMediaPost $post): JsonResponse
    {
        if ($post->status === 'published') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot update published posts',
            ], 400);
        }

        $validated = $request->validate([
            'title' => 'sometimes|string|max:255',
            'content' => 'sometimes|string',
            'media_urls' => 'nullable|array',
            'hashtags' => 'nullable|array',
            'platforms' => 'sometimes|array|min:1',
            'platforms.*' => 'in:facebook,instagram,linkedin,twitter,youtube',
            'post_type' => 'sometimes|in:text,image,video,carousel,story',
            'scheduled_at' => 'nullable|date|after:now',
            'targeting' => 'nullable|array',
            'boost_post' => 'nullable|boolean',
            'boost_budget' => 'nullable|numeric|min:0',
        ]);

        $post->update($validated);

        if (isset($validated['scheduled_at'])) {
            $this->socialMediaService->schedulePost($post, Carbon::parse($validated['scheduled_at']));
        }

        return response()->json([
            'success' => true,
            'message' => 'Post updated successfully',
            'data' => $post->fresh(),
        ]);
    }

    public function destroy(SocialMediaPost $post): JsonResponse
    {
        if ($post->status === 'published') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete published posts',
            ], 400);
        }

        $post->delete();

        return response()->json([
            'success' => true,
            'message' => 'Post deleted successfully',
        ]);
    }

    public function publish(SocialMediaPost $post): JsonResponse
    {
        try {
            $results = $this->socialMediaService->publishPost($post);

            return response()->json([
                'success' => true,
                'message' => 'Post published successfully',
                'data' => [
                    'results' => $results,
                    'post' => $post->fresh(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to publish post: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function schedule(Request $request, SocialMediaPost $post): JsonResponse
    {
        $validated = $request->validate([
            'scheduled_at' => 'required|date|after:now',
        ]);

        try {
            $this->socialMediaService->schedulePost($post, Carbon::parse($validated['scheduled_at']));

            return response()->json([
                'success' => true,
                'message' => 'Post scheduled successfully',
                'data' => $post->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to schedule post: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function generateContent(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => 'required|in:promotional,educational,behind_the_scenes,client_success,seasonal',
            'service' => 'nullable|string',
            'platform' => 'required|in:facebook,instagram,linkedin,twitter',
            'business_type' => 'nullable|string',
            'client_type' => 'nullable|string',
            'achievement' => 'nullable|string',
        ]);

        $content = $this->contentService->generateContent($validated);

        return response()->json([
            'success' => true,
            'data' => $content,
        ]);
    }

    public function generateCalendar(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:90',
        ]);

        $calendar = $this->contentService->generateContentCalendar($validated['days'] ?? 30);

        return response()->json([
            'success' => true,
            'data' => $calendar,
        ]);
    }

    public function getAccounts(): JsonResponse
    {
        $accounts = SocialMediaAccount::active()
            ->with(['creator'])
            ->get();

        return response()->json([
            'success' => true,
            'data' => $accounts,
        ]);
    }

    public function getAnalytics(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
            'platforms' => 'nullable|array',
        ]);

        $analytics = $this->analyticsService->getDashboardMetrics($validated['days'] ?? 30);

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    public function getAccountAnalytics(SocialMediaAccount $account, Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
        ]);

        $analytics = $this->analyticsService->getAccountAnalytics($account, $validated['days'] ?? 30);

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    public function generateReport(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
            'platforms' => 'nullable|array',
        ]);

        $report = $this->analyticsService->generateReport($validated);

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    public function getOptimalTimes(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'platform' => 'required|in:facebook,instagram,linkedin,twitter',
        ]);

        $optimalTimes = $this->socialMediaService->getOptimalPostingTimes($validated['platform']);

        return response()->json([
            'success' => true,
            'data' => $optimalTimes,
        ]);
    }

    public function getContentSuggestions(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'industry' => 'nullable|string',
        ]);

        $suggestions = $this->socialMediaService->generateContentSuggestions($validated['industry'] ?? 'technology');

        return response()->json([
            'success' => true,
            'data' => $suggestions,
        ]);
    }
}
