import './bootstrap';

// Alpine.js
import Alpine from 'alpinejs';
import focus from '@alpinejs/focus';
import collapse from '@alpinejs/collapse';

// Register Alpine plugins
Alpine.plugin(focus);
Alpine.plugin(collapse);

// Alpine.js global data and methods
Alpine.data('navbar', () => ({
    open: false,
    toggle() {
        this.open = !this.open;
    },
    close() {
        this.open = false;
    }
}));

Alpine.data('modal', () => ({
    open: false,
    show() {
        this.open = true;
        document.body.style.overflow = 'hidden';
    },
    hide() {
        this.open = false;
        document.body.style.overflow = 'auto';
    }
}));

Alpine.data('enhancedContactForm', () => ({
    loading: false,
    success: false,
    error: false,
    errors: {},
    errorMessage: '',
    formData: {
        name: '',
        email: '',
        phone: '',
        company: '',
        service_interest: '',
        budget: '',
        timeline: '',
        message: ''
    },

    get isFormValid() {
        return this.formData.name &&
               this.formData.email &&
               this.isValidEmail(this.formData.email) &&
               this.formData.service_interest &&
               this.formData.message &&
               this.formData.message.length >= 10;
    },

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    validateField(field) {
        this.errors[field] = '';

        switch(field) {
            case 'name':
                if (!this.formData.name) {
                    this.errors.name = 'Name is required';
                } else if (this.formData.name.length < 2) {
                    this.errors.name = 'Name must be at least 2 characters';
                }
                break;

            case 'email':
                if (!this.formData.email) {
                    this.errors.email = 'Email is required';
                } else if (!this.isValidEmail(this.formData.email)) {
                    this.errors.email = 'Please enter a valid email address';
                }
                break;

            case 'phone':
                if (this.formData.phone && !this.isValidPhone(this.formData.phone)) {
                    this.errors.phone = 'Please enter a valid phone number';
                }
                break;

            case 'service_interest':
                if (!this.formData.service_interest) {
                    this.errors.service_interest = 'Please select a service';
                }
                break;

            case 'message':
                if (!this.formData.message) {
                    this.errors.message = 'Message is required';
                } else if (this.formData.message.length < 10) {
                    this.errors.message = 'Message must be at least 10 characters';
                } else if (this.formData.message.length > 2000) {
                    this.errors.message = 'Message must be less than 2000 characters';
                }
                break;
        }
    },

    isValidPhone(phone) {
        const phoneRegex = /^[+]?[91]?[6-9]\d{9}$/;
        return phoneRegex.test(phone.replace(/\s+/g, ''));
    },

    resetForm() {
        this.formData = {
            name: '',
            email: '',
            phone: '',
            company: '',
            service_interest: '',
            budget: '',
            timeline: '',
            message: ''
        };
        this.errors = {};
        this.success = false;
        this.error = false;
        this.errorMessage = '';
    },

    async submitForm() {
        // Validate all fields
        Object.keys(this.formData).forEach(field => {
            this.validateField(field);
        });

        // Check if there are any errors
        if (Object.values(this.errors).some(error => error !== '')) {
            return;
        }

        this.loading = true;
        this.error = false;
        this.success = false;
        this.errorMessage = '';

        try {
            const response = await fetch('/contact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(this.formData)
            });

            const data = await response.json();

            if (response.ok && data.success) {
                this.success = true;
                this.resetForm();

                // Scroll to success message
                setTimeout(() => {
                    document.querySelector('[x-show="success"]').scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }, 100);

                // Auto-hide success message after 10 seconds
                setTimeout(() => {
                    this.success = false;
                }, 10000);

            } else {
                this.error = true;
                this.errorMessage = data.message || 'An error occurred while sending your message.';

                // Handle validation errors
                if (data.errors) {
                    this.errors = data.errors;
                }
            }
        } catch (error) {
            this.error = true;
            this.errorMessage = 'Network error. Please check your connection and try again.';
        } finally {
            this.loading = false;
        }
    }
}));

// Portfolio Filter Component
Alpine.data('portfolioFilter', () => ({
    activeFilter: 'all',

    setFilter(category) {
        this.activeFilter = category;
    },

    isActive(category) {
        return this.activeFilter === category;
    },

    filterProjects(category) {
        this.activeFilter = category;

        // Add smooth transition effect
        const projects = document.querySelectorAll('[data-category]');
        projects.forEach(project => {
            const projectCategory = project.dataset.category;

            if (category === 'all' || projectCategory === category) {
                project.style.display = 'block';
                setTimeout(() => {
                    project.classList.add('animate-fade-in');
                }, 100);
            } else {
                project.classList.remove('animate-fade-in');
                setTimeout(() => {
                    project.style.display = 'none';
                }, 300);
            }
        });
    }
}));

// FAQ Component
Alpine.data('faq', () => ({
    openItem: null,

    toggle(index) {
        this.openItem = this.openItem === index ? null : index;
    }
}));

// Stats Counter Component
Alpine.data('statsCounter', () => ({
    init() {
        this.animateCounters();
    },

    animateCounters() {
        const counters = this.$el.querySelectorAll('[data-count]');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    const target = parseInt(counter.dataset.count);
                    const duration = 2000;
                    const increment = target / (duration / 16);

                    let current = 0;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            current = target;
                            clearInterval(timer);
                        }
                        counter.textContent = Math.floor(current).toLocaleString();
                    }, 16);

                    observer.unobserve(counter);
                }
            });
        });

        counters.forEach(counter => observer.observe(counter));
    }
}));

// Scroll animations
Alpine.data('scrollAnimations', () => ({
    init() {
        this.observeElements();
    },

    observeElements() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    }
}));

// Global utilities
window.BhavitechUtils = {
    scrollTo(elementId, offset = 0) {
        const element = document.getElementById(elementId);
        if (element) {
            const top = element.offsetTop - offset;
            window.scrollTo({
                top: top,
                behavior: 'smooth'
            });
        }
    },

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    isValidPhone(phone) {
        const phoneRegex = /^[+]?[91]?[6-9]\d{9}$/;
        return phoneRegex.test(phone.replace(/\s+/g, ''));
    }
};

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', () => {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Lazy load images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
});

// Newsletter Signup Component
Alpine.data('newsletter', () => ({
    email: '',
    loading: false,
    success: false,
    error: false,
    errorMessage: '',

    async subscribe() {
        if (!this.email || !BhavitechUtils.isValidEmail(this.email)) {
            this.error = true;
            this.errorMessage = 'Please enter a valid email address.';
            return;
        }

        this.loading = true;
        this.error = false;
        this.success = false;

        try {
            const response = await fetch('/newsletter/subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({ email: this.email })
            });

            const data = await response.json();

            if (response.ok && data.success) {
                this.success = true;
                this.email = '';
            } else {
                this.error = true;
                this.errorMessage = data.message || 'Failed to subscribe. Please try again.';
            }
        } catch (error) {
            this.error = true;
            this.errorMessage = 'Network error. Please try again.';
        } finally {
            this.loading = false;
        }
    }
}));

// Cookie Consent Component
Alpine.data('cookieConsent', () => ({
    show: false,

    init() {
        // Check if user has already consented
        if (!localStorage.getItem('cookieConsent')) {
            setTimeout(() => {
                this.show = true;
            }, 2000);
        }
    },

    accept() {
        localStorage.setItem('cookieConsent', 'accepted');
        this.show = false;
    },

    decline() {
        localStorage.setItem('cookieConsent', 'declined');
        this.show = false;
    }
}));

// Live Chat Widget Component - API Connected
Alpine.data('liveChat', () => ({
    isOpen: false,
    messages: [],
    newMessage: '',
    isTyping: false,
    sessionId: null,
    visitorId: null,
    loading: false,
    error: null,
    connected: false,

    async init() {
        // Check for existing session in localStorage
        this.sessionId = localStorage.getItem('chat_session_id');
        this.visitorId = localStorage.getItem('chat_visitor_id');

        if (this.sessionId) {
            await this.loadExistingSession();
            this.setupRealTimeListeners();
        }
    },

    async toggle() {
        this.isOpen = !this.isOpen;

        if (this.isOpen && !this.sessionId) {
            await this.startNewSession();
        }
    },

    async startNewSession() {
        this.loading = true;
        this.error = null;

        try {
            const response = await fetch('/api/chat/sessions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify({
                    source_page: window.location.href,
                }),
            });

            const data = await response.json();

            if (data.success) {
                this.sessionId = data.data.session.session_id;
                this.visitorId = data.data.visitor_id;
                this.messages = this.formatMessages(data.data.session.messages || []);

                // Store session info
                localStorage.setItem('chat_session_id', this.sessionId);
                if (this.visitorId) {
                    localStorage.setItem('chat_visitor_id', this.visitorId);
                }

                this.connected = true;
                this.setupRealTimeListeners();
            } else {
                this.error = data.message;
            }
        } catch (error) {
            this.error = 'Failed to start chat session';
            console.error('Chat error:', error);
        } finally {
            this.loading = false;
        }
    },

    async loadExistingSession() {
        if (!this.sessionId) return;

        try {
            const response = await fetch(`/api/chat/sessions/${this.sessionId}`);
            const data = await response.json();

            if (data.success) {
                this.messages = this.formatMessages(data.data.messages || []);
                this.connected = true;
            } else {
                // Session not found, clear localStorage
                localStorage.removeItem('chat_session_id');
                localStorage.removeItem('chat_visitor_id');
                this.sessionId = null;
                this.visitorId = null;
            }
        } catch (error) {
            console.error('Failed to load session:', error);
        }
    },

    async sendMessage() {
        if (!this.newMessage.trim() || !this.sessionId || this.loading) return;

        const messageText = this.newMessage.trim();
        this.newMessage = '';
        this.loading = true;

        // Add message to UI immediately
        this.addMessage({
            message: messageText,
            sender_type: 'visitor',
            sender_name: 'You',
            sent_at: new Date().toISOString(),
        });

        try {
            const response = await fetch('/api/chat/messages', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    message: messageText,
                }),
            });

            const data = await response.json();

            if (!data.success) {
                this.error = data.message;
                // Remove the optimistically added message
                this.messages.pop();
            }
        } catch (error) {
            this.error = 'Failed to send message';
            console.error('Send message error:', error);
            // Remove the optimistically added message
            this.messages.pop();
        } finally {
            this.loading = false;
        }
    },

    addMessage(messageData) {
        this.messages.push({
            text: messageData.message,
            isUser: messageData.sender_type === 'visitor',
            isBot: messageData.sender_type === 'bot',
            isAgent: messageData.sender_type === 'agent',
            senderName: messageData.sender_name,
            timestamp: new Date(messageData.sent_at),
            messageId: messageData.message_id,
        });

        // Scroll to bottom
        this.$nextTick(() => {
            const messagesContainer = document.getElementById('chat-messages');
            if (messagesContainer) {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        });
    },

    formatMessages(messages) {
        return messages.map(msg => ({
            text: msg.message,
            isUser: msg.sender_type === 'visitor',
            isBot: msg.sender_type === 'bot',
            isAgent: msg.sender_type === 'agent',
            senderName: msg.sender_name,
            timestamp: new Date(msg.sent_at),
            messageId: msg.message_id,
        }));
    },

    async sendTypingIndicator(isTyping) {
        if (!this.sessionId) return;

        try {
            await fetch('/api/chat/typing', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify({
                    session_id: this.sessionId,
                    is_typing: isTyping,
                }),
            });
        } catch (error) {
            console.error('Typing indicator error:', error);
        }
    },

    async closeSession() {
        if (!this.sessionId) return;

        try {
            await fetch(`/api/chat/sessions/${this.sessionId}/close`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify({
                    reason: 'User closed chat',
                }),
            });

            // Disconnect real-time listeners
            this.disconnectRealTime();

            // Clear session data
            localStorage.removeItem('chat_session_id');
            localStorage.removeItem('chat_visitor_id');
            this.sessionId = null;
            this.visitorId = null;
            this.messages = [];
            this.connected = false;
        } catch (error) {
            console.error('Close session error:', error);
        }
    },

    getMessageClass(message) {
        if (message.isUser) {
            return 'bg-primary-600 text-white';
        } else if (message.isBot) {
            return 'bg-yellow-100 text-yellow-800 border border-yellow-200';
        } else if (message.isAgent) {
            return 'bg-green-100 text-green-800 border border-green-200';
        }
        return 'bg-gray-100 text-gray-800';
    },

    getSenderLabel(message) {
        if (message.isUser) return 'You';
        if (message.isBot) return 'Bhavitech Bot';
        if (message.isAgent) return message.senderName || 'Agent';
        return message.senderName || 'System';
    },

    formatTime(timestamp) {
        return new Date(timestamp).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        });
    },

    setupRealTimeListeners() {
        if (!this.sessionId || !window.Echo) return;

        // Listen for new messages
        window.Echo.private(`chat-session.${this.sessionId}`)
            .listen('MessageSent', (e) => {
                // Don't add our own messages (they're already added optimistically)
                if (e.message.sender_type !== 'visitor') {
                    this.addMessage({
                        message: e.message.message,
                        sender_type: e.message.sender_type,
                        sender_name: e.message.sender_name,
                        sent_at: e.message.sent_at,
                        message_id: e.message.message_id,
                    });
                }
            })
            .listen('TypingIndicator', (e) => {
                // Show typing indicator for other users
                if (e.sender_type !== 'visitor') {
                    this.isTyping = e.is_typing;

                    // Auto-hide typing indicator after 3 seconds
                    if (e.is_typing) {
                        setTimeout(() => {
                            this.isTyping = false;
                        }, 3000);
                    }
                }
            });
    },

    disconnectRealTime() {
        if (this.sessionId && window.Echo) {
            window.Echo.leave(`chat-session.${this.sessionId}`);
        }
    },
}));

// Login Form Component
Alpine.data('loginForm', () => ({
    loading: false,
    error: false,
    errors: {},
    errorMessage: '',
    showPassword: false,
    formData: {
        email: '',
        password: '',
        remember: false
    },

    get isFormValid() {
        return this.formData.email && this.formData.password;
    },

    async submitForm() {
        this.loading = true;
        this.error = false;
        this.errors = {};
        this.errorMessage = '';

        try {
            const formData = new FormData();
            formData.append('email', this.formData.email);
            formData.append('password', this.formData.password);
            formData.append('remember', this.formData.remember ? '1' : '0');
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            const response = await fetch('/login', {
                method: 'POST',
                body: formData
            });

            if (response.redirected) {
                // Redirect will be handled by the server
                window.location.href = response.url;
            } else if (response.ok) {
                // Handle JSON response for successful login
                const data = await response.json();
                if (data.redirect) {
                    window.location.href = data.redirect;
                }
            } else {
                const data = await response.json();
                this.error = true;

                if (data.errors) {
                    this.errors = data.errors;
                    this.errorMessage = Object.values(data.errors)[0][0];
                } else {
                    this.errorMessage = data.message || 'Login failed. Please check your credentials.';
                }
            }
        } catch (error) {
            this.error = true;
            this.errorMessage = 'Network error. Please try again.';
        } finally {
            this.loading = false;
        }
    }
}));

// Register Form Component
Alpine.data('registerForm', () => ({
    loading: false,
    error: false,
    errors: {},
    errorMessage: '',
    showPassword: false,
    showConfirmPassword: false,
    formData: {
        name: '',
        email: '',
        phone: '',
        company: '',
        role: 'customer',
        password: '',
        password_confirmation: '',
        terms: false
    },

    get isFormValid() {
        return this.formData.name &&
               this.formData.email &&
               this.formData.role &&
               this.formData.password &&
               this.formData.password_confirmation &&
               this.formData.password === this.formData.password_confirmation &&
               this.formData.password.length >= 8 &&
               this.formData.terms;
    },

    async submitForm() {
        this.loading = true;
        this.error = false;
        this.errors = {};
        this.errorMessage = '';

        try {
            const formData = new FormData();
            Object.keys(this.formData).forEach(key => {
                if (key === 'terms') {
                    formData.append(key, this.formData[key] ? '1' : '0');
                } else {
                    formData.append(key, this.formData[key]);
                }
            });
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            const response = await fetch('/register', {
                method: 'POST',
                body: formData
            });

            if (response.redirected) {
                window.location.href = response.url;
            } else if (response.ok) {
                const data = await response.json();
                if (data.redirect) {
                    window.location.href = data.redirect;
                }
            } else {
                const data = await response.json();
                this.error = true;

                if (data.errors) {
                    this.errors = data.errors;
                    this.errorMessage = Object.values(data.errors)[0][0];
                } else {
                    this.errorMessage = data.message || 'Registration failed. Please try again.';
                }
            }
        } catch (error) {
            this.error = true;
            this.errorMessage = 'Network error. Please try again.';
        } finally {
            this.loading = false;
        }
    }
}));

// Start Alpine
Alpine.start();
