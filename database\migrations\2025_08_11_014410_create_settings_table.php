<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('category')->default('general');
            $table->string('type')->default('string'); // string, integer, boolean, array, json, encrypted
            $table->text('description')->nullable();
            $table->json('validation_rules')->nullable();
            $table->json('options')->nullable(); // For select/radio options
            $table->boolean('is_encrypted')->default(false);
            $table->boolean('is_public')->default(false); // Can be accessed by frontend
            $table->boolean('requires_restart')->default(false); // Requires app restart
            $table->integer('sort_order')->default(0);
            $table->boolean('is_active')->default(true);
            $table->string('group')->nullable(); // Sub-category grouping
            $table->json('metadata')->nullable(); // Additional configuration
            $table->timestamp('last_modified_at')->nullable();
            $table->unsignedBigInteger('modified_by')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['category', 'is_active']);
            $table->index(['key', 'is_active']);
            $table->index(['category', 'group', 'sort_order']);

            // Foreign key for user who modified
            $table->foreign('modified_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
