<?php

namespace App\Filament\Widgets;

use App\Models\Lead;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class LeadScoringDistributionChart extends ChartWidget
{
    protected static ?string $heading = 'Lead Scoring Distribution';
    protected static ?int $sort = 3;
    protected static ?string $pollingInterval = '60s';

    public ?string $filter = 'all_time';

    protected function getFilters(): ?array
    {
        return [
            'today' => 'Today',
            'this_week' => 'This Week',
            'this_month' => 'This Month',
            'this_quarter' => 'This Quarter',
            'all_time' => 'All Time',
        ];
    }

    protected function getData(): array
    {
        $query = Lead::query();

        // Apply date filter
        switch ($this->filter) {
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case 'this_week':
                $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'this_month':
                $query->whereMonth('created_at', now()->month)
                      ->whereYear('created_at', now()->year);
                break;
            case 'this_quarter':
                $quarter = ceil(now()->month / 3);
                $startMonth = ($quarter - 1) * 3 + 1;
                $endMonth = $quarter * 3;
                $query->whereBetween('created_at', [
                    now()->month($startMonth)->startOfMonth(),
                    now()->month($endMonth)->endOfMonth()
                ]);
                break;
            case 'all_time':
            default:
                // No additional filter
                break;
        }

        // Get lead score distribution
        $scoreRanges = [
            'Excellent (90-100)' => $query->clone()->whereBetween('score', [90, 100])->count(),
            'Very Good (80-89)' => $query->clone()->whereBetween('score', [80, 89])->count(),
            'Good (70-79)' => $query->clone()->whereBetween('score', [70, 79])->count(),
            'Average (60-69)' => $query->clone()->whereBetween('score', [60, 69])->count(),
            'Below Average (50-59)' => $query->clone()->whereBetween('score', [50, 59])->count(),
            'Poor (40-49)' => $query->clone()->whereBetween('score', [40, 49])->count(),
            'Very Poor (0-39)' => $query->clone()->whereBetween('score', [0, 39])->count(),
        ];

        return [
            'datasets' => [
                [
                    'label' => 'Lead Distribution',
                    'data' => array_values($scoreRanges),
                    'backgroundColor' => [
                        'rgba(34, 197, 94, 0.8)',   // Excellent - Green
                        'rgba(59, 130, 246, 0.8)',  // Very Good - Blue
                        'rgba(16, 185, 129, 0.8)',  // Good - Emerald
                        'rgba(245, 158, 11, 0.8)',  // Average - Amber
                        'rgba(249, 115, 22, 0.8)',  // Below Average - Orange
                        'rgba(239, 68, 68, 0.8)',   // Poor - Red
                        'rgba(156, 163, 175, 0.8)', // Very Poor - Gray
                    ],
                    'borderColor' => [
                        'rgba(34, 197, 94, 1)',
                        'rgba(59, 130, 246, 1)',
                        'rgba(16, 185, 129, 1)',
                        'rgba(245, 158, 11, 1)',
                        'rgba(249, 115, 22, 1)',
                        'rgba(239, 68, 68, 1)',
                        'rgba(156, 163, 175, 1)',
                    ],
                    'borderWidth' => 2,
                ],
            ],
            'labels' => array_keys($scoreRanges),
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                    'labels' => [
                        'usePointStyle' => true,
                        'padding' => 20,
                    ],
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            const label = context.label || "";
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                            return label + ": " + value + " leads (" + percentage + "%)";
                        }',
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
            'cutout' => '50%',
        ];
    }

    public function getDescription(): ?string
    {
        $query = Lead::query();

        // Apply same date filter as in getData()
        switch ($this->filter) {
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case 'this_week':
                $query->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()]);
                break;
            case 'this_month':
                $query->whereMonth('created_at', now()->month)
                      ->whereYear('created_at', now()->year);
                break;
            case 'this_quarter':
                $quarter = ceil(now()->month / 3);
                $startMonth = ($quarter - 1) * 3 + 1;
                $endMonth = $quarter * 3;
                $query->whereBetween('created_at', [
                    now()->month($startMonth)->startOfMonth(),
                    now()->month($endMonth)->endOfMonth()
                ]);
                break;
        }

        $totalLeads = $query->count();
        $avgScore = $query->avg('score') ?? 0;
        $highQualityLeads = $query->where('score', '>=', 80)->count();
        $highQualityPercentage = $totalLeads > 0 ? ($highQualityLeads / $totalLeads) * 100 : 0;

        return "Total: {$totalLeads} leads | Avg Score: " . number_format($avgScore, 1) . 
               " | High Quality: {$highQualityLeads} (" . number_format($highQualityPercentage, 1) . "%)";
    }
}
