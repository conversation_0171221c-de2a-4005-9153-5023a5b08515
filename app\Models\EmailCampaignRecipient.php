<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmailCampaignRecipient extends Model
{
    use HasFactory;

    protected $fillable = [
        'campaign_id',
        'email',
        'name',
        'status',
        'sent_at',
        'delivered_at',
        'opened_at',
        'clicked_at',
        'bounced_at',
        'unsubscribed_at',
        'error_message',
        'tracking_data',
        'personalization_data',
    ];

    protected $casts = [
        'tracking_data' => 'array',
        'personalization_data' => 'array',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'opened_at' => 'datetime',
        'clicked_at' => 'datetime',
        'bounced_at' => 'datetime',
        'unsubscribed_at' => 'datetime',
    ];

    // Relationships
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(EmailCampaign::class, 'campaign_id');
    }

    // Scopes
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    public function scopeOpened($query)
    {
        return $query->whereNotNull('opened_at');
    }

    public function scopeClicked($query)
    {
        return $query->whereNotNull('clicked_at');
    }

    public function scopeBounced($query)
    {
        return $query->where('status', 'bounced');
    }

    // Methods
    public function markAsSent(): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    public function markAsDelivered(): void
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    public function markAsOpened(): void
    {
        if (!$this->opened_at) {
            $this->update(['opened_at' => now()]);
        }
    }

    public function markAsClicked(): void
    {
        if (!$this->clicked_at) {
            $this->update(['clicked_at' => now()]);
        }
    }

    public function markAsBounced(string $errorMessage = null): void
    {
        $this->update([
            'status' => 'bounced',
            'bounced_at' => now(),
            'error_message' => $errorMessage,
        ]);
    }

    public function markAsUnsubscribed(): void
    {
        $this->update(['unsubscribed_at' => now()]);
    }

    public function isOpened(): bool
    {
        return !is_null($this->opened_at);
    }

    public function isClicked(): bool
    {
        return !is_null($this->clicked_at);
    }

    public function isBounced(): bool
    {
        return $this->status === 'bounced';
    }

    public function isUnsubscribed(): bool
    {
        return !is_null($this->unsubscribed_at);
    }

    public function getEngagementScore(): int
    {
        $score = 0;

        if ($this->isOpened()) {
            $score += 10;
        }

        if ($this->isClicked()) {
            $score += 20;
        }

        // Bonus for quick engagement
        if ($this->opened_at && $this->sent_at) {
            $hoursToOpen = $this->sent_at->diffInHours($this->opened_at);
            if ($hoursToOpen <= 1) {
                $score += 5; // Quick opener bonus
            }
        }

        return $score;
    }
}
