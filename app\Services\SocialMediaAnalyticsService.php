<?php

namespace App\Services;

use App\Models\SocialMediaAccount;
use App\Models\SocialMediaPost;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SocialMediaAnalyticsService
{
    public function getDashboardMetrics(int $days = 30): array
    {
        $startDate = now()->subDays($days);
        
        return [
            'overview' => $this->getOverviewMetrics($startDate),
            'platform_performance' => $this->getPlatformPerformance($startDate),
            'top_posts' => $this->getTopPerformingPosts($startDate),
            'engagement_trends' => $this->getEngagementTrends($startDate),
            'audience_insights' => $this->getAudienceInsights($startDate),
            'content_performance' => $this->getContentPerformance($startDate),
        ];
    }

    protected function getOverviewMetrics(Carbon $startDate): array
    {
        $posts = SocialMediaPost::where('published_at', '>=', $startDate)
            ->where('status', 'published')
            ->get();

        $totalEngagement = $posts->sum(function ($post) {
            $analytics = $post->analytics ?? [];
            return ($analytics['likes'] ?? 0) + 
                   ($analytics['comments'] ?? 0) + 
                   ($analytics['shares'] ?? 0) + 
                   ($analytics['clicks'] ?? 0);
        });

        $totalReach = $posts->sum(function ($post) {
            return $post->analytics['reach'] ?? 0;
        });

        $totalImpressions = $posts->sum(function ($post) {
            return $post->analytics['impressions'] ?? 0;
        });

        return [
            'total_posts' => $posts->count(),
            'total_engagement' => $totalEngagement,
            'total_reach' => $totalReach,
            'total_impressions' => $totalImpressions,
            'avg_engagement_rate' => $totalImpressions > 0 ? ($totalEngagement / $totalImpressions) * 100 : 0,
            'avg_reach_rate' => $totalImpressions > 0 ? ($totalReach / $totalImpressions) * 100 : 0,
        ];
    }

    protected function getPlatformPerformance(Carbon $startDate): array
    {
        $platforms = ['facebook', 'instagram', 'linkedin', 'twitter'];
        $performance = [];

        foreach ($platforms as $platform) {
            $posts = SocialMediaPost::where('published_at', '>=', $startDate)
                ->where('status', 'published')
                ->whereJsonContains('cross_post_platforms', $platform)
                ->get();

            $engagement = $posts->sum(function ($post) {
                $analytics = $post->analytics ?? [];
                return ($analytics['likes'] ?? 0) + 
                       ($analytics['comments'] ?? 0) + 
                       ($analytics['shares'] ?? 0);
            });

            $impressions = $posts->sum(function ($post) {
                return $post->analytics['impressions'] ?? 0;
            });

            $performance[$platform] = [
                'posts_count' => $posts->count(),
                'total_engagement' => $engagement,
                'total_impressions' => $impressions,
                'engagement_rate' => $impressions > 0 ? ($engagement / $impressions) * 100 : 0,
                'avg_engagement_per_post' => $posts->count() > 0 ? $engagement / $posts->count() : 0,
            ];
        }

        return $performance;
    }

    protected function getTopPerformingPosts(Carbon $startDate, int $limit = 10): array
    {
        return SocialMediaPost::where('published_at', '>=', $startDate)
            ->where('status', 'published')
            ->get()
            ->map(function ($post) {
                $analytics = $post->analytics ?? [];
                $engagement = ($analytics['likes'] ?? 0) + 
                             ($analytics['comments'] ?? 0) + 
                             ($analytics['shares'] ?? 0);
                
                return [
                    'id' => $post->id,
                    'title' => $post->title,
                    'content' => substr($post->content, 0, 100) . '...',
                    'published_at' => $post->published_at,
                    'platforms' => $post->cross_post_platforms,
                    'engagement' => $engagement,
                    'reach' => $analytics['reach'] ?? 0,
                    'impressions' => $analytics['impressions'] ?? 0,
                    'engagement_rate' => ($analytics['impressions'] ?? 0) > 0 ? 
                        ($engagement / $analytics['impressions']) * 100 : 0,
                ];
            })
            ->sortByDesc('engagement')
            ->take($limit)
            ->values()
            ->toArray();
    }

    protected function getEngagementTrends(Carbon $startDate): array
    {
        $trends = [];
        $currentDate = $startDate->copy();
        
        while ($currentDate->lte(now())) {
            $dayPosts = SocialMediaPost::whereDate('published_at', $currentDate)
                ->where('status', 'published')
                ->get();

            $dayEngagement = $dayPosts->sum(function ($post) {
                $analytics = $post->analytics ?? [];
                return ($analytics['likes'] ?? 0) + 
                       ($analytics['comments'] ?? 0) + 
                       ($analytics['shares'] ?? 0);
            });

            $dayReach = $dayPosts->sum(function ($post) {
                return $post->analytics['reach'] ?? 0;
            });

            $trends[] = [
                'date' => $currentDate->format('Y-m-d'),
                'posts_count' => $dayPosts->count(),
                'engagement' => $dayEngagement,
                'reach' => $dayReach,
                'avg_engagement_per_post' => $dayPosts->count() > 0 ? $dayEngagement / $dayPosts->count() : 0,
            ];

            $currentDate->addDay();
        }

        return $trends;
    }

    protected function getAudienceInsights(Carbon $startDate): array
    {
        // This would typically come from platform APIs
        // For now, return sample data structure
        return [
            'demographics' => [
                'age_groups' => [
                    '18-24' => 15,
                    '25-34' => 35,
                    '35-44' => 30,
                    '45-54' => 15,
                    '55+' => 5,
                ],
                'gender' => [
                    'male' => 60,
                    'female' => 38,
                    'other' => 2,
                ],
                'locations' => [
                    'Salem' => 40,
                    'Chennai' => 25,
                    'Bangalore' => 15,
                    'Coimbatore' => 10,
                    'Other' => 10,
                ],
            ],
            'interests' => [
                'Technology' => 45,
                'Business' => 30,
                'Design' => 25,
                'Marketing' => 20,
                'Startups' => 15,
            ],
            'activity_patterns' => [
                'peak_hours' => ['10:00', '14:00', '19:00'],
                'peak_days' => ['Tuesday', 'Wednesday', 'Thursday'],
                'engagement_by_hour' => $this->getHourlyEngagement(),
            ],
        ];
    }

    protected function getContentPerformance(Carbon $startDate): array
    {
        $posts = SocialMediaPost::where('published_at', '>=', $startDate)
            ->where('status', 'published')
            ->get();

        $contentTypes = $posts->groupBy('post_type');
        $performance = [];

        foreach ($contentTypes as $type => $typePosts) {
            $totalEngagement = $typePosts->sum(function ($post) {
                $analytics = $post->analytics ?? [];
                return ($analytics['likes'] ?? 0) + 
                       ($analytics['comments'] ?? 0) + 
                       ($analytics['shares'] ?? 0);
            });

            $totalImpressions = $typePosts->sum(function ($post) {
                return $post->analytics['impressions'] ?? 0;
            });

            $performance[$type] = [
                'posts_count' => $typePosts->count(),
                'total_engagement' => $totalEngagement,
                'avg_engagement' => $typePosts->count() > 0 ? $totalEngagement / $typePosts->count() : 0,
                'engagement_rate' => $totalImpressions > 0 ? ($totalEngagement / $totalImpressions) * 100 : 0,
            ];
        }

        return $performance;
    }

    protected function getHourlyEngagement(): array
    {
        $hourlyData = [];
        
        for ($hour = 0; $hour < 24; $hour++) {
            // Sample data - in production, this would come from actual analytics
            $engagement = match (true) {
                $hour >= 9 && $hour <= 11 => rand(80, 100),
                $hour >= 14 && $hour <= 16 => rand(70, 90),
                $hour >= 19 && $hour <= 21 => rand(60, 80),
                default => rand(20, 50),
            };

            $hourlyData[sprintf('%02d:00', $hour)] = $engagement;
        }

        return $hourlyData;
    }

    public function getAccountAnalytics(SocialMediaAccount $account, int $days = 30): array
    {
        $startDate = now()->subDays($days);
        
        $posts = SocialMediaPost::where('published_at', '>=', $startDate)
            ->where('status', 'published')
            ->whereJsonContains('cross_post_platforms', $account->platform)
            ->get();

        $totalEngagement = $posts->sum(function ($post) {
            $analytics = $post->analytics ?? [];
            return ($analytics['likes'] ?? 0) + 
                   ($analytics['comments'] ?? 0) + 
                   ($analytics['shares'] ?? 0);
        });

        $totalReach = $posts->sum(function ($post) {
            return $post->analytics['reach'] ?? 0;
        });

        $totalImpressions = $posts->sum(function ($post) {
            return $post->analytics['impressions'] ?? 0;
        });

        return [
            'account_info' => [
                'platform' => $account->platform,
                'username' => $account->username,
                'is_active' => $account->is_active,
                'last_posted' => $account->last_posted_at,
            ],
            'performance' => [
                'posts_count' => $posts->count(),
                'total_engagement' => $totalEngagement,
                'total_reach' => $totalReach,
                'total_impressions' => $totalImpressions,
                'avg_engagement_per_post' => $posts->count() > 0 ? $totalEngagement / $posts->count() : 0,
                'engagement_rate' => $totalImpressions > 0 ? ($totalEngagement / $totalImpressions) * 100 : 0,
            ],
            'trends' => $this->getAccountTrends($account, $startDate),
            'top_posts' => $this->getAccountTopPosts($account, $startDate),
        ];
    }

    protected function getAccountTrends(SocialMediaAccount $account, Carbon $startDate): array
    {
        $trends = [];
        $currentDate = $startDate->copy();
        
        while ($currentDate->lte(now())) {
            $dayPosts = SocialMediaPost::whereDate('published_at', $currentDate)
                ->where('status', 'published')
                ->whereJsonContains('cross_post_platforms', $account->platform)
                ->get();

            $dayEngagement = $dayPosts->sum(function ($post) {
                $analytics = $post->analytics ?? [];
                return ($analytics['likes'] ?? 0) + 
                       ($analytics['comments'] ?? 0) + 
                       ($analytics['shares'] ?? 0);
            });

            $trends[] = [
                'date' => $currentDate->format('Y-m-d'),
                'posts' => $dayPosts->count(),
                'engagement' => $dayEngagement,
            ];

            $currentDate->addDay();
        }

        return $trends;
    }

    protected function getAccountTopPosts(SocialMediaAccount $account, Carbon $startDate): array
    {
        return SocialMediaPost::where('published_at', '>=', $startDate)
            ->where('status', 'published')
            ->whereJsonContains('cross_post_platforms', $account->platform)
            ->get()
            ->map(function ($post) {
                $analytics = $post->analytics ?? [];
                $engagement = ($analytics['likes'] ?? 0) + 
                             ($analytics['comments'] ?? 0) + 
                             ($analytics['shares'] ?? 0);
                
                return [
                    'id' => $post->id,
                    'content' => substr($post->content, 0, 100) . '...',
                    'published_at' => $post->published_at,
                    'engagement' => $engagement,
                ];
            })
            ->sortByDesc('engagement')
            ->take(5)
            ->values()
            ->toArray();
    }

    public function generateReport(array $options = []): array
    {
        $days = $options['days'] ?? 30;
        $platforms = $options['platforms'] ?? ['facebook', 'instagram', 'linkedin', 'twitter'];
        
        $report = [
            'report_period' => [
                'start_date' => now()->subDays($days)->format('Y-m-d'),
                'end_date' => now()->format('Y-m-d'),
                'days' => $days,
            ],
            'executive_summary' => $this->getExecutiveSummary($days),
            'detailed_metrics' => $this->getDashboardMetrics($days),
            'recommendations' => $this->generateRecommendations($days),
            'generated_at' => now()->toISOString(),
        ];

        return $report;
    }

    protected function getExecutiveSummary(int $days): array
    {
        $metrics = $this->getOverviewMetrics(now()->subDays($days));
        
        return [
            'key_highlights' => [
                "Published {$metrics['total_posts']} posts across all platforms",
                "Achieved {$metrics['total_engagement']} total engagements",
                "Reached {$metrics['total_reach']} unique users",
                "Maintained {$metrics['avg_engagement_rate']}% average engagement rate",
            ],
            'performance_summary' => $this->getPerformanceSummary($metrics),
        ];
    }

    protected function getPerformanceSummary(array $metrics): string
    {
        $engagementRate = $metrics['avg_engagement_rate'];
        
        if ($engagementRate > 5) {
            return 'Excellent performance with high engagement rates across platforms.';
        } elseif ($engagementRate > 3) {
            return 'Good performance with steady engagement and reach.';
        } elseif ($engagementRate > 1) {
            return 'Average performance with room for improvement in engagement.';
        } else {
            return 'Below average performance requiring strategy optimization.';
        }
    }

    protected function generateRecommendations(int $days): array
    {
        $platformPerformance = $this->getPlatformPerformance(now()->subDays($days));
        $recommendations = [];

        foreach ($platformPerformance as $platform => $performance) {
            if ($performance['engagement_rate'] < 2) {
                $recommendations[] = "Improve {$platform} content strategy to increase engagement";
            }
            
            if ($performance['posts_count'] < 10) {
                $recommendations[] = "Increase posting frequency on {$platform}";
            }
        }

        if (empty($recommendations)) {
            $recommendations[] = 'Continue current strategy while testing new content formats';
            $recommendations[] = 'Consider expanding to additional platforms';
        }

        return $recommendations;
    }
}
