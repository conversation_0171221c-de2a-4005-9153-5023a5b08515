<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class MetalRate extends Model
{
    use HasFactory;

    protected $fillable = [
        'metal',
        'rate',
        'currency',
        'unit',
        'source',
        'fetched_at',
        'purity',
        'buy_price',
        'sell_price',
        'spot_price',
        'market',
        'location',
        'change_amount',
        'change_percentage',
        'is_active',
        'additional_data',
    ];

    protected $casts = [
        'rate' => 'decimal:2',
        'buy_price' => 'decimal:2',
        'sell_price' => 'decimal:2',
        'spot_price' => 'decimal:2',
        'change_amount' => 'decimal:2',
        'change_percentage' => 'decimal:2',
        'fetched_at' => 'datetime',
        'is_active' => 'boolean',
        'additional_data' => 'array',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByMetal($query, $metalType)
    {
        return $query->where('metal_type', $metalType);
    }

    public function scopeByPurity($query, $purity)
    {
        return $query->where('purity', $purity);
    }

    public function scopeLatest($query)
    {
        return $query->orderBy('rate_date', 'desc');
    }

    public function scopeToday($query)
    {
        return $query->whereDate('rate_date', today());
    }

    public function scopeDomestic($query)
    {
        return $query->where('market', 'domestic');
    }

    public function scopeInternational($query)
    {
        return $query->where('market', 'international');
    }

    // Methods
    public static function getLatestRate(string $metalType, string $purity = null, string $unit = 'gram'): ?self
    {
        $query = static::active()
            ->byMetal($metalType)
            ->where('unit', $unit)
            ->latest('rate_date');

        if ($purity) {
            $query->byPurity($purity);
        }

        return $query->first();
    }

    public static function getTodaysRates(string $metalType = null): \Illuminate\Database\Eloquent\Collection
    {
        $query = static::active()->today()->latest('rate_date');

        if ($metalType) {
            $query->byMetal($metalType);
        }

        return $query->get();
    }

    public function getPriceChange(): array
    {
        $previousRate = static::active()
            ->byMetal($this->metal_type)
            ->where('purity', $this->purity)
            ->where('unit', $this->unit)
            ->where('rate_date', '<', $this->rate_date)
            ->latest('rate_date')
            ->first();

        if (!$previousRate) {
            return [
                'amount' => 0,
                'percentage' => 0,
                'direction' => 'neutral'
            ];
        }

        $changeAmount = $this->buy_price - $previousRate->buy_price;
        $changePercentage = ($changeAmount / $previousRate->buy_price) * 100;

        return [
            'amount' => round($changeAmount, 2),
            'percentage' => round($changePercentage, 2),
            'direction' => $changeAmount > 0 ? 'up' : ($changeAmount < 0 ? 'down' : 'neutral')
        ];
    }

    public function getFormattedPrice(string $type = 'buy'): string
    {
        $price = $type === 'sell' ? $this->sell_price : $this->buy_price;

        return '₹' . number_format($price, 2);
    }

    public function isGold(): bool
    {
        return $this->metal_type === 'gold';
    }

    public function isSilver(): bool
    {
        return $this->metal_type === 'silver';
    }

    public static function broadcastRateUpdate(string $metalType): void
    {
        // This method would trigger WhatsApp/Email broadcasts
        // Implementation would be in a service class
    }
}
