# Bhavitech Platform - API Documentation

🔌 **Comprehensive API documentation for the Bhavitech Enterprise CRM & Marketing Platform.**

## 📋 Overview

The Bhavitech Platform provides a comprehensive RESTful API that allows you to integrate with all platform features including lead management, marketing campaigns, analytics, and customer portal functionality.

### Base URL
```
Production: https://your-domain.com/api
Development: http://localhost:8000/api
```

### API Version
Current API Version: `v1`

## 🔐 Authentication

### Bearer Token Authentication
All API endpoints require authentication using Bearer tokens (Laravel Sanctum).

#### Login to Get Token
```http
POST /api/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}
```

**Response:**
```json
{
    "user": {
        "id": 1,
        "name": "Admin User",
        "email": "<EMAIL>",
        "role": "admin"
    },
    "token": "1|abc123def456ghi789..."
}
```

#### Using the Token
Include the token in the Authorization header for all subsequent requests:
```http
Authorization: Bearer 1|abc123def456ghi789...
```

#### Logout
```http
POST /api/auth/logout
Authorization: Bearer {token}
```

## 📊 Rate Limiting

- **Authenticated Users**: 1000 requests per minute
- **Unauthenticated Users**: 60 requests per minute
- **Rate limit headers** are included in all responses

## 🎯 Lead Management API

### List Leads
```http
GET /api/leads
Authorization: Bearer {token}
```

**Query Parameters:**
- `page` (int): Page number for pagination
- `per_page` (int): Items per page (max 100)
- `status` (string): Filter by status (new, contacted, qualified, converted, lost)
- `source` (string): Filter by lead source
- `service_interest` (string): Filter by service interest
- `score_min` (int): Minimum lead score
- `score_max` (int): Maximum lead score
- `search` (string): Search in name, email, company
- `sort` (string): Sort field (created_at, score, name)
- `order` (string): Sort order (asc, desc)

**Response:**
```json
{
    "data": [
        {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "phone": "+************",
            "company": "Example Corp",
            "source": "website_contact_form",
            "service_interest": "web_development",
            "status": "new",
            "score": 85,
            "estimated_value": 150000,
            "conversion_probability": 75.5,
            "assigned_to": {
                "id": 2,
                "name": "Sales Rep"
            },
            "created_at": "2025-08-10T10:30:00Z",
            "updated_at": "2025-08-10T10:30:00Z"
        }
    ],
    "meta": {
        "current_page": 1,
        "total": 150,
        "per_page": 20,
        "last_page": 8
    }
}
```

### Create Lead
```http
POST /api/leads
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": "+************",
    "company": "Smith Industries",
    "source": "website_contact_form",
    "service_interest": "digital_marketing",
    "message": "I need help with digital marketing for my business.",
    "budget_range": "1l_3l",
    "timeline": "3_months",
    "metadata": {
        "utm_source": "google",
        "utm_campaign": "summer_2024"
    }
}
```

**Response:**
```json
{
    "data": {
        "id": 151,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "phone": "+************",
        "company": "Smith Industries",
        "source": "website_contact_form",
        "service_interest": "digital_marketing",
        "status": "new",
        "score": 78,
        "estimated_value": 125000,
        "conversion_probability": 68.2,
        "created_at": "2025-08-10T11:00:00Z",
        "updated_at": "2025-08-10T11:00:00Z"
    }
}
```

### Get Lead Details
```http
GET /api/leads/{id}
Authorization: Bearer {token}
```

### Update Lead
```http
PUT /api/leads/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
    "status": "contacted",
    "notes": "Called and discussed requirements",
    "assigned_to": 2
}
```

### Calculate Lead Score
```http
POST /api/leads/{id}/score
Authorization: Bearer {token}
```

**Response:**
```json
{
    "score": 85,
    "breakdown": {
        "source_score": 40,
        "service_score": 25,
        "company_score": 15,
        "engagement_score": 5
    },
    "grade": "A",
    "conversion_probability": 75.5
}
```

### Lead Activities
```http
GET /api/leads/{id}/activities
POST /api/leads/{id}/activities
```

## 📧 Email Campaign API

### List Email Campaigns
```http
GET /api/email-campaigns
Authorization: Bearer {token}
```

**Query Parameters:**
- `status` (string): Filter by status (draft, scheduled, sending, sent, paused)
- `type` (string): Filter by type (newsletter, promotional, transactional)
- `search` (string): Search in name, subject

**Response:**
```json
{
    "data": [
        {
            "id": 1,
            "name": "Welcome Series - Part 1",
            "subject": "Welcome to Bhavitech!",
            "type": "newsletter",
            "status": "sent",
            "recipients_count": 1250,
            "delivered_count": 1230,
            "opened_count": 615,
            "clicked_count": 123,
            "open_rate": 50.0,
            "click_rate": 10.0,
            "scheduled_at": "2025-08-10T09:00:00Z",
            "sent_at": "2025-08-10T09:00:00Z",
            "created_at": "2025-08-09T15:30:00Z"
        }
    ]
}
```

### Create Email Campaign
```http
POST /api/email-campaigns
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "Summer Promotion 2025",
    "subject": "50% Off All Web Development Services",
    "content": "<h1>Limited Time Offer!</h1><p>Get 50% off...</p>",
    "type": "promotional",
    "email_lists": [1, 2, 3],
    "scheduled_at": "2025-08-15T10:00:00Z",
    "is_ab_test": true,
    "ab_subject_variant": "Huge Savings on Web Development!"
}
```

### Send Test Email
```http
POST /api/email-campaigns/{id}/test
Authorization: Bearer {token}
Content-Type: application/json

{
    "email": "<EMAIL>"
}
```

### Campaign Analytics
```http
GET /api/email-campaigns/{id}/analytics
Authorization: Bearer {token}
```

**Response:**
```json
{
    "campaign_id": 1,
    "recipients_count": 1250,
    "delivered_count": 1230,
    "bounced_count": 20,
    "opened_count": 615,
    "clicked_count": 123,
    "unsubscribed_count": 5,
    "open_rate": 50.0,
    "click_rate": 10.0,
    "bounce_rate": 1.6,
    "unsubscribe_rate": 0.4,
    "top_links": [
        {
            "url": "https://bhavitech.com/services",
            "clicks": 85
        }
    ],
    "geographic_data": {
        "India": 800,
        "USA": 300,
        "UK": 150
    }
}
```

## 📱 WhatsApp Campaign API

### List WhatsApp Campaigns
```http
GET /api/whatsapp-campaigns
Authorization: Bearer {token}
```

### Create WhatsApp Campaign
```http
POST /api/whatsapp-campaigns
Authorization: Bearer {token}
Content-Type: application/json

{
    "name": "Product Launch Announcement",
    "message": "🚀 Exciting news! We've launched our new AI-powered CRM. Check it out: https://bhavitech.com/ai-crm",
    "type": "broadcast",
    "sender_number": "+************",
    "whatsapp_lists": [1, 2],
    "scheduled_at": "2025-08-15T14:00:00Z",
    "media_url": "https://example.com/product-image.jpg",
    "media_type": "image"
}
```

### Send Test Message
```http
POST /api/whatsapp-campaigns/{id}/test
Authorization: Bearer {token}
Content-Type: application/json

{
    "phone": "+************"
}
```

## 📱 Social Media API

### List Social Media Posts
```http
GET /api/social-media-posts
Authorization: Bearer {token}
```

### Create Social Media Post
```http
POST /api/social-media-posts
Authorization: Bearer {token}
Content-Type: application/json

{
    "title": "New AI Features Launch",
    "content": "We're excited to announce our latest AI-powered features! 🤖 #AI #Innovation #Bhavitech",
    "post_type": "image",
    "platforms": ["facebook", "instagram", "linkedin"],
    "scheduled_at": "2025-08-15T16:00:00Z",
    "media_urls": ["https://example.com/ai-features.jpg"],
    "hashtags": ["AI", "Innovation", "Bhavitech", "Technology"]
}
```

### Publish Post Now
```http
POST /api/social-media-posts/{id}/publish
Authorization: Bearer {token}
```

## 📊 Analytics API

### Dashboard Analytics
```http
GET /api/analytics/dashboard
Authorization: Bearer {token}
```

**Response:**
```json
{
    "total_leads": 1250,
    "new_leads_today": 15,
    "new_leads_this_week": 85,
    "new_leads_this_month": 320,
    "conversion_rate": 12.5,
    "avg_lead_score": 67.8,
    "pipeline_value": 15750000,
    "email_performance": {
        "total_campaigns": 45,
        "active_campaigns": 3,
        "avg_open_rate": 24.5,
        "avg_click_rate": 4.2
    },
    "whatsapp_performance": {
        "total_campaigns": 28,
        "active_campaigns": 1,
        "avg_delivery_rate": 95.8,
        "avg_read_rate": 78.3
    },
    "social_performance": {
        "total_posts": 156,
        "published_posts": 142,
        "scheduled_posts": 8,
        "avg_engagement": 245
    },
    "top_lead_sources": [
        {"source": "website_contact_form", "count": 450},
        {"source": "social_media_facebook", "count": 280},
        {"source": "referral_customer", "count": 220}
    ]
}
```

### Lead Analytics
```http
GET /api/analytics/leads
Authorization: Bearer {token}
```

**Query Parameters:**
- `period` (string): Time period (7d, 30d, 90d, 1y)
- `group_by` (string): Group by field (source, status, service_interest)

### Marketing Analytics
```http
GET /api/analytics/marketing
Authorization: Bearer {token}
```

### Trend Analysis
```http
GET /api/analytics/trends
Authorization: Bearer {token}
```

**Query Parameters:**
- `metric` (string): Metric to analyze (leads, conversions, revenue)
- `period` (int): Number of days to analyze
- `interval` (string): Data interval (daily, weekly, monthly)

**Response:**
```json
{
    "data": [125, 142, 138, 156, 171, 165, 189],
    "labels": ["Aug 4", "Aug 5", "Aug 6", "Aug 7", "Aug 8", "Aug 9", "Aug 10"],
    "summary": {
        "total": 1086,
        "average": 155.1,
        "growth_rate": 12.8,
        "trend": "increasing"
    }
}
```

## 💰 Metal Rates API

### Current Metal Rates
```http
GET /api/metal-rates
```

**Response:**
```json
{
    "gold": {
        "price_per_gram": 5850.50,
        "price_per_ounce": 1825.75,
        "change_24h": 45.25,
        "change_percentage": 0.78,
        "currency": "INR"
    },
    "silver": {
        "price_per_gram": 68.25,
        "price_per_ounce": 21.45,
        "change_24h": -1.15,
        "change_percentage": -1.66,
        "currency": "INR"
    },
    "platinum": {
        "price_per_gram": 2850.00,
        "price_per_ounce": 890.25,
        "change_24h": 12.50,
        "change_percentage": 0.44,
        "currency": "INR"
    },
    "last_updated": "2025-08-10T12:00:00Z",
    "source": "goldapi.io"
}
```

### Historical Rates
```http
GET /api/metal-rates/historical
```

**Query Parameters:**
- `metal` (string): Metal type (gold, silver, platinum)
- `period` (int): Number of days (max 365)
- `interval` (string): Data interval (daily, weekly, monthly)

### Create Price Alert
```http
POST /api/metal-rates/alerts
Authorization: Bearer {token}
Content-Type: application/json

{
    "metal": "gold",
    "target_price": 6000.00,
    "condition": "above",
    "email": "<EMAIL>",
    "phone": "+************",
    "is_active": true
}
```

## 👥 Customer Portal API

### Customer Dashboard
```http
GET /api/customer/dashboard
Authorization: Bearer {token}
```

**Response:**
```json
{
    "customer": {
        "id": 15,
        "name": "John Customer",
        "email": "<EMAIL>"
    },
    "projects": {
        "active": 2,
        "completed": 5,
        "total": 7,
        "recent": [
            {
                "id": 1,
                "name": "E-commerce Website",
                "status": "in_progress",
                "progress": 75,
                "start_date": "2025-07-01",
                "estimated_completion": "2025-08-15"
            }
        ]
    },
    "invoices": {
        "pending": 1,
        "paid": 8,
        "total_amount": 485000,
        "pending_amount": 125000
    },
    "support_tickets": {
        "open": 1,
        "closed": 12,
        "total": 13
    }
}
```

### Customer Projects
```http
GET /api/customer/projects
Authorization: Bearer {token}
```

### Customer Invoices
```http
GET /api/customer/invoices
Authorization: Bearer {token}
```

### Create Support Ticket
```http
POST /api/customer/support-tickets
Authorization: Bearer {token}
Content-Type: application/json

{
    "subject": "Issue with login",
    "description": "I'm unable to log into my account",
    "priority": "medium",
    "category": "technical"
}
```

## 🔍 System Health API

### Health Check
```http
GET /api/health
```

**Response:**
```json
{
    "status": "healthy",
    "timestamp": "2025-08-10T12:00:00Z",
    "services": {
        "database": "healthy",
        "cache": "healthy",
        "queue": "healthy",
        "storage": "healthy"
    },
    "version": "1.0.0"
}
```

### System Status
```http
GET /api/status
Authorization: Bearer {token}
```

## 📝 Error Handling

### Error Response Format
```json
{
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "The given data was invalid.",
        "details": {
            "email": ["The email field is required."],
            "name": ["The name field is required."]
        }
    },
    "timestamp": "2025-08-10T12:00:00Z"
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Too Many Requests
- `500` - Internal Server Error

### Common Error Codes
- `VALIDATION_ERROR` - Input validation failed
- `AUTHENTICATION_REQUIRED` - Valid token required
- `INSUFFICIENT_PERMISSIONS` - User lacks required permissions
- `RESOURCE_NOT_FOUND` - Requested resource doesn't exist
- `RATE_LIMIT_EXCEEDED` - Too many requests
- `INTERNAL_ERROR` - Server error

## 📚 SDKs & Libraries

### PHP SDK
```php
use Bhavitech\SDK\BhavitechClient;

$client = new BhavitechClient([
    'base_url' => 'https://your-domain.com/api',
    'token' => 'your-api-token'
]);

// Create a lead
$lead = $client->leads()->create([
    'name' => 'John Doe',
    'email' => '<EMAIL>',
    'source' => 'api'
]);

// Get analytics
$analytics = $client->analytics()->dashboard();
```

### JavaScript SDK
```javascript
import { BhavitechClient } from '@bhavitech/sdk';

const client = new BhavitechClient({
    baseUrl: 'https://your-domain.com/api',
    token: 'your-api-token'
});

// Create a lead
const lead = await client.leads.create({
    name: 'John Doe',
    email: '<EMAIL>',
    source: 'api'
});

// Get analytics
const analytics = await client.analytics.dashboard();
```

## 🔗 Webhooks

### Webhook Events
- `lead.created` - New lead created
- `lead.updated` - Lead updated
- `lead.converted` - Lead converted to customer
- `campaign.sent` - Email campaign sent
- `campaign.completed` - Campaign completed
- `project.status_changed` - Project status updated

### Webhook Payload Example
```json
{
    "event": "lead.created",
    "data": {
        "id": 151,
        "name": "Jane Smith",
        "email": "<EMAIL>",
        "source": "website_contact_form",
        "score": 78
    },
    "timestamp": "2025-08-10T12:00:00Z"
}
```

---

**API Documentation Version**: 1.0  
**Last Updated**: August 2025  
**Platform Version**: 1.0.0

For API support, contact: <EMAIL>
