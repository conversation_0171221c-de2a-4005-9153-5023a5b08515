# 🌿 Git Conventions for 1 Crore Email Management System

## 📋 Overview
This document outlines the Git conventions, branch naming standards, and commit message formats for the 1 Crore Email Management System project.

## 🌿 Branch Naming Convention

### **Main Branches**
- `main` - Production-ready code
- `development` - Integration branch for features
- `staging` - Pre-production testing

### **Feature Branches**
Format: `feature/[component]-[description]`

**Email System Components:**
- `feature/email-import-csv-optimization`
- `feature/geographic-filtering-enhancement`
- `feature/mysql-performance-tuning`
- `feature/analytics-dashboard-widgets`
- `feature/whatsapp-integration-api`
- `feature/campaign-automation-workflows`
- `feature/contact-segmentation-advanced`
- `feature/deliverability-monitoring`

### **Bug Fix Branches**
Format: `bugfix/[component]-[issue-description]`

Examples:
- `bugfix/email-import-memory-leak`
- `bugfix/geographic-filter-sql-error`
- `bugfix/mysql-connection-timeout`
- `bugfix/dashboard-loading-performance`

### **Hotfix Branches**
Format: `hotfix/[version]-[critical-issue]`

Examples:
- `hotfix/1.0.1-email-sending-failure`
- `hotfix/1.0.2-database-connection-error`
- `hotfix/1.0.3-security-vulnerability`

### **Release Branches**
Format: `release/[version]`

Examples:
- `release/1.0.0`
- `release/1.1.0`
- `release/2.0.0`

### **Experimental Branches**
Format: `experiment/[description]`

Examples:
- `experiment/ai-email-optimization`
- `experiment/blockchain-verification`
- `experiment/machine-learning-segmentation`

## 📝 Commit Message Convention

### **Format**
```
<type>(<scope>): <subject>

<body>

<footer>
```

### **Types**
- `feat` - New feature
- `fix` - Bug fix
- `docs` - Documentation changes
- `style` - Code style changes (formatting, etc.)
- `refactor` - Code refactoring
- `perf` - Performance improvements
- `test` - Adding or updating tests
- `chore` - Maintenance tasks
- `ci` - CI/CD changes
- `build` - Build system changes

### **Scopes (Email System Specific)**
- `email-import` - Email import functionality
- `geographic` - Geographic filtering
- `mysql` - Database operations
- `analytics` - Analytics and reporting
- `whatsapp` - WhatsApp integration
- `campaigns` - Email campaigns
- `contacts` - Contact management
- `admin` - Admin panel
- `api` - API development
- `security` - Security features
- `performance` - Performance optimizations

### **Examples**

#### **Feature Commits**
```
feat(email-import): add support for massive CSV files up to 1 crore records

- Implement chunked processing for large datasets
- Add memory optimization for bulk imports
- Support geographic data validation
- Add progress tracking for long-running imports

Closes #123
```

#### **Bug Fix Commits**
```
fix(geographic): resolve SQL error in state filtering query

- Fix column name mismatch in contacts table
- Add proper indexing for geographic queries
- Improve error handling for invalid state names

Fixes #456
```

#### **Performance Commits**
```
perf(mysql): optimize database queries for 1 crore email handling

- Add composite indexes for common query patterns
- Implement query result caching
- Optimize JOIN operations for large datasets
- Reduce memory usage in bulk operations

Performance improvement: 75% faster query execution
```

#### **Documentation Commits**
```
docs(setup): add comprehensive MySQL setup guide

- Document installation and configuration
- Add performance tuning recommendations
- Include troubleshooting section
- Add examples for 1 crore email scenarios
```

## 🔄 Workflow Process

### **Feature Development Workflow**
1. **Create Feature Branch**
   ```bash
   git checkout development
   git pull origin development
   git checkout -b feature/email-import-optimization
   ```

2. **Development**
   ```bash
   # Make changes
   git add .
   git commit -m "feat(email-import): add chunked processing for large files"
   ```

3. **Push and Create PR**
   ```bash
   git push -u origin feature/email-import-optimization
   # Create Pull Request to development branch
   ```

4. **Merge to Development**
   ```bash
   git checkout development
   git merge --no-ff feature/email-import-optimization
   git push origin development
   ```

### **Release Workflow**
1. **Create Release Branch**
   ```bash
   git checkout development
   git checkout -b release/1.1.0
   ```

2. **Prepare Release**
   ```bash
   # Update version numbers
   # Update CHANGELOG.md
   # Final testing
   git commit -m "chore(release): prepare version 1.1.0"
   ```

3. **Merge to Main**
   ```bash
   git checkout main
   git merge --no-ff release/1.1.0
   git tag -a v1.1.0 -m "Release version 1.1.0"
   git push origin main --tags
   ```

4. **Merge Back to Development**
   ```bash
   git checkout development
   git merge --no-ff release/1.1.0
   git push origin development
   ```

### **Hotfix Workflow**
1. **Create Hotfix Branch**
   ```bash
   git checkout main
   git checkout -b hotfix/1.0.1-critical-email-bug
   ```

2. **Fix Issue**
   ```bash
   # Make critical fix
   git commit -m "fix(email-import): resolve memory leak in bulk processing"
   ```

3. **Merge to Main and Development**
   ```bash
   git checkout main
   git merge --no-ff hotfix/1.0.1-critical-email-bug
   git tag -a v1.0.1 -m "Hotfix version 1.0.1"
   git push origin main --tags
   
   git checkout development
   git merge --no-ff hotfix/1.0.1-critical-email-bug
   git push origin development
   ```

## 🏷️ Tagging Convention

### **Version Format**
`v[MAJOR].[MINOR].[PATCH]`

### **Version Types**
- **MAJOR** - Breaking changes, major new features
- **MINOR** - New features, backward compatible
- **PATCH** - Bug fixes, small improvements

### **Examples**
- `v1.0.0` - Initial release with 1 crore email support
- `v1.1.0` - Added WhatsApp integration
- `v1.1.1` - Fixed geographic filtering bug
- `v2.0.0` - Major architecture changes

### **Pre-release Tags**
- `v1.1.0-alpha.1` - Alpha release
- `v1.1.0-beta.1` - Beta release
- `v1.1.0-rc.1` - Release candidate

## 📋 Pull Request Guidelines

### **PR Title Format**
`[TYPE] Component: Brief description`

Examples:
- `[FEAT] Email Import: Add support for massive CSV files`
- `[FIX] Geographic Filter: Resolve SQL query performance issue`
- `[PERF] MySQL: Optimize indexes for 1 crore records`

### **PR Description Requirements**
- Clear description of changes
- Link to related issues
- Testing performed
- Performance impact assessment
- Breaking changes (if any)
- Screenshots (if UI changes)

### **PR Labels**
- `email-management` - Email system changes
- `mysql-optimization` - Database improvements
- `geographic-filtering` - Location-based features
- `performance` - Performance improvements
- `security` - Security enhancements
- `documentation` - Documentation updates
- `breaking-change` - Breaking changes
- `needs-review` - Requires review
- `ready-to-merge` - Approved and ready

## 🔍 Code Review Guidelines

### **Review Checklist**
- [ ] Code follows project conventions
- [ ] Commit messages are clear and descriptive
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] Performance impact is acceptable
- [ ] Security implications are considered
- [ ] Email system functionality is preserved

### **Email System Specific Reviews**
- [ ] Large dataset handling is optimized
- [ ] Geographic filtering works correctly
- [ ] MySQL queries are efficient
- [ ] Memory usage is reasonable
- [ ] Email deliverability is maintained

## 📊 Metrics and Monitoring

### **Branch Metrics**
- Feature branch lifetime (target: < 1 week)
- PR review time (target: < 24 hours)
- Merge frequency (target: daily to development)
- Hotfix frequency (target: minimize)

### **Code Quality Metrics**
- Test coverage (target: > 80%)
- Code complexity (target: low)
- Performance benchmarks
- Security scan results

## 🚨 Emergency Procedures

### **Critical Bug Process**
1. Create hotfix branch immediately
2. Notify team via Slack/email
3. Fix and test quickly
4. Fast-track review process
5. Deploy to production ASAP
6. Post-mortem analysis

### **Rollback Procedure**
1. Identify problematic commit/release
2. Create rollback branch
3. Revert changes
4. Test rollback
5. Deploy rollback
6. Investigate root cause

## 📚 Resources

### **Git Commands Reference**
```bash
# Branch management
git branch -a                    # List all branches
git branch -d feature-branch     # Delete local branch
git push origin --delete branch  # Delete remote branch

# Commit management
git log --oneline               # View commit history
git revert <commit-hash>        # Revert specific commit
git cherry-pick <commit-hash>   # Apply specific commit

# Merge management
git merge --no-ff branch-name   # Merge with merge commit
git rebase -i HEAD~3           # Interactive rebase
```

### **Useful Aliases**
```bash
git config --global alias.co checkout
git config --global alias.br branch
git config --global alias.ci commit
git config --global alias.st status
git config --global alias.unstage 'reset HEAD --'
git config --global alias.last 'log -1 HEAD'
git config --global alias.visual '!gitk'
```

---

**📧 Email System Version**: 1.0.0  
**🗄️ Database**: MySQL 8.0  
**🐘 PHP**: 8.2+  
**🎨 Framework**: Laravel 11.x
