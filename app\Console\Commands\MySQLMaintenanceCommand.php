<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MySQLMaintenanceCommand extends Command
{
    protected $signature = 'mysql:maintenance 
                            {--optimize : Optimize tables}
                            {--analyze : Analyze tables}
                            {--check : Check table integrity}
                            {--stats : Show database statistics}
                            {--cleanup : Clean up old data}
                            {--all : Run all maintenance tasks}';

    protected $description = 'Perform MySQL maintenance tasks for optimal performance with massive email datasets';

    public function handle(): int
    {
        if (DB::connection()->getDriverName() !== 'mysql') {
            $this->error('This command is only for MySQL databases.');
            return 1;
        }

        $this->info('🗄️ Starting MySQL maintenance for massive email system...');
        $this->newLine();

        $options = $this->options();

        if ($options['all'] || $options['stats']) {
            $this->showDatabaseStats();
        }

        if ($options['all'] || $options['check']) {
            $this->checkTableIntegrity();
        }

        if ($options['all'] || $options['analyze']) {
            $this->analyzeTables();
        }

        if ($options['all'] || $options['optimize']) {
            $this->optimizeTables();
        }

        if ($options['all'] || $options['cleanup']) {
            $this->cleanupOldData();
        }

        $this->newLine();
        $this->info('✅ MySQL maintenance completed successfully!');
        
        return 0;
    }

    private function showDatabaseStats(): void
    {
        $this->info('📊 Database Statistics:');
        $this->line('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

        // Database size
        $dbSize = DB::select("
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = DATABASE()
        ")[0]->size_mb;

        $this->line("Database Size: {$dbSize} MB");

        // Table statistics
        $tables = DB::select("
            SELECT
                TABLE_NAME as table_name,
                TABLE_ROWS as table_rows,
                ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS size_mb,
                ROUND((DATA_LENGTH / 1024 / 1024), 2) AS data_mb,
                ROUND((INDEX_LENGTH / 1024 / 1024), 2) AS index_mb
            FROM information_schema.tables
            WHERE table_schema = DATABASE()
            ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC
        ");

        $this->table(
            ['Table', 'Rows', 'Total Size (MB)', 'Data (MB)', 'Index (MB)'],
            collect($tables)->map(function ($table) {
                return [
                    $table->table_name,
                    number_format($table->table_rows),
                    $table->size_mb,
                    $table->data_mb,
                    $table->index_mb,
                ];
            })->toArray()
        );

        // Connection statistics
        $connections = DB::select("SHOW STATUS LIKE 'Connections'")[0]->Value;
        $maxConnections = DB::select("SHOW VARIABLES LIKE 'max_connections'")[0]->Value;
        $threadsConnected = DB::select("SHOW STATUS LIKE 'Threads_connected'")[0]->Value;

        $this->newLine();
        $this->line("Connections: {$threadsConnected}/{$maxConnections} (Total: {$connections})");

        // Buffer pool statistics
        $bufferPoolSize = DB::select("SHOW STATUS LIKE 'Innodb_buffer_pool_pages_total'")[0]->Value;
        $bufferPoolFree = DB::select("SHOW STATUS LIKE 'Innodb_buffer_pool_pages_free'")[0]->Value;
        $bufferPoolUsed = $bufferPoolSize - $bufferPoolFree;
        $bufferPoolUsage = round(($bufferPoolUsed / $bufferPoolSize) * 100, 2);

        $this->line("InnoDB Buffer Pool Usage: {$bufferPoolUsage}% ({$bufferPoolUsed}/{$bufferPoolSize} pages)");

        $this->newLine();
    }

    private function checkTableIntegrity(): void
    {
        $this->info('🔍 Checking table integrity...');

        $tables = ['contacts', 'contact_lists', 'contact_list_members', 'email_accounts', 
                  'email_campaigns', 'email_deliverability_logs'];

        foreach ($tables as $table) {
            if ($this->tableExists($table)) {
                $this->line("Checking {$table}...");
                
                $result = DB::select("CHECK TABLE {$table}");
                $status = $result[0]->Msg_text ?? 'Unknown';
                
                if ($status === 'OK') {
                    $this->line("  ✅ {$table}: OK");
                } else {
                    $this->line("  ❌ {$table}: {$status}");
                    Log::warning("Table integrity issue", ['table' => $table, 'status' => $status]);
                }
            }
        }

        $this->newLine();
    }

    private function analyzeTables(): void
    {
        $this->info('📈 Analyzing tables for query optimization...');

        $tables = ['contacts', 'contact_lists', 'contact_list_members', 'email_accounts', 
                  'email_campaigns', 'email_deliverability_logs'];

        foreach ($tables as $table) {
            if ($this->tableExists($table)) {
                $this->line("Analyzing {$table}...");
                
                try {
                    DB::statement("ANALYZE TABLE {$table}");
                    $this->line("  ✅ {$table}: Analyzed");
                } catch (\Exception $e) {
                    $this->line("  ❌ {$table}: Error - " . $e->getMessage());
                    Log::error("Table analysis failed", ['table' => $table, 'error' => $e->getMessage()]);
                }
            }
        }

        $this->newLine();
    }

    private function optimizeTables(): void
    {
        $this->info('⚡ Optimizing tables for better performance...');
        $this->warn('Note: This may take a long time for large tables!');

        $tables = ['contacts', 'contact_lists', 'contact_list_members', 'email_deliverability_logs'];

        foreach ($tables as $table) {
            if ($this->tableExists($table)) {
                $this->line("Optimizing {$table}...");
                
                try {
                    $startTime = microtime(true);
                    DB::statement("OPTIMIZE TABLE {$table}");
                    $endTime = microtime(true);
                    $duration = round($endTime - $startTime, 2);
                    
                    $this->line("  ✅ {$table}: Optimized ({$duration}s)");
                } catch (\Exception $e) {
                    $this->line("  ❌ {$table}: Error - " . $e->getMessage());
                    Log::error("Table optimization failed", ['table' => $table, 'error' => $e->getMessage()]);
                }
            }
        }

        $this->newLine();
    }

    private function cleanupOldData(): void
    {
        $this->info('🧹 Cleaning up old data...');

        // Clean up old email deliverability logs (older than 6 months)
        if ($this->tableExists('email_deliverability_logs')) {
            $deleted = DB::table('email_deliverability_logs')
                ->where('created_at', '<', now()->subMonths(6))
                ->delete();
            
            $this->line("Deleted {$deleted} old email deliverability logs");
        }

        // Clean up old sender reputation logs (older than 1 year)
        if ($this->tableExists('sender_reputation_logs')) {
            $deleted = DB::table('sender_reputation_logs')
                ->where('date', '<', now()->subYear())
                ->delete();
            
            $this->line("Deleted {$deleted} old sender reputation logs");
        }

        // Clean up inactive contacts (not interacted for 2 years)
        $inactiveContacts = DB::table('contacts')
            ->where('last_interaction_at', '<', now()->subYears(2))
            ->where('is_subscribed', false)
            ->count();

        if ($inactiveContacts > 0 && $this->confirm("Delete {$inactiveContacts} inactive contacts?")) {
            $deleted = DB::table('contacts')
                ->where('last_interaction_at', '<', now()->subYears(2))
                ->where('is_subscribed', false)
                ->delete();
            
            $this->line("Deleted {$deleted} inactive contacts");
        }

        // Clean up failed jobs older than 1 month
        if ($this->tableExists('failed_jobs')) {
            $deleted = DB::table('failed_jobs')
                ->where('failed_at', '<', now()->subMonth())
                ->delete();
            
            $this->line("Deleted {$deleted} old failed jobs");
        }

        $this->newLine();
    }

    private function tableExists(string $table): bool
    {
        try {
            return DB::select("SHOW TABLES LIKE '{$table}'") !== [];
        } catch (\Exception $e) {
            return false;
        }
    }
}
