@extends('layouts.app')

@section('title', 'Get Free Quote - Custom Digital Solutions | Bhavitech')
@section('description', 'Get a free, detailed quote for your web development, mobile app, or digital marketing project. Professional consultation and transparent pricing from Bhavitech.')

@section('content')
<!-- Hero Section -->
<section class="relative py-20 lg:py-32 gradient-bg overflow-hidden">
    <div class="absolute inset-0">
        <div class="absolute inset-0 bg-black opacity-40"></div>
        <div class="absolute top-20 left-10 w-72 h-72 bg-yellow-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow"></div>
        <div class="absolute bottom-20 right-10 w-72 h-72 bg-orange-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow animation-delay-200"></div>
    </div>
    
    <div class="relative z-10 container-custom">
        <div class="max-w-4xl mx-auto text-center text-white">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">
                Get Your Free <span class="text-gradient bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">Quote</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-gray-200 animate-slide-up animation-delay-200">
                Tell us about your project and receive a detailed, transparent quote within 24 hours
            </p>
        </div>
    </div>
</section>

<!-- Quote Form Section -->
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="max-w-4xl mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Project Details</h2>
                <p class="text-xl text-gray-600">
                    The more details you provide, the more accurate your quote will be
                </p>
            </div>

            <form x-data="quoteForm" @submit.prevent="submitForm" class="space-y-8">
                <!-- Personal Information -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4">Contact Information</h3>
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                            <input type="text" id="name" x-model="formData.name" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                            <input type="email" id="email" x-model="formData.email" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                        </div>
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                            <input type="tel" id="phone" x-model="formData.phone" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                        </div>
                        <div>
                            <label for="company" class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                            <input type="text" id="company" x-model="formData.company" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                        </div>
                    </div>
                </div>

                <!-- Project Type -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4">Project Type *</h3>
                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <label class="relative">
                            <input type="radio" name="project_type" value="web-development" x-model="formData.projectType" class="sr-only">
                            <div :class="formData.projectType === 'web-development' ? 'border-primary-500 bg-primary-50' : 'border-gray-300'" 
                                 class="border-2 rounded-lg p-4 cursor-pointer transition-all hover:border-primary-300">
                                <div class="text-center">
                                    <svg class="w-8 h-8 mx-auto mb-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                    </svg>
                                    <div class="font-medium">Web Development</div>
                                </div>
                            </div>
                        </label>

                        <label class="relative">
                            <input type="radio" name="project_type" value="mobile-development" x-model="formData.projectType" class="sr-only">
                            <div :class="formData.projectType === 'mobile-development' ? 'border-primary-500 bg-primary-50' : 'border-gray-300'" 
                                 class="border-2 rounded-lg p-4 cursor-pointer transition-all hover:border-primary-300">
                                <div class="text-center">
                                    <svg class="w-8 h-8 mx-auto mb-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                    </svg>
                                    <div class="font-medium">Mobile App</div>
                                </div>
                            </div>
                        </label>

                        <label class="relative">
                            <input type="radio" name="project_type" value="digital-marketing" x-model="formData.projectType" class="sr-only">
                            <div :class="formData.projectType === 'digital-marketing' ? 'border-primary-500 bg-primary-50' : 'border-gray-300'" 
                                 class="border-2 rounded-lg p-4 cursor-pointer transition-all hover:border-primary-300">
                                <div class="text-center">
                                    <svg class="w-8 h-8 mx-auto mb-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                    <div class="font-medium">Digital Marketing</div>
                                </div>
                            </div>
                        </label>

                        <label class="relative">
                            <input type="radio" name="project_type" value="graphic-design" x-model="formData.projectType" class="sr-only">
                            <div :class="formData.projectType === 'graphic-design' ? 'border-primary-500 bg-primary-50' : 'border-gray-300'" 
                                 class="border-2 rounded-lg p-4 cursor-pointer transition-all hover:border-primary-300">
                                <div class="text-center">
                                    <svg class="w-8 h-8 mx-auto mb-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                                    </svg>
                                    <div class="font-medium">Graphic Design</div>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Project Details -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-xl font-semibold mb-4">Project Details</h3>
                    <div class="space-y-6">
                        <div>
                            <label for="project_description" class="block text-sm font-medium text-gray-700 mb-2">Project Description *</label>
                            <textarea id="project_description" x-model="formData.projectDescription" rows="4" required 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
                                      placeholder="Describe your project requirements, goals, and any specific features you need..."></textarea>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6">
                            <div>
                                <label for="budget" class="block text-sm font-medium text-gray-700 mb-2">Budget Range</label>
                                <select id="budget" x-model="formData.budget" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                                    <option value="">Select budget range</option>
                                    <option value="under-50k">Under ₹50,000</option>
                                    <option value="50k-1l">₹50,000 - ₹1,00,000</option>
                                    <option value="1l-2l">₹1,00,000 - ₹2,00,000</option>
                                    <option value="2l-5l">₹2,00,000 - ₹5,00,000</option>
                                    <option value="above-5l">Above ₹5,00,000</option>
                                </select>
                            </div>
                            <div>
                                <label for="timeline" class="block text-sm font-medium text-gray-700 mb-2">Preferred Timeline</label>
                                <select id="timeline" x-model="formData.timeline" 
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors">
                                    <option value="">Select timeline</option>
                                    <option value="asap">ASAP (Rush Job)</option>
                                    <option value="1-month">Within 1 month</option>
                                    <option value="2-3-months">2-3 months</option>
                                    <option value="3-6-months">3-6 months</option>
                                    <option value="flexible">Flexible</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Additional Services Needed</label>
                            <div class="grid md:grid-cols-2 gap-4">
                                <label class="flex items-center">
                                    <input type="checkbox" x-model="formData.additionalServices" value="hosting" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    <span class="ml-2">Web Hosting & Domain</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" x-model="formData.additionalServices" value="maintenance" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    <span class="ml-2">Ongoing Maintenance</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" x-model="formData.additionalServices" value="seo" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    <span class="ml-2">SEO Optimization</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" x-model="formData.additionalServices" value="training" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                                    <span class="ml-2">Training & Support</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                    <button type="submit" :disabled="loading" 
                            class="btn-primary text-lg px-12 py-4 flex items-center justify-center space-x-2 mx-auto">
                        <span x-show="!loading">Get My Free Quote</span>
                        <span x-show="loading" class="flex items-center space-x-2">
                            <svg class="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span>Submitting...</span>
                        </span>
                    </button>
                </div>

                <!-- Success Message -->
                <div x-show="success" x-transition class="bg-green-50 border border-green-200 rounded-lg p-6">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-green-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-lg font-semibold text-green-800">Quote Request Submitted!</h3>
                            <p class="text-green-700">Thank you for your interest. We'll review your requirements and send you a detailed quote within 24 hours.</p>
                        </div>
                    </div>
                </div>

                <!-- Error Message -->
                <div x-show="error" x-transition class="bg-red-50 border border-red-200 rounded-lg p-6">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h3 class="text-lg font-semibold text-red-800">Submission Error</h3>
                            <p class="text-red-700">Sorry, there was an error submitting your quote request. Please try again or contact us directly.</p>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- What Happens Next -->
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">What Happens Next?</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Our streamlined process ensures you get a comprehensive quote quickly
            </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">1</div>
                <h3 class="text-xl font-semibold mb-3">Review & Analysis</h3>
                <p class="text-gray-600">Our team reviews your requirements and analyzes the project scope to understand your needs completely.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">2</div>
                <h3 class="text-xl font-semibold mb-3">Detailed Quote</h3>
                <p class="text-gray-600">We prepare a comprehensive quote with timeline, deliverables, and transparent pricing breakdown.</p>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">3</div>
                <h3 class="text-xl font-semibold mb-3">Consultation Call</h3>
                <p class="text-gray-600">We schedule a call to discuss the quote, answer questions, and refine the project details.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding gradient-bg">
    <div class="container-custom text-center">
        <div class="max-w-3xl mx-auto text-white">
            <h2 class="text-4xl md:text-5xl font-bold mb-6">Need to Discuss Your Project?</h2>
            <p class="text-xl mb-8 text-gray-200">
                Prefer to talk directly? Give us a call and let's discuss your project requirements
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="tel:+917010860889" class="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    Call Now: +91 7010860889
                </a>
                <a href="{{ route('contact') }}" class="btn-secondary border-white text-white hover:bg-white hover:text-primary-600">
                    Contact Us
                </a>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('quoteForm', () => ({
        loading: false,
        success: false,
        error: false,
        formData: {
            name: '',
            email: '',
            phone: '',
            company: '',
            projectType: '',
            projectDescription: '',
            budget: '',
            timeline: '',
            additionalServices: []
        },
        async submitForm() {
            this.loading = true;
            this.error = false;
            this.success = false;
            
            try {
                const response = await fetch('/quote', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify(this.formData)
                });
                
                if (response.ok) {
                    this.success = true;
                    this.formData = {
                        name: '',
                        email: '',
                        phone: '',
                        company: '',
                        projectType: '',
                        projectDescription: '',
                        budget: '',
                        timeline: '',
                        additionalServices: []
                    };
                } else {
                    this.error = true;
                }
            } catch (error) {
                this.error = true;
            } finally {
                this.loading = false;
            }
        }
    }));
});
</script>
@endsection
