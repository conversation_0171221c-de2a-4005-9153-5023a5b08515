<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\User;
use App\Events\MessageSent;
use App\Events\TypingIndicator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ChatController extends Controller
{
    /**
     * Start a new chat session
     */
    public function startSession(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'visitor_name' => 'nullable|string|max:255',
            'visitor_email' => 'nullable|email|max:255',
            'visitor_phone' => 'nullable|string|max:20',
            'initial_message' => 'nullable|string|max:1000',
            'source_page' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            // Generate visitor ID if not authenticated
            $visitorId = $request->user() ? null : 'visitor_' . Str::random(16);

            $session = ChatSession::create([
                'visitor_id' => $visitorId,
                'user_id' => $request->user()?->id,
                'status' => 'active',
                'visitor_name' => $request->visitor_name,
                'visitor_email' => $request->visitor_email,
                'visitor_phone' => $request->visitor_phone,
                'initial_message' => $request->initial_message,
                'source_page' => $request->source_page,
                'visitor_info' => [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'referer' => $request->header('referer'),
                ],
                'started_at' => now(),
                'last_activity_at' => now(),
            ]);

            // Create initial message if provided
            if ($request->initial_message) {
                ChatMessage::create([
                    'chat_session_id' => $session->id,
                    'sender_type' => $request->user() ? 'user' : 'visitor',
                    'sender_id' => $request->user()?->id,
                    'sender_name' => $request->visitor_name ?? $request->user()?->name,
                    'message_type' => 'text',
                    'message' => $request->initial_message,
                    'sent_at' => now(),
                ]);
            }

            // Send welcome bot message
            $this->sendBotWelcomeMessage($session);

            return response()->json([
                'success' => true,
                'message' => 'Chat session started successfully',
                'data' => [
                    'session' => $session->load('messages'),
                    'visitor_id' => $visitorId,
                ],
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to start chat session: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send a message in a chat session
     */
    public function sendMessage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string|exists:chat_sessions,session_id',
            'message' => 'required|string|max:2000',
            'message_type' => 'nullable|in:text,file,image',
            'sender_name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $session = ChatSession::where('session_id', $request->session_id)->first();

            if ($session->isClosed()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat session is closed',
                ], 400);
            }

            $message = ChatMessage::create([
                'chat_session_id' => $session->id,
                'sender_type' => $request->user() ? 'agent' : 'visitor',
                'sender_id' => $request->user()?->id,
                'sender_name' => $request->sender_name ?? $request->user()?->name,
                'message_type' => $request->message_type ?? 'text',
                'message' => $request->message,
                'sent_at' => now(),
            ]);

            // Update session activity
            $session->updateActivity();

            // Handle bot responses for visitor messages
            if (!$request->user()) {
                $this->handleBotResponse($session, $request->message);
            }

            // Message will be automatically broadcasted by the model

            return response()->json([
                'success' => true,
                'message' => 'Message sent successfully',
                'data' => $message->load('chatSession'),
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send message: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get chat session with messages
     */
    public function getSession(Request $request, string $sessionId): JsonResponse
    {
        try {
            $session = ChatSession::where('session_id', $sessionId)
                ->with(['messages' => function ($query) {
                    $query->where('is_internal', false)->orderBy('sent_at');
                }, 'agent'])
                ->first();

            if (!$session) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat session not found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $session,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve session: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get chat messages for a session
     */
    public function getMessages(Request $request, string $sessionId): JsonResponse
    {
        try {
            $session = ChatSession::where('session_id', $sessionId)->first();

            if (!$session) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat session not found',
                ], 404);
            }

            $messages = $session->messages()
                ->where('is_internal', false)
                ->orderBy('sent_at')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $messages,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve messages: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Close a chat session
     */
    public function closeSession(Request $request, string $sessionId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'nullable|string|max:500',
            'rating' => 'nullable|numeric|min:1|max:5',
            'feedback' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $session = ChatSession::where('session_id', $sessionId)->first();

            if (!$session) {
                return response()->json([
                    'success' => false,
                    'message' => 'Chat session not found',
                ], 404);
            }

            $session->update([
                'status' => 'closed',
                'closed_at' => now(),
                'closing_reason' => $request->reason,
                'satisfaction_rating' => $request->rating,
                'feedback' => $request->feedback,
                'is_resolved' => true,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Chat session closed successfully',
                'data' => $session,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to close session: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send typing indicator
     */
    public function sendTypingIndicator(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string|exists:chat_sessions,session_id',
            'is_typing' => 'required|boolean',
            'sender_name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $senderType = $request->user() ? 'agent' : 'visitor';
            $senderName = $request->sender_name ?? ($request->user()?->name ?? 'Visitor');

            broadcast(new TypingIndicator(
                $request->session_id,
                $senderType,
                $senderName,
                $request->is_typing
            ))->toOthers();

            return response()->json([
                'success' => true,
                'message' => 'Typing indicator sent',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send typing indicator: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Send bot welcome message
     */
    private function sendBotWelcomeMessage(ChatSession $session): void
    {
        ChatMessage::create([
            'chat_session_id' => $session->id,
            'sender_type' => 'bot',
            'sender_name' => 'Bhavitech Bot',
            'message_type' => 'text',
            'message' => "Hi! Welcome to Bhavitech. I'm here to help you with any questions about our services. How can I assist you today?",
            'sent_at' => now(),
        ]);
    }

    /**
     * Handle bot responses to visitor messages
     */
    private function handleBotResponse(ChatSession $session, string $userMessage): void
    {
        $message = strtolower($userMessage);
        $response = $this->generateBotResponse($message);

        if ($response) {
            ChatMessage::create([
                'chat_session_id' => $session->id,
                'sender_type' => 'bot',
                'sender_name' => 'Bhavitech Bot',
                'message_type' => 'text',
                'message' => $response,
                'sent_at' => now(),
            ]);
        }
    }

    /**
     * Generate bot response based on user message
     */
    private function generateBotResponse(string $message): ?string
    {
        if (str_contains($message, 'price') || str_contains($message, 'cost') || str_contains($message, 'quote')) {
            return "I'd be happy to help with pricing! Our services are customized based on your specific needs. Please visit our quote page or I can connect you with our sales team for a detailed quote.";
        }

        if (str_contains($message, 'service') || str_contains($message, 'what do you do')) {
            return "We offer comprehensive digital solutions including:\n• Web Development\n• Mobile App Development\n• Digital Marketing\n• Graphic Design\n\nWhich service interests you most?";
        }

        if (str_contains($message, 'contact') || str_contains($message, 'phone') || str_contains($message, 'email')) {
            return "You can reach us at:\n📞 +91 7010860889\n📧 <EMAIL>\n\nOur team is available Monday to Friday, 9 AM to 6 PM. Would you like me to connect you with a specialist?";
        }

        if (str_contains($message, 'hello') || str_contains($message, 'hi') || str_contains($message, 'hey')) {
            return "Hello! Great to meet you. I'm here to help you learn about our digital services. What would you like to know about Bhavitech?";
        }

        if (str_contains($message, 'help') || str_contains($message, 'support')) {
            return "I'm here to help! You can ask me about:\n• Our services and pricing\n• How to get started\n• Contact information\n• Project timelines\n\nWhat would you like to know?";
        }

        // Default response
        return "Thanks for your message! I'll make sure our team gets back to you soon. For immediate assistance, please call +91 7010860889 <NAME_EMAIL>.";
    }
}
