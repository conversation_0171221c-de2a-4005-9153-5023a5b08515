<?php

namespace App\Services;

use App\Models\AnalyticsEvent;
use App\Models\Lead;
use App\Models\EmailCampaign;
use App\Models\WhatsAppCampaign;
use App\Models\SocialMediaPost;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class AnalyticsService
{
    /**
     * Get dashboard analytics overview
     */
    public function getDashboardOverview(): array
    {
        return [
            'leads' => $this->getLeadAnalytics(),
            'email_marketing' => $this->getEmailMarketingAnalytics(),
            'whatsapp_marketing' => $this->getWhatsAppMarketingAnalytics(),
            'social_media' => $this->getSocialMediaAnalytics(),
            'conversion_funnel' => $this->getConversionFunnelData(),
            'traffic_sources' => $this->getTrafficSources(),
        ];
    }

    /**
     * Get lead analytics
     */
    public function getLeadAnalytics(): array
    {
        $totalLeads = Lead::count();
        $newLeadsToday = Lead::whereDate('created_at', today())->count();
        $newLeadsThisWeek = Lead::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count();
        $newLeadsThisMonth = Lead::whereMonth('created_at', now()->month)->count();

        $conversionRate = $totalLeads > 0 ? 
            (Lead::where('status', 'converted')->count() / $totalLeads) * 100 : 0;

        $averageScore = Lead::avg('score') ?? 0;

        return [
            'total_leads' => $totalLeads,
            'new_leads_today' => $newLeadsToday,
            'new_leads_this_week' => $newLeadsThisWeek,
            'new_leads_this_month' => $newLeadsThisMonth,
            'conversion_rate' => round($conversionRate, 2),
            'average_score' => round($averageScore, 1),
            'lead_distribution' => $this->getLeadDistribution(),
        ];
    }

    /**
     * Get email marketing analytics
     */
    public function getEmailMarketingAnalytics(): array
    {
        $totalCampaigns = EmailCampaign::count();
        $activeCampaigns = EmailCampaign::where('status', 'sending')->count();
        $completedCampaigns = EmailCampaign::where('status', 'sent')->count();

        $totalSent = EmailCampaign::sum('sent_count') ?? 0;
        $totalOpened = EmailCampaign::sum('opened_count') ?? 0;
        $totalClicked = EmailCampaign::sum('clicked_count') ?? 0;

        $openRate = $totalSent > 0 ? ($totalOpened / $totalSent) * 100 : 0;
        $clickRate = $totalSent > 0 ? ($totalClicked / $totalSent) * 100 : 0;

        return [
            'total_campaigns' => $totalCampaigns,
            'active_campaigns' => $activeCampaigns,
            'completed_campaigns' => $completedCampaigns,
            'total_sent' => $totalSent,
            'total_opened' => $totalOpened,
            'total_clicked' => $totalClicked,
            'open_rate' => round($openRate, 2),
            'click_rate' => round($clickRate, 2),
        ];
    }

    /**
     * Get WhatsApp marketing analytics
     */
    public function getWhatsAppMarketingAnalytics(): array
    {
        $totalCampaigns = WhatsAppCampaign::count();
        $activeCampaigns = WhatsAppCampaign::where('status', 'sending')->count();
        $completedCampaigns = WhatsAppCampaign::where('status', 'sent')->count();

        $totalSent = WhatsAppCampaign::sum('sent_count') ?? 0;
        $totalDelivered = WhatsAppCampaign::sum('delivered_count') ?? 0;
        $totalRead = WhatsAppCampaign::sum('read_count') ?? 0;

        $deliveryRate = $totalSent > 0 ? ($totalDelivered / $totalSent) * 100 : 0;
        $readRate = $totalSent > 0 ? ($totalRead / $totalSent) * 100 : 0;

        return [
            'total_campaigns' => $totalCampaigns,
            'active_campaigns' => $activeCampaigns,
            'completed_campaigns' => $completedCampaigns,
            'total_sent' => $totalSent,
            'total_delivered' => $totalDelivered,
            'total_read' => $totalRead,
            'delivery_rate' => round($deliveryRate, 2),
            'read_rate' => round($readRate, 2),
        ];
    }

    /**
     * Get social media analytics
     */
    public function getSocialMediaAnalytics(): array
    {
        $totalPosts = SocialMediaPost::count();
        $publishedPosts = SocialMediaPost::where('status', 'published')->count();
        $scheduledPosts = SocialMediaPost::where('status', 'scheduled')->count();

        $posts = SocialMediaPost::where('status', 'published')->get();
        
        $totalEngagement = $posts->sum(function ($post) {
            $analytics = $post->analytics ?? [];
            return ($analytics['likes'] ?? 0) + 
                   ($analytics['comments'] ?? 0) + 
                   ($analytics['shares'] ?? 0);
        });

        $totalReach = $posts->sum(function ($post) {
            return $post->analytics['reach'] ?? 0;
        });

        return [
            'total_posts' => $totalPosts,
            'published_posts' => $publishedPosts,
            'scheduled_posts' => $scheduledPosts,
            'total_engagement' => $totalEngagement,
            'total_reach' => $totalReach,
            'average_engagement_per_post' => $publishedPosts > 0 ? 
                round($totalEngagement / $publishedPosts, 1) : 0,
        ];
    }

    /**
     * Get conversion funnel data
     */
    public function getConversionFunnelData(): array
    {
        $visitors = AnalyticsEvent::where('event_type', 'page_view')->distinct('session_id')->count();
        $leads = Lead::count();
        $qualified = Lead::where('status', 'qualified')->count();
        $proposals = Lead::where('status', 'proposal_sent')->count();
        $converted = Lead::where('status', 'converted')->count();

        return [
            'visitors' => $visitors,
            'leads' => $leads,
            'qualified' => $qualified,
            'proposals' => $proposals,
            'converted' => $converted,
            'visitor_to_lead_rate' => $visitors > 0 ? round(($leads / $visitors) * 100, 2) : 0,
            'lead_to_qualified_rate' => $leads > 0 ? round(($qualified / $leads) * 100, 2) : 0,
            'qualified_to_proposal_rate' => $qualified > 0 ? round(($proposals / $qualified) * 100, 2) : 0,
            'proposal_to_conversion_rate' => $proposals > 0 ? round(($converted / $proposals) * 100, 2) : 0,
        ];
    }

    /**
     * Get traffic sources
     */
    public function getTrafficSources(): array
    {
        return AnalyticsEvent::where('event_type', 'page_view')
            ->selectRaw('source, COUNT(*) as visits')
            ->groupBy('source')
            ->orderByDesc('visits')
            ->limit(10)
            ->get()
            ->toArray();
    }

    /**
     * Get lead distribution by score
     */
    protected function getLeadDistribution(): array
    {
        return [
            'hot' => Lead::where('score', '>=', 80)->count(),
            'warm' => Lead::whereBetween('score', [60, 79])->count(),
            'qualified' => Lead::whereBetween('score', [40, 59])->count(),
            'cold' => Lead::whereBetween('score', [20, 39])->count(),
            'unqualified' => Lead::where('score', '<', 20)->count(),
        ];
    }

    /**
     * Track analytics event
     */
    public function trackEvent(array $data): AnalyticsEvent
    {
        return AnalyticsEvent::create([
            'event_type' => $data['event_type'],
            'event_data' => $data['event_data'] ?? [],
            'user_id' => $data['user_id'] ?? null,
            'session_id' => $data['session_id'] ?? null,
            'ip_address' => $data['ip_address'] ?? null,
            'user_agent' => $data['user_agent'] ?? null,
            'source' => $data['source'] ?? 'direct',
            'medium' => $data['medium'] ?? null,
            'campaign' => $data['campaign'] ?? null,
        ]);
    }

    /**
     * Get real-time analytics
     */
    public function getRealTimeAnalytics(): array
    {
        $now = now();
        $last24Hours = $now->copy()->subHours(24);

        return [
            'active_visitors' => AnalyticsEvent::where('created_at', '>=', $now->copy()->subMinutes(30))
                ->distinct('session_id')
                ->count(),
            'page_views_24h' => AnalyticsEvent::where('event_type', 'page_view')
                ->where('created_at', '>=', $last24Hours)
                ->count(),
            'new_leads_24h' => Lead::where('created_at', '>=', $last24Hours)->count(),
            'recent_events' => AnalyticsEvent::with('user')
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get(),
        ];
    }

    /**
     * Generate custom report
     */
    public function generateCustomReport(array $filters): array
    {
        $startDate = Carbon::parse($filters['start_date'] ?? now()->subDays(30));
        $endDate = Carbon::parse($filters['end_date'] ?? now());

        return [
            'period' => [
                'start' => $startDate->toDateString(),
                'end' => $endDate->toDateString(),
            ],
            'leads' => $this->getLeadAnalyticsForPeriod($startDate, $endDate),
            'campaigns' => $this->getCampaignAnalyticsForPeriod($startDate, $endDate),
            'traffic' => $this->getTrafficAnalyticsForPeriod($startDate, $endDate),
        ];
    }

    /**
     * Get lead analytics for specific period
     */
    protected function getLeadAnalyticsForPeriod(Carbon $start, Carbon $end): array
    {
        $leads = Lead::whereBetween('created_at', [$start, $end]);

        return [
            'total' => $leads->count(),
            'converted' => $leads->where('status', 'converted')->count(),
            'by_source' => $leads->selectRaw('source, COUNT(*) as count')
                ->groupBy('source')
                ->get()
                ->pluck('count', 'source')
                ->toArray(),
        ];
    }

    /**
     * Get campaign analytics for specific period
     */
    protected function getCampaignAnalyticsForPeriod(Carbon $start, Carbon $end): array
    {
        $emailCampaigns = EmailCampaign::whereBetween('created_at', [$start, $end]);
        $whatsappCampaigns = WhatsAppCampaign::whereBetween('created_at', [$start, $end]);

        return [
            'email_campaigns' => $emailCampaigns->count(),
            'whatsapp_campaigns' => $whatsappCampaigns->count(),
            'total_emails_sent' => $emailCampaigns->sum('sent_count'),
            'total_whatsapp_sent' => $whatsappCampaigns->sum('sent_count'),
        ];
    }

    /**
     * Get traffic analytics for specific period
     */
    protected function getTrafficAnalyticsForPeriod(Carbon $start, Carbon $end): array
    {
        return [
            'page_views' => AnalyticsEvent::where('event_type', 'page_view')
                ->whereBetween('created_at', [$start, $end])
                ->count(),
            'unique_visitors' => AnalyticsEvent::where('event_type', 'page_view')
                ->whereBetween('created_at', [$start, $end])
                ->distinct('session_id')
                ->count(),
        ];
    }
}
