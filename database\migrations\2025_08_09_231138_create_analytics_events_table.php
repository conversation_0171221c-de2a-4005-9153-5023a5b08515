<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('analytics_events', function (Blueprint $table) {
            $table->id();
            $table->string('event_name'); // page_view, form_submit, button_click, etc.
            $table->string('event_category')->nullable(); // marketing, engagement, conversion
            $table->string('event_action')->nullable(); // click, submit, download
            $table->string('event_label')->nullable(); // Additional context
            $table->decimal('event_value', 10, 2)->nullable(); // Monetary value
            $table->string('session_id')->nullable(); // User session identifier
            $table->string('user_id')->nullable(); // Authenticated user ID
            $table->string('visitor_id')->nullable(); // Anonymous visitor ID
            $table->string('page_url');
            $table->string('page_title')->nullable();
            $table->string('referrer_url')->nullable();
            $table->string('utm_source')->nullable();
            $table->string('utm_medium')->nullable();
            $table->string('utm_campaign')->nullable();
            $table->string('utm_term')->nullable();
            $table->string('utm_content')->nullable();
            $table->string('device_type')->nullable(); // desktop, mobile, tablet
            $table->string('browser')->nullable();
            $table->string('operating_system')->nullable();
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->string('ip_address')->nullable();
            $table->json('custom_properties')->nullable(); // Additional event data
            $table->timestamp('event_timestamp');
            $table->timestamps();

            $table->index(['event_name', 'event_timestamp']);
            $table->index(['session_id', 'event_timestamp']);
            $table->index(['user_id', 'event_timestamp']);
            $table->index(['utm_campaign', 'event_timestamp']);
            $table->index('event_timestamp');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('analytics_events');
    }
};
