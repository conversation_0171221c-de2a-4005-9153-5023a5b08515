<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_sessions', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->unique(); // Unique session identifier
            $table->string('visitor_id')->nullable(); // Anonymous visitor tracking
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null'); // Authenticated user
            $table->foreignId('agent_id')->nullable()->constrained('users')->onDelete('set null'); // Assigned agent
            $table->enum('status', ['active', 'waiting', 'assigned', 'closed', 'abandoned'])->default('active');
            $table->string('visitor_name')->nullable(); // Visitor's provided name
            $table->string('visitor_email')->nullable(); // Visitor's provided email
            $table->string('visitor_phone')->nullable(); // Visitor's provided phone
            $table->text('initial_message')->nullable(); // First message from visitor
            $table->string('source_page')->nullable(); // Page where chat was initiated
            $table->json('visitor_info')->nullable(); // Browser, IP, location data
            $table->timestamp('started_at')->useCurrent();
            $table->timestamp('last_activity_at')->useCurrent();
            $table->timestamp('assigned_at')->nullable();
            $table->timestamp('closed_at')->nullable();
            $table->text('closing_reason')->nullable();
            $table->integer('message_count')->default(0);
            $table->integer('agent_response_time')->nullable(); // Average response time in seconds
            $table->boolean('is_resolved')->default(false);
            $table->decimal('satisfaction_rating', 2, 1)->nullable(); // 1-5 rating
            $table->text('feedback')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index(['agent_id', 'status']);
            $table->index(['visitor_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index('last_activity_at');
            $table->index('session_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_sessions');
    }
};
