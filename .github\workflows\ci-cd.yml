name: CI/CD Pipeline for 1 Crore Email Management System

on:
  push:
    branches: [ main, development ]
  pull_request:
    branches: [ main, development ]
  release:
    types: [ published ]

env:
  PHP_VERSION: '8.2'
  NODE_VERSION: '18'
  MYSQL_VERSION: '8.0'

jobs:
  # Code Quality and Testing
  test:
    name: 🧪 Tests & Code Quality
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: bhavitech_emails_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐘 Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv, imagick, redis, mysql
        coverage: xdebug

    - name: 📦 Cache Composer dependencies
      uses: actions/cache@v3
      with:
        path: ~/.composer/cache/files
        key: dependencies-composer-${{ hashFiles('composer.json') }}

    - name: 🎼 Install Composer dependencies
      run: composer install --no-progress --prefer-dist --optimize-autoloader

    - name: 📋 Copy environment file
      run: cp .env.example .env

    - name: 🔑 Generate application key
      run: php artisan key:generate

    - name: 🗄️ Run database migrations
      run: php artisan migrate --force
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: bhavitech_emails_test
        DB_USERNAME: root
        DB_PASSWORD: password

    - name: 🌱 Seed database
      run: php artisan db:seed --force

    - name: 🧪 Run PHPUnit tests
      run: vendor/bin/phpunit --coverage-clover coverage.xml

    - name: 📊 Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

    - name: 🔍 Run PHP CS Fixer
      run: vendor/bin/php-cs-fixer fix --dry-run --diff

    - name: 🔎 Run PHPStan
      run: vendor/bin/phpstan analyse

  # Frontend Build and Testing
  frontend:
    name: 🎨 Frontend Build
    runs-on: ubuntu-latest

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🟢 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: 📦 Install dependencies
      run: npm ci

    - name: 🏗️ Build assets
      run: npm run build

    - name: 🧪 Run frontend tests
      run: npm test

    - name: 📊 Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: frontend-build
        path: public/build/

  # Security Scanning
  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🔍 Run security audit
      run: |
        composer audit
        npm audit

    - name: 🛡️ Run Snyk security scan
      uses: snyk/actions/php@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

  # Performance Testing
  performance:
    name: ⚡ Performance Tests
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐘 Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}

    - name: 📦 Install dependencies
      run: composer install --no-dev --optimize-autoloader

    - name: ⚡ Run performance tests
      run: |
        php artisan mysql:maintenance --stats
        php artisan queue:work --once --verbose

  # Database Migration Testing
  migration:
    name: 🗄️ Migration Tests
    runs-on: ubuntu-latest

    strategy:
      matrix:
        mysql-version: ['8.0', '5.7']

    services:
      mysql:
        image: mysql:${{ matrix.mysql-version }}
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: migration_test
        ports:
          - 3306:3306

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐘 Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}

    - name: 📦 Install dependencies
      run: composer install

    - name: 🗄️ Test migrations
      run: |
        php artisan migrate:fresh --force
        php artisan migrate:rollback --force
        php artisan migrate --force

  # Deployment to Staging
  deploy-staging:
    name: 🚀 Deploy to Staging
    runs-on: ubuntu-latest
    needs: [test, frontend, security]
    if: github.ref == 'refs/heads/development'

    environment:
      name: staging
      url: https://staging.bhavitech.com

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🚀 Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here

  # Deployment to Production
  deploy-production:
    name: 🌟 Deploy to Production
    runs-on: ubuntu-latest
    needs: [test, frontend, security, performance]
    if: github.ref == 'refs/heads/main'

    environment:
      name: production
      url: https://bhavitech.com

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🌟 Deploy to production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here

    - name: 📧 Notify deployment
      run: |
        echo "Production deployment completed successfully!"
        # Add notification logic here

  # Email System Health Check
  health-check:
    name: 🏥 Email System Health Check
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: 🏥 Check email system health
      run: |
        echo "Checking email system health..."
        # Add health check commands here
        curl -f https://bhavitech.com/health || exit 1

    - name: 📊 Check database performance
      run: |
        echo "Checking database performance..."
        # Add database performance checks here

    - name: 📧 Verify email delivery
      run: |
        echo "Verifying email delivery capabilities..."
        # Add email delivery verification here
