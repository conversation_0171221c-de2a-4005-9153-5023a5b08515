<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EmailTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'category',
        'status',
        'subject',
        'preview_text',
        'content',
        'layout',
        'primary_color',
        'secondary_color',
        'background_color',
        'font_family',
        'font_size',
        'track_opens',
        'track_clicks',
        'include_unsubscribe',
        'tags',
        'custom_variables',
        'enable_ab_testing',
        'variant_b_subject',
        'variant_b_content',
        'test_percentage',
        'type',
        'variables',
        'is_active',
        'usage_count',
        'created_by',
    ];

    protected $casts = [
        'variables' => 'array',
        'tags' => 'array',
        'custom_variables' => 'array',
        'is_active' => 'boolean',
        'track_opens' => 'boolean',
        'track_clicks' => 'boolean',
        'include_unsubscribe' => 'boolean',
        'enable_ab_testing' => 'boolean',
        'test_percentage' => 'integer',
    ];

    // Relationships
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function campaigns(): HasMany
    {
        return $this->hasMany(EmailCampaign::class, 'template_id');
    }

    public function emailCampaigns(): HasMany
    {
        return $this->hasMany(EmailCampaign::class, 'template_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopePopular($query)
    {
        return $query->orderBy('usage_count', 'desc');
    }

    // Methods
    public function renderContent(array $variables = []): string
    {
        $content = $this->content;

        // Replace template variables
        foreach ($variables as $key => $value) {
            $content = str_replace("{{$key}}", $value, $content);
        }

        // Replace default variables if not provided
        $defaultVariables = $this->getDefaultVariables();
        foreach ($defaultVariables as $key => $value) {
            if (!isset($variables[$key])) {
                $content = str_replace("{{$key}}", $value, $content);
            }
        }

        return $content;
    }

    public function renderSubject(array $variables = []): string
    {
        $subject = $this->subject;

        foreach ($variables as $key => $value) {
            $subject = str_replace("{{$key}}", $value, $subject);
        }

        return $subject;
    }

    protected function getDefaultVariables(): array
    {
        return [
            'company_name' => BusinessSetting::get('company_name', 'Bhavitech'),
            'company_address' => BusinessSetting::get('company_address', 'Salem, Tamil Nadu'),
            'company_phone' => BusinessSetting::get('company_phone', '7010860889'),
            'company_email' => BusinessSetting::get('company_email', '<EMAIL>'),
            'current_year' => date('Y'),
            'current_date' => date('F j, Y'),
        ];
    }

    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    public function getAvailableVariables(): array
    {
        return array_merge(
            $this->variables ?? [],
            array_keys($this->getDefaultVariables()),
            [
                'recipient_name',
                'recipient_email',
                'recipient_company',
                'unsubscribe_url',
                'tracking_pixel',
            ]
        );
    }

    public function getAverageOpenRate(): float
    {
        $campaigns = $this->emailCampaigns()->where('status', 'sent')->get();

        if ($campaigns->isEmpty()) {
            return 0;
        }

        $totalSent = $campaigns->sum('sent_count');
        $totalOpened = $campaigns->sum('opened_count');

        if ($totalSent === 0) {
            return 0;
        }

        return ($totalOpened / $totalSent) * 100;
    }

    public function getAverageClickRate(): float
    {
        $campaigns = $this->emailCampaigns()->where('status', 'sent')->get();

        if ($campaigns->isEmpty()) {
            return 0;
        }

        $totalSent = $campaigns->sum('sent_count');
        $totalClicked = $campaigns->sum('clicked_count');

        if ($totalSent === 0) {
            return 0;
        }

        return ($totalClicked / $totalSent) * 100;
    }

    public static function getDefaultTemplates(): array
    {
        return [
            [
                'name' => 'Welcome Email',
                'subject' => 'Welcome to {{company_name}} - Let\'s Transform Your Business!',
                'content' => self::getWelcomeTemplate(),
                'type' => 'welcome',
                'category' => 'customer_onboarding',
                'variables' => ['recipient_name', 'service_interest'],
                'is_active' => true,
            ],
            [
                'name' => 'Lead Nurture - Web Development',
                'subject' => 'Transform Your Online Presence with Professional Web Development',
                'content' => self::getWebDevelopmentNurtureTemplate(),
                'type' => 'nurture',
                'category' => 'lead_nurture',
                'variables' => ['recipient_name', 'recipient_company'],
                'is_active' => true,
            ],
            [
                'name' => 'Follow-up After Quote',
                'subject' => 'Your {{company_name}} Project Quote - Next Steps',
                'content' => self::getQuoteFollowupTemplate(),
                'type' => 'follow_up',
                'category' => 'sales',
                'variables' => ['recipient_name', 'quote_amount', 'project_type'],
                'is_active' => true,
            ],
        ];
    }

    protected static function getWelcomeTemplate(): string
    {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h1 style="color: #2563eb;">Welcome to {{company_name}}!</h1>

            <p>Dear {{recipient_name}},</p>

            <p>Thank you for your interest in our {{service_interest}} services. We\'re excited to help transform your business with cutting-edge digital solutions.</p>

            <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>What happens next?</h3>
                <ul>
                    <li>Our team will review your requirements</li>
                    <li>We\'ll prepare a customized proposal</li>
                    <li>Schedule a consultation call</li>
                    <li>Begin your digital transformation journey</li>
                </ul>
            </div>

            <p>In the meantime, feel free to explore our portfolio and case studies on our website.</p>

            <p>Best regards,<br>The {{company_name}} Team</p>

            <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; font-size: 12px; color: #6b7280;">
                <p>{{company_name}} | {{company_address}} | {{company_phone}}</p>
                <p><a href="{{unsubscribe_url}}">Unsubscribe</a></p>
            </div>
        </div>';
    }

    protected static function getWebDevelopmentNurtureTemplate(): string
    {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h1 style="color: #2563eb;">Ready to Elevate Your Online Presence?</h1>

            <p>Hi {{recipient_name}},</p>

            <p>In today\'s digital world, your website is often the first impression potential customers have of {{recipient_company}}. Is your current website helping or hurting your business?</p>

            <div style="background: #dbeafe; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>🚀 Why Choose {{company_name}} for Web Development?</h3>
                <ul>
                    <li><strong>Modern Design:</strong> Responsive, mobile-first designs</li>
                    <li><strong>Performance:</strong> Lightning-fast loading speeds</li>
                    <li><strong>SEO Optimized:</strong> Built for search engine success</li>
                    <li><strong>Conversion Focused:</strong> Designed to turn visitors into customers</li>
                </ul>
            </div>

            <p><strong>Special Offer:</strong> Book a free consultation this month and receive a complimentary website audit worth ₹5,000!</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="tel:{{company_phone}}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Schedule Free Consultation</a>
            </div>

            <p>Best regards,<br>{{company_name}} Web Development Team</p>
        </div>';
    }

    protected static function getQuoteFollowupTemplate(): string
    {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h1 style="color: #2563eb;">Your {{project_type}} Project Quote</h1>

            <p>Dear {{recipient_name}},</p>

            <p>Thank you for considering {{company_name}} for your {{project_type}} project. We\'ve prepared a detailed quote of ₹{{quote_amount}} based on your requirements.</p>

            <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #22c55e;">
                <h3>✅ What\'s Included:</h3>
                <ul>
                    <li>Complete project development</li>
                    <li>Quality assurance testing</li>
                    <li>30 days of free support</li>
                    <li>Training and documentation</li>
                </ul>
            </div>

            <p><strong>Limited Time:</strong> Accept this quote within 7 days and receive a 10% discount!</p>

            <p>Have questions? I\'m here to help. Reply to this email or call me directly at {{company_phone}}.</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="mailto:{{company_email}}" style="background: #22c55e; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Accept Quote</a>
            </div>

            <p>Looking forward to working with you!</p>

            <p>Best regards,<br>{{company_name}} Team</p>
        </div>';
    }
}
