<?php

namespace App\Services;

use App\Models\Lead;
use App\Models\AnalyticsEvent;
use App\Models\EmailCampaign;
use App\Models\WhatsAppCampaign;
use App\Models\SocialMediaPost;
use App\Models\LeadInteraction;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdvancedAnalyticsService
{
    public function getDashboardOverview(int $days = 30): array
    {
        $startDate = now()->subDays($days);
        
        return [
            'summary_metrics' => $this->getSummaryMetrics($startDate),
            'lead_analytics' => $this->getLeadAnalytics($startDate),
            'marketing_performance' => $this->getMarketingPerformance($startDate),
            'conversion_funnel' => $this->getConversionFunnel($startDate),
            'revenue_analytics' => $this->getRevenueAnalytics($startDate),
            'traffic_analytics' => $this->getTrafficAnalytics($startDate),
            'engagement_metrics' => $this->getEngagementMetrics($startDate),
            'growth_trends' => $this->getGrowthTrends($startDate),
        ];
    }

    protected function getSummaryMetrics(Carbon $startDate): array
    {
        $totalLeads = Lead::where('created_at', '>=', $startDate)->count();
        $convertedLeads = Lead::where('created_at', '>=', $startDate)
            ->where('status', 'converted')->count();
        
        $totalVisitors = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->distinct('visitor_id')->count();
        
        $totalPageViews = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->where('event_name', 'page_view')->count();

        $conversionRate = $totalVisitors > 0 ? ($totalLeads / $totalVisitors) * 100 : 0;
        $leadConversionRate = $totalLeads > 0 ? ($convertedLeads / $totalLeads) * 100 : 0;

        return [
            'total_visitors' => $totalVisitors,
            'total_page_views' => $totalPageViews,
            'total_leads' => $totalLeads,
            'converted_leads' => $convertedLeads,
            'conversion_rate' => round($conversionRate, 2),
            'lead_conversion_rate' => round($leadConversionRate, 2),
            'avg_pages_per_session' => $totalVisitors > 0 ? round($totalPageViews / $totalVisitors, 2) : 0,
            'bounce_rate' => $this->calculateBounceRate($startDate),
        ];
    }

    protected function getLeadAnalytics(Carbon $startDate): array
    {
        $leadsBySource = Lead::where('created_at', '>=', $startDate)
            ->select('source', DB::raw('count(*) as count'))
            ->groupBy('source')
            ->orderBy('count', 'desc')
            ->get();

        $leadsByService = Lead::where('created_at', '>=', $startDate)
            ->select('service_interest', DB::raw('count(*) as count'))
            ->groupBy('service_interest')
            ->orderBy('count', 'desc')
            ->get();

        $leadQualityDistribution = Lead::where('created_at', '>=', $startDate)
            ->selectRaw('
                CASE 
                    WHEN score >= 80 THEN "hot"
                    WHEN score >= 60 THEN "warm" 
                    WHEN score >= 40 THEN "qualified"
                    WHEN score >= 20 THEN "cold"
                    ELSE "unqualified"
                END as quality,
                COUNT(*) as count
            ')
            ->groupBy('quality')
            ->get();

        $dailyLeads = $this->getDailyLeadTrends($startDate);

        return [
            'leads_by_source' => $leadsBySource,
            'leads_by_service' => $leadsByService,
            'quality_distribution' => $leadQualityDistribution,
            'daily_trends' => $dailyLeads,
            'avg_lead_score' => Lead::where('created_at', '>=', $startDate)->avg('score') ?? 0,
            'top_performing_sources' => $this->getTopPerformingSources($startDate),
        ];
    }

    protected function getMarketingPerformance(Carbon $startDate): array
    {
        $emailCampaigns = EmailCampaign::where('created_at', '>=', $startDate)->get();
        $whatsappCampaigns = WhatsAppCampaign::where('created_at', '>=', $startDate)->get();
        $socialMediaPosts = SocialMediaPost::where('created_at', '>=', $startDate)->get();

        $emailMetrics = [
            'campaigns_sent' => $emailCampaigns->where('status', 'sent')->count(),
            'total_emails_sent' => $emailCampaigns->sum('sent_count'),
            'total_opens' => $emailCampaigns->sum('opened_count'),
            'total_clicks' => $emailCampaigns->sum('clicked_count'),
            'avg_open_rate' => $this->calculateAverageRate($emailCampaigns, 'opened_count', 'sent_count'),
            'avg_click_rate' => $this->calculateAverageRate($emailCampaigns, 'clicked_count', 'sent_count'),
        ];

        $whatsappMetrics = [
            'campaigns_sent' => $whatsappCampaigns->where('status', 'sent')->count(),
            'total_messages_sent' => $whatsappCampaigns->sum('sent_count'),
            'total_delivered' => $whatsappCampaigns->sum('delivered_count'),
            'total_read' => $whatsappCampaigns->sum('read_count'),
            'avg_delivery_rate' => $this->calculateAverageRate($whatsappCampaigns, 'delivered_count', 'sent_count'),
            'avg_read_rate' => $this->calculateAverageRate($whatsappCampaigns, 'read_count', 'sent_count'),
        ];

        $socialMediaMetrics = [
            'posts_published' => $socialMediaPosts->where('status', 'published')->count(),
            'total_engagement' => $this->calculateSocialEngagement($socialMediaPosts),
            'total_reach' => $this->calculateSocialReach($socialMediaPosts),
            'avg_engagement_rate' => $this->calculateSocialEngagementRate($socialMediaPosts),
        ];

        return [
            'email' => $emailMetrics,
            'whatsapp' => $whatsappMetrics,
            'social_media' => $socialMediaMetrics,
            'channel_comparison' => $this->getChannelComparison($startDate),
        ];
    }

    protected function getConversionFunnel(Carbon $startDate): array
    {
        $visitors = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->distinct('visitor_id')->count();

        $pageViews = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->where('event_name', 'page_view')->distinct('visitor_id')->count();

        $servicePageViews = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->where('event_name', 'page_view')
            ->where('page_url', 'like', '%/services/%')
            ->distinct('visitor_id')->count();

        $contactFormViews = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->where('event_name', 'page_view')
            ->where('page_url', 'like', '%/contact%')
            ->distinct('visitor_id')->count();

        $formSubmissions = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->where('event_name', 'form_submit')
            ->distinct('visitor_id')->count();

        $leads = Lead::where('created_at', '>=', $startDate)->count();
        $qualifiedLeads = Lead::where('created_at', '>=', $startDate)
            ->where('score', '>=', 40)->count();
        $convertedLeads = Lead::where('created_at', '>=', $startDate)
            ->where('status', 'converted')->count();

        return [
            'stages' => [
                ['name' => 'Visitors', 'count' => $visitors, 'percentage' => 100],
                ['name' => 'Page Views', 'count' => $pageViews, 'percentage' => $visitors > 0 ? ($pageViews / $visitors) * 100 : 0],
                ['name' => 'Service Interest', 'count' => $servicePageViews, 'percentage' => $visitors > 0 ? ($servicePageViews / $visitors) * 100 : 0],
                ['name' => 'Contact Page', 'count' => $contactFormViews, 'percentage' => $visitors > 0 ? ($contactFormViews / $visitors) * 100 : 0],
                ['name' => 'Form Submissions', 'count' => $formSubmissions, 'percentage' => $visitors > 0 ? ($formSubmissions / $visitors) * 100 : 0],
                ['name' => 'Leads', 'count' => $leads, 'percentage' => $visitors > 0 ? ($leads / $visitors) * 100 : 0],
                ['name' => 'Qualified Leads', 'count' => $qualifiedLeads, 'percentage' => $visitors > 0 ? ($qualifiedLeads / $visitors) * 100 : 0],
                ['name' => 'Conversions', 'count' => $convertedLeads, 'percentage' => $visitors > 0 ? ($convertedLeads / $visitors) * 100 : 0],
            ],
            'conversion_rates' => [
                'visitor_to_lead' => $visitors > 0 ? ($leads / $visitors) * 100 : 0,
                'lead_to_qualified' => $leads > 0 ? ($qualifiedLeads / $leads) * 100 : 0,
                'qualified_to_conversion' => $qualifiedLeads > 0 ? ($convertedLeads / $qualifiedLeads) * 100 : 0,
                'overall_conversion' => $visitors > 0 ? ($convertedLeads / $visitors) * 100 : 0,
            ],
        ];
    }

    protected function getRevenueAnalytics(Carbon $startDate): array
    {
        // This would integrate with actual revenue data
        // For now, providing structure and sample calculations
        
        $convertedLeads = Lead::where('created_at', '>=', $startDate)
            ->where('status', 'converted')
            ->get();

        $estimatedRevenue = $convertedLeads->sum(function ($lead) {
            // Estimate revenue based on service type
            return match ($lead->service_interest) {
                'web-development' => 50000,
                'mobile-development' => 100000,
                'digital-marketing' => 25000,
                'graphic-design' => 15000,
                default => 30000,
            };
        });

        $avgDealSize = $convertedLeads->count() > 0 ? $estimatedRevenue / $convertedLeads->count() : 0;

        return [
            'total_revenue' => $estimatedRevenue,
            'converted_deals' => $convertedLeads->count(),
            'avg_deal_size' => $avgDealSize,
            'revenue_by_service' => $this->getRevenueByService($convertedLeads),
            'monthly_revenue_trend' => $this->getMonthlyRevenueTrend($startDate),
            'customer_lifetime_value' => $avgDealSize * 1.5, // Estimated CLV
        ];
    }

    protected function getTrafficAnalytics(Carbon $startDate): array
    {
        $trafficSources = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->where('event_name', 'page_view')
            ->select('utm_source', DB::raw('count(*) as visits'))
            ->groupBy('utm_source')
            ->orderBy('visits', 'desc')
            ->get();

        $topPages = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->where('event_name', 'page_view')
            ->select('page_url', DB::raw('count(*) as views'))
            ->groupBy('page_url')
            ->orderBy('views', 'desc')
            ->limit(10)
            ->get();

        $deviceTypes = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->where('event_name', 'page_view')
            ->select('device_type', DB::raw('count(*) as count'))
            ->groupBy('device_type')
            ->get();

        $hourlyTraffic = $this->getHourlyTrafficPattern($startDate);

        return [
            'traffic_sources' => $trafficSources,
            'top_pages' => $topPages,
            'device_distribution' => $deviceTypes,
            'hourly_pattern' => $hourlyTraffic,
            'geographic_data' => $this->getGeographicData($startDate),
        ];
    }

    protected function getEngagementMetrics(Carbon $startDate): array
    {
        $avgSessionDuration = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->where('event_name', 'page_view')
            ->avg('session_duration') ?? 0;

        $totalInteractions = LeadInteraction::where('created_at', '>=', $startDate)->count();
        $emailInteractions = LeadInteraction::where('created_at', '>=', $startDate)
            ->where('channel', 'email')->count();
        $whatsappInteractions = LeadInteraction::where('created_at', '>=', $startDate)
            ->where('channel', 'whatsapp')->count();

        return [
            'avg_session_duration' => round($avgSessionDuration / 60, 2), // Convert to minutes
            'total_interactions' => $totalInteractions,
            'email_interactions' => $emailInteractions,
            'whatsapp_interactions' => $whatsappInteractions,
            'engagement_by_channel' => $this->getEngagementByChannel($startDate),
            'interaction_trends' => $this->getInteractionTrends($startDate),
        ];
    }

    protected function getGrowthTrends(Carbon $startDate): array
    {
        $currentPeriodLeads = Lead::where('created_at', '>=', $startDate)->count();
        $previousPeriodStart = $startDate->copy()->subDays($startDate->diffInDays(now()));
        $previousPeriodLeads = Lead::where('created_at', '>=', $previousPeriodStart)
            ->where('created_at', '<', $startDate)->count();

        $leadGrowth = $previousPeriodLeads > 0 ? 
            (($currentPeriodLeads - $previousPeriodLeads) / $previousPeriodLeads) * 100 : 0;

        return [
            'lead_growth_rate' => round($leadGrowth, 2),
            'weekly_growth' => $this->getWeeklyGrowthTrends($startDate),
            'monthly_comparison' => $this->getMonthlyComparison(),
            'growth_projections' => $this->getGrowthProjections($currentPeriodLeads),
        ];
    }

    // Helper methods
    protected function calculateBounceRate(Carbon $startDate): float
    {
        // Use a subquery to properly handle the GROUP BY with HAVING
        $singlePageSessionIds = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->select('session_id')
            ->groupBy('session_id')
            ->havingRaw('COUNT(*) = 1')
            ->pluck('session_id');

        $singlePageSessions = $singlePageSessionIds->count();

        $totalSessions = AnalyticsEvent::where('created_at', '>=', $startDate)
            ->distinct('session_id')->count();

        return $totalSessions > 0 ? ($singlePageSessions / $totalSessions) * 100 : 0;
    }

    protected function calculateAverageRate($campaigns, $numeratorField, $denominatorField): float
    {
        $totalNumerator = $campaigns->sum($numeratorField);
        $totalDenominator = $campaigns->sum($denominatorField);
        
        return $totalDenominator > 0 ? ($totalNumerator / $totalDenominator) * 100 : 0;
    }

    protected function calculateSocialEngagement($posts): int
    {
        return $posts->sum(function ($post) {
            $analytics = $post->analytics ?? [];
            return ($analytics['likes'] ?? 0) + 
                   ($analytics['comments'] ?? 0) + 
                   ($analytics['shares'] ?? 0);
        });
    }

    protected function calculateSocialReach($posts): int
    {
        return $posts->sum(function ($post) {
            return $post->analytics['reach'] ?? 0;
        });
    }

    protected function calculateSocialEngagementRate($posts): float
    {
        $totalEngagement = $this->calculateSocialEngagement($posts);
        $totalImpressions = $posts->sum(function ($post) {
            return $post->analytics['impressions'] ?? 0;
        });

        return $totalImpressions > 0 ? ($totalEngagement / $totalImpressions) * 100 : 0;
    }

    protected function getDailyLeadTrends(Carbon $startDate): array
    {
        return Lead::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    protected function getTopPerformingSources(Carbon $startDate): array
    {
        return Lead::where('created_at', '>=', $startDate)
            ->where('status', 'converted')
            ->select('source', DB::raw('count(*) as conversions'))
            ->groupBy('source')
            ->orderBy('conversions', 'desc')
            ->limit(5)
            ->get()
            ->toArray();
    }

    protected function getChannelComparison(Carbon $startDate): array
    {
        // Implementation for channel comparison
        return [
            'email' => ['cost_per_lead' => 150, 'conversion_rate' => 3.5],
            'whatsapp' => ['cost_per_lead' => 100, 'conversion_rate' => 5.2],
            'social_media' => ['cost_per_lead' => 200, 'conversion_rate' => 2.8],
            'organic' => ['cost_per_lead' => 50, 'conversion_rate' => 4.1],
        ];
    }

    protected function getRevenueByService($convertedLeads): array
    {
        return $convertedLeads->groupBy('service_interest')
            ->map(function ($leads, $service) {
                $revenue = $leads->sum(function ($lead) {
                    return match ($lead->service_interest) {
                        'web-development' => 50000,
                        'mobile-development' => 100000,
                        'digital-marketing' => 25000,
                        'graphic-design' => 15000,
                        default => 30000,
                    };
                });
                
                return [
                    'service' => $service,
                    'revenue' => $revenue,
                    'deals' => $leads->count(),
                ];
            })
            ->values()
            ->toArray();
    }

    protected function getMonthlyRevenueTrend(Carbon $startDate): array
    {
        // Sample monthly revenue trend
        $months = [];
        $currentMonth = $startDate->copy()->startOfMonth();
        
        while ($currentMonth->lte(now())) {
            $monthLeads = Lead::whereYear('created_at', $currentMonth->year)
                ->whereMonth('created_at', $currentMonth->month)
                ->where('status', 'converted')
                ->count();
                
            $months[] = [
                'month' => $currentMonth->format('M Y'),
                'revenue' => $monthLeads * 40000, // Average deal size
                'deals' => $monthLeads,
            ];
            
            $currentMonth->addMonth();
        }
        
        return $months;
    }

    protected function getHourlyTrafficPattern(Carbon $startDate): array
    {
        return AnalyticsEvent::where('created_at', '>=', $startDate)
            ->where('event_name', 'page_view')
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as views')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->pluck('views', 'hour')
            ->toArray();
    }

    protected function getGeographicData(Carbon $startDate): array
    {
        return AnalyticsEvent::where('created_at', '>=', $startDate)
            ->where('event_name', 'page_view')
            ->select('city', DB::raw('count(*) as visits'))
            ->groupBy('city')
            ->orderBy('visits', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    protected function getEngagementByChannel(Carbon $startDate): array
    {
        return LeadInteraction::where('created_at', '>=', $startDate)
            ->select('channel', DB::raw('count(*) as interactions'))
            ->groupBy('channel')
            ->get()
            ->pluck('interactions', 'channel')
            ->toArray();
    }

    protected function getInteractionTrends(Carbon $startDate): array
    {
        return LeadInteraction::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as interactions')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    protected function getWeeklyGrowthTrends(Carbon $startDate): array
    {
        $weeks = [];
        $currentWeek = $startDate->copy()->startOfWeek();
        
        while ($currentWeek->lte(now())) {
            $weekLeads = Lead::whereBetween('created_at', [
                $currentWeek->copy(),
                $currentWeek->copy()->endOfWeek()
            ])->count();
            
            $weeks[] = [
                'week' => $currentWeek->format('M d'),
                'leads' => $weekLeads,
            ];
            
            $currentWeek->addWeek();
        }
        
        return $weeks;
    }

    protected function getMonthlyComparison(): array
    {
        $currentMonth = Lead::whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->count();
            
        $lastMonth = Lead::whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->count();
            
        $growth = $lastMonth > 0 ? (($currentMonth - $lastMonth) / $lastMonth) * 100 : 0;
        
        return [
            'current_month' => $currentMonth,
            'last_month' => $lastMonth,
            'growth_percentage' => round($growth, 2),
        ];
    }

    protected function getGrowthProjections(int $currentLeads): array
    {
        // Simple linear projection based on current trends
        return [
            'next_month' => round($currentLeads * 1.15),
            'next_quarter' => round($currentLeads * 3.5),
            'next_year' => round($currentLeads * 14),
        ];
    }
}
