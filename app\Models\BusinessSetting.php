<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Cache;

class BusinessSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'type',
        'group',
        'label',
        'description',
        'is_public',
        'is_encrypted',
        'validation_rules',
        'options',
        'sort_order',
    ];

    protected $casts = [
        'validation_rules' => 'array',
        'options' => 'array',
        'is_public' => 'boolean',
        'is_encrypted' => 'boolean',
    ];

    // Scopes
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('label');
    }

    // Accessors & Mutators
    public function getValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            $value = decrypt($value);
        }

        return match ($this->type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'float' => (float) $value,
            'array', 'json' => json_decode($value, true),
            default => $value,
        };
    }

    public function setValueAttribute($value)
    {
        $processedValue = match ($this->type) {
            'boolean' => $value ? '1' : '0',
            'array', 'json' => json_encode($value),
            default => (string) $value,
        };

        if ($this->is_encrypted) {
            $processedValue = encrypt($processedValue);
        }

        $this->attributes['value'] = $processedValue;
    }

    // Static Methods
    public static function get(string $key, $default = null)
    {
        return Cache::remember("setting.{$key}", 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)->first();
            return $setting ? $setting->value : $default;
        });
    }

    public static function getValue(string $key, $default = null)
    {
        return static::get($key, $default);
    }

    public static function set(string $key, $value): void
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            ['value' => $value]
        );

        Cache::forget("setting.{$key}");
    }

    public static function getByGroup(string $group): \Illuminate\Support\Collection
    {
        return Cache::remember("settings.group.{$group}", 3600, function () use ($group) {
            return static::byGroup($group)->ordered()->get();
        });
    }

    public static function getPublicSettings(): \Illuminate\Support\Collection
    {
        return Cache::remember('settings.public', 3600, function () {
            return static::public()->get()->pluck('value', 'key');
        });
    }

    public static function clearCache(): void
    {
        Cache::flush(); // In production, you might want to be more selective
    }

    // Default Settings
    public static function getDefaultSettings(): array
    {
        return [
            // Company Information
            [
                'key' => 'company_name',
                'value' => 'Bhavitech',
                'type' => 'string',
                'group' => 'company',
                'label' => 'Company Name',
                'description' => 'Official company name',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'company_address',
                'value' => 'Convent Road, Fairlands, Salem - 636016',
                'type' => 'string',
                'group' => 'company',
                'label' => 'Company Address',
                'description' => 'Complete business address',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'company_phone',
                'value' => '7010860889',
                'type' => 'string',
                'group' => 'company',
                'label' => 'Primary Phone',
                'description' => 'Main contact phone number',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'company_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'group' => 'company',
                'label' => 'Company Email',
                'description' => 'Main contact email address',
                'is_public' => true,
                'sort_order' => 4,
            ],
            // Marketing Settings
            [
                'key' => 'lead_auto_assignment',
                'value' => true,
                'type' => 'boolean',
                'group' => 'marketing',
                'label' => 'Auto-assign Leads',
                'description' => 'Automatically assign new leads to team members',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'email_marketing_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'marketing',
                'label' => 'Email Marketing',
                'description' => 'Enable email marketing campaigns',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'whatsapp_marketing_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'marketing',
                'label' => 'WhatsApp Marketing',
                'description' => 'Enable WhatsApp marketing campaigns',
                'is_public' => false,
                'sort_order' => 3,
            ],
        ];
    }
}
