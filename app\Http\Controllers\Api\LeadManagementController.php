<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Lead;
use App\Services\LeadScoringService;
use App\Services\LeadDistributionService;
use App\Services\PredictiveAnalyticsService;
use App\Services\BehavioralTrackingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LeadManagementController extends Controller
{
    public function __construct(
        protected LeadScoringService $scoringService,
        protected LeadDistributionService $distributionService,
        protected PredictiveAnalyticsService $analyticsService,
        protected BehavioralTrackingService $trackingService
    ) {}

    public function getLeadInsights(Lead $lead): JsonResponse
    {
        $insights = $this->analyticsService->getLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => $insights,
        ]);
    }

    public function updateLeadScore(Lead $lead): JsonResponse
    {
        $oldScore = $lead->score;
        $this->scoringService->updateLeadScore($lead);

        return response()->json([
            'success' => true,
            'data' => [
                'old_score' => $oldScore,
                'new_score' => $lead->fresh()->score,
                'quality' => $this->scoringService->getLeadQuality($lead->score),
            ],
        ]);
    }

    public function bulkUpdateScores(): JsonResponse
    {
        $updated = $this->scoringService->bulkUpdateScores();

        return response()->json([
            'success' => true,
            'message' => "Updated scores for {$updated} leads",
            'data' => ['updated_count' => $updated],
        ]);
    }

    public function reassignLead(Lead $lead, Request $request): JsonResponse
    {
        $newUserId = $request->input('user_id');
        $assignedUser = $this->distributionService->reassignLead($lead, $newUserId);

        if ($assignedUser) {
            return response()->json([
                'success' => true,
                'message' => 'Lead reassigned successfully',
                'data' => [
                    'assigned_to' => $assignedUser->id,
                    'assigned_to_name' => $assignedUser->name,
                ],
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Failed to reassign lead',
        ], 400);
    }

    public function getDistributionStats(): JsonResponse
    {
        $stats = $this->distributionService->getDistributionStats();

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    public function rebalanceLeads(): JsonResponse
    {
        $rebalanced = $this->distributionService->rebalanceLeads();

        return response()->json([
            'success' => true,
            'message' => "Rebalanced {$rebalanced} leads",
            'data' => ['rebalanced_count' => $rebalanced],
        ]);
    }

    public function trackEvent(Request $request): JsonResponse
    {
        $eventData = $request->validate([
            'event_name' => 'required|string',
            'event_category' => 'nullable|string',
            'event_action' => 'nullable|string',
            'event_label' => 'nullable|string',
            'event_value' => 'nullable|numeric',
            'page_title' => 'nullable|string',
            'custom_properties' => 'nullable|array',
        ]);

        $event = $this->trackingService->trackEvent($eventData);

        return response()->json([
            'success' => true,
            'data' => ['event_id' => $event->id],
        ]);
    }

    public function getLeadJourney(Lead $lead): JsonResponse
    {
        $journey = $this->trackingService->getLeadJourney($lead);

        return response()->json([
            'success' => true,
            'data' => $journey,
        ]);
    }

    public function predictConversion(Lead $lead): JsonResponse
    {
        $probability = $this->analyticsService->predictConversionProbability($lead);
        $optimalTime = $this->analyticsService->predictOptimalContactTime($lead);
        $churnRisk = $this->analyticsService->predictChurnRisk($lead);

        return response()->json([
            'success' => true,
            'data' => [
                'conversion_probability' => $probability,
                'optimal_contact_time' => $optimalTime,
                'churn_risk' => $churnRisk,
            ],
        ]);
    }

    public function getLeadRecommendations(Lead $lead): JsonResponse
    {
        $insights = $this->analyticsService->getLeadInsights($lead);
        $recommendations = $this->generateRecommendations($lead, $insights);

        return response()->json([
            'success' => true,
            'data' => [
                'insights' => $insights,
                'recommendations' => $recommendations,
            ],
        ]);
    }

    protected function generateRecommendations(Lead $lead, array $insights): array
    {
        $recommendations = [];

        $conversionProb = $insights['conversion_probability'];
        $churnRisk = $insights['churn_risk'];

        if ($conversionProb > 80) {
            $recommendations[] = [
                'type' => 'high_priority',
                'action' => 'immediate_contact',
                'message' => 'High conversion probability - contact immediately',
                'priority' => 1,
            ];
        } elseif ($conversionProb > 60) {
            $recommendations[] = [
                'type' => 'medium_priority',
                'action' => 'schedule_call',
                'message' => 'Good conversion potential - schedule a call',
                'priority' => 2,
            ];
        }

        if ($churnRisk['risk_level'] === 'high') {
            $recommendations[] = [
                'type' => 'retention',
                'action' => 'urgent_follow_up',
                'message' => 'High churn risk - urgent follow-up required',
                'priority' => 1,
            ];
        }

        if (empty($lead->phone)) {
            $recommendations[] = [
                'type' => 'data_collection',
                'action' => 'collect_phone',
                'message' => 'Collect phone number to improve lead quality',
                'priority' => 3,
            ];
        }

        return $recommendations;
    }
}
