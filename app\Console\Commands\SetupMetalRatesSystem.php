<?php

namespace App\Console\Commands;

use App\Models\BusinessSetting;
use App\Services\MetalRatesService;
use App\Jobs\FetchMetalRatesJob;
use Illuminate\Console\Command;

class SetupMetalRatesSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bhavitech:setup-metal-rates {--force : Force recreation of settings} {--fetch-initial : Fetch initial rates}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup metal rates API integration system for Bhavitech';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up metal rates API integration system for Bhavitech...');

        if ($this->option('force')) {
            $this->warn('Force mode: Resetting metal rates settings...');
        }

        $this->setupMetalRatesSettings();

        if ($this->option('fetch-initial')) {
            $this->fetchInitialRates();
        }

        $this->info('✅ Metal rates API integration system setup completed!');

        return Command::SUCCESS;
    }

    protected function setupMetalRatesSettings(): void
    {
        $this->info('Setting up metal rates settings...');

        $settings = [
            [
                'key' => 'metal_rates_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'metal_rates',
                'label' => 'Enable Metal Rates',
                'description' => 'Enable metal rates API integration',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'metal_rates_provider',
                'value' => 'mock',
                'type' => 'string',
                'group' => 'metal_rates',
                'label' => 'API Provider',
                'description' => 'Metal rates API provider',
                'options' => ['metals_api', 'fixer_io', 'currencylayer', 'mock'],
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'metals_api_key',
                'value' => '',
                'type' => 'string',
                'group' => 'metal_rates',
                'label' => 'Metals API Key',
                'description' => 'API key for metals-api.com',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'fixer_io_api_key',
                'value' => '',
                'type' => 'string',
                'group' => 'metal_rates',
                'label' => 'Fixer.io API Key',
                'description' => 'API key for fixer.io',
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'currencylayer_api_key',
                'value' => '',
                'type' => 'string',
                'group' => 'metal_rates',
                'label' => 'CurrencyLayer API Key',
                'description' => 'API key for currencylayer.com',
                'is_public' => false,
                'sort_order' => 5,
            ],
            [
                'key' => 'metal_rates_fetch_frequency',
                'value' => 'hourly',
                'type' => 'string',
                'group' => 'metal_rates',
                'label' => 'Fetch Frequency',
                'description' => 'How often to fetch metal rates',
                'options' => ['every_5_minutes', 'every_15_minutes', 'hourly', 'daily'],
                'is_public' => false,
                'sort_order' => 6,
            ],
            [
                'key' => 'metal_rates_cache_duration',
                'value' => 300,
                'type' => 'integer',
                'group' => 'metal_rates',
                'label' => 'Cache Duration (Seconds)',
                'description' => 'How long to cache metal rates',
                'is_public' => false,
                'sort_order' => 7,
            ],
            [
                'key' => 'metal_rates_alerts_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'metal_rates',
                'label' => 'Enable Alerts',
                'description' => 'Enable metal rate alerts and notifications',
                'is_public' => false,
                'sort_order' => 8,
            ],
            [
                'key' => 'metal_rates_subscriptions_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'metal_rates',
                'label' => 'Enable Subscriptions',
                'description' => 'Enable metal rate subscriptions',
                'is_public' => false,
                'sort_order' => 9,
            ],
            [
                'key' => 'metal_rates_widget_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'metal_rates',
                'label' => 'Enable Widget',
                'description' => 'Enable metal rates widget for website',
                'is_public' => true,
                'sort_order' => 10,
            ],
            [
                'key' => 'metal_rates_default_metals',
                'value' => json_encode(['gold', 'silver', 'platinum', 'copper']),
                'type' => 'array',
                'group' => 'metal_rates',
                'label' => 'Default Metals',
                'description' => 'Default metals to display and track',
                'is_public' => false,
                'sort_order' => 11,
            ],
            [
                'key' => 'metal_rates_currency',
                'value' => 'INR',
                'type' => 'string',
                'group' => 'metal_rates',
                'label' => 'Base Currency',
                'description' => 'Base currency for metal rates',
                'options' => ['INR', 'USD', 'EUR', 'GBP'],
                'is_public' => false,
                'sort_order' => 12,
            ],
        ];

        $created = 0;
        foreach ($settings as $settingData) {
            $existing = BusinessSetting::where('key', $settingData['key'])->first();

            if (!$existing || $this->option('force')) {
                if ($existing) {
                    $existing->delete();
                }

                BusinessSetting::create($settingData);
                $created++;
                $this->line("  ✓ Created setting: {$settingData['key']}");
            } else {
                $this->line("  - Setting already exists: {$settingData['key']}");
            }
        }

        $this->info("Created {$created} metal rates settings.");
    }

    protected function fetchInitialRates(): void
    {
        $this->info('Fetching initial metal rates...');

        try {
            $metalRatesService = app(MetalRatesService::class);
            $result = $metalRatesService->fetchCurrentRates();

            if ($result['success']) {
                $this->info("✓ Successfully fetched rates for " . count($result['rates']) . " metals");
                $this->info("  Provider: {$result['provider']}");
                $this->info("  Timestamp: {$result['timestamp']}");

                // Display the rates
                $this->table(
                    ['Metal', 'Rate', 'Currency', 'Unit'],
                    collect($result['rates'])->map(function ($rate, $metal) use ($metalRatesService) {
                        $config = $metalRatesService->getSupportedMetals()[$metal];
                        return [
                            ucfirst($metal),
                            number_format($rate, 2),
                            $config['currency'],
                            $config['unit'],
                        ];
                    })->toArray()
                );

                // Queue regular fetching job
                FetchMetalRatesJob::dispatch();
                $this->info('✓ Queued regular metal rates fetching job');

            } else {
                $this->error('Failed to fetch initial metal rates');
                if (isset($result['warning'])) {
                    $this->warn($result['warning']);
                }
            }

        } catch (\Exception $e) {
            $this->error('Error fetching initial rates: ' . $e->getMessage());
        }
    }
}
