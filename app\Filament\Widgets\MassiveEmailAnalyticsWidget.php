<?php

namespace App\Filament\Widgets;

use App\Models\Contact;
use App\Models\ContactList;
use App\Models\EmailAccount;
use App\Models\EmailCampaign;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class MassiveEmailAnalyticsWidget extends BaseWidget
{
    protected static ?int $sort = 1;
    
    protected function getStats(): array
    {
        // Get total contacts with geographic breakdown
        $totalContacts = Contact::count();
        $subscribedContacts = Contact::where('is_subscribed', true)->count();
        
        // Get geographic distribution
        $topStates = Contact::select('state', DB::raw('COUNT(*) as count'))
            ->whereNotNull('state')
            ->groupBy('state')
            ->orderByDesc('count')
            ->limit(3)
            ->pluck('count', 'state')
            ->toArray();
        
        // Get email account utilization
        $emailAccounts = EmailAccount::where('is_active', true)->get();
        $totalDailyCapacity = $emailAccounts->sum('daily_send_limit');
        $totalSentToday = $emailAccounts->sum('emails_sent_today');
        $utilizationRate = $totalDailyCapacity > 0 ? ($totalSentToday / $totalDailyCapacity) * 100 : 0;
        
        // Get engagement metrics
        $highEngagementContacts = Contact::where('engagement_score', '>=', 70)->count();
        $engagementRate = $totalContacts > 0 ? ($highEngagementContacts / $totalContacts) * 100 : 0;
        
        // Get campaign performance
        $activeCampaigns = EmailCampaign::where('status', 'active')->count();
        $scheduledCampaigns = EmailCampaign::where('status', 'scheduled')->count();
        
        // Get list statistics
        $totalLists = ContactList::where('status', 'active')->count();

        // Get massive lists (with 100k+ contacts) using a proper subquery
        $massiveLists = ContactList::where('status', 'active')
            ->whereIn('id', function ($query) {
                $query->select('contact_list_id')
                    ->from('contact_list_members')
                    ->groupBy('contact_list_id')
                    ->havingRaw('COUNT(*) >= 100000');
            })
            ->count();
        
        // Format top states for display
        $topStatesText = '';
        if (!empty($topStates)) {
            $stateStrings = [];
            foreach ($topStates as $state => $count) {
                $stateStrings[] = $state . ' (' . number_format($count) . ')';
            }
            $topStatesText = implode(', ', $stateStrings);
        }
        
        return [
            Stat::make('Total Contacts', number_format($totalContacts))
                ->description($subscribedContacts > 0 ? number_format($subscribedContacts) . ' subscribed' : 'No subscribed contacts')
                ->descriptionIcon($totalContacts >= 1000000 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-users')
                ->color($totalContacts >= 1000000 ? 'success' : ($totalContacts >= 100000 ? 'warning' : 'gray'))
                ->chart($this->getContactGrowthChart()),
                
            Stat::make('Geographic Distribution', $topStatesText ?: 'No geographic data')
                ->description('Top 3 states by contact count')
                ->descriptionIcon('heroicon-m-map-pin')
                ->color('info'),
                
            Stat::make('Daily Email Capacity', number_format($totalDailyCapacity))
                ->description(number_format($totalSentToday) . ' sent today (' . number_format($utilizationRate, 1) . '% used)')
                ->descriptionIcon($utilizationRate >= 90 ? 'heroicon-m-exclamation-triangle' : 'heroicon-m-envelope')
                ->color($utilizationRate >= 90 ? 'danger' : ($utilizationRate >= 70 ? 'warning' : 'success'))
                ->chart($this->getDailyUsageChart()),
                
            Stat::make('Engagement Rate', number_format($engagementRate, 1) . '%')
                ->description(number_format($highEngagementContacts) . ' highly engaged contacts')
                ->descriptionIcon('heroicon-m-heart')
                ->color($engagementRate >= 70 ? 'success' : ($engagementRate >= 40 ? 'warning' : 'danger'))
                ->chart($this->getEngagementChart()),
                
            Stat::make('Active Campaigns', $activeCampaigns)
                ->description($scheduledCampaigns . ' scheduled campaigns')
                ->descriptionIcon('heroicon-m-paper-airplane')
                ->color($activeCampaigns > 0 ? 'success' : 'gray'),
                
            Stat::make('Email Lists', number_format($totalLists))
                ->description($massiveLists . ' massive lists (1L+ contacts)')
                ->descriptionIcon('heroicon-m-list-bullet')
                ->color($massiveLists > 0 ? 'success' : 'info'),
        ];
    }
    
    private function getContactGrowthChart(): array
    {
        // Get contact growth over last 7 days
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            $count = Contact::whereDate('created_at', $date)->count();
            $data[] = $count;
        }
        return $data;
    }
    
    private function getDailyUsageChart(): array
    {
        // Get daily email usage over last 7 days
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->format('Y-m-d');
            // This would need to be tracked in a daily_email_stats table
            // For now, return sample data
            $data[] = rand(100, 500);
        }
        return $data;
    }
    
    private function getEngagementChart(): array
    {
        // Get engagement distribution
        $low = Contact::where('engagement_score', '<', 40)->count();
        $medium = Contact::whereBetween('engagement_score', [40, 69])->count();
        $high = Contact::where('engagement_score', '>=', 70)->count();
        
        return [$low, $medium, $high];
    }
}
