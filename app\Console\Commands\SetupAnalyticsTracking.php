<?php

namespace App\Console\Commands;

use App\Models\BusinessSetting;
use App\Models\AnalyticsEvent;
use App\Models\User;
use Illuminate\Console\Command;
use Carbon\Carbon;

class SetupAnalyticsTracking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bhavitech:setup-analytics {--force : Force recreation of settings}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup advanced analytics tracking and dashboard for Bhavitech';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up advanced analytics tracking for Bhavitech...');

        if ($this->option('force')) {
            $this->warn('Force mode: Resetting analytics settings...');
        }

        $this->setupAnalyticsSettings();
        $this->createSampleAnalyticsData();

        $this->info('✅ Advanced analytics setup completed!');

        return Command::SUCCESS;
    }

    protected function setupAnalyticsSettings(): void
    {
        $this->info('Setting up analytics settings...');

        $settings = [
            [
                'key' => 'analytics_tracking_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'analytics',
                'label' => 'Enable Analytics Tracking',
                'description' => 'Enable comprehensive analytics tracking across the website',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'analytics_real_time_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'analytics',
                'label' => 'Enable Real-time Analytics',
                'description' => 'Enable real-time visitor and event tracking',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'analytics_data_retention_days',
                'value' => 365,
                'type' => 'integer',
                'group' => 'analytics',
                'label' => 'Data Retention Period (Days)',
                'description' => 'How long to keep analytics data',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'analytics_session_timeout',
                'value' => 30,
                'type' => 'integer',
                'group' => 'analytics',
                'label' => 'Session Timeout (Minutes)',
                'description' => 'Session timeout for visitor tracking',
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'analytics_bounce_rate_threshold',
                'value' => 10,
                'type' => 'integer',
                'group' => 'analytics',
                'label' => 'Bounce Rate Threshold (Seconds)',
                'description' => 'Minimum time on page to not count as bounce',
                'is_public' => false,
                'sort_order' => 5,
            ],
            [
                'key' => 'analytics_conversion_goals',
                'value' => json_encode([
                    'form_submit' => 'Contact Form Submission',
                    'quote_request' => 'Quote Request',
                    'phone_call' => 'Phone Call Click',
                    'email_click' => 'Email Click',
                    'service_page_view' => 'Service Page View',
                ]),
                'type' => 'array',
                'group' => 'analytics',
                'label' => 'Conversion Goals',
                'description' => 'Defined conversion goals for tracking',
                'is_public' => false,
                'sort_order' => 6,
            ],
            [
                'key' => 'analytics_dashboard_refresh_interval',
                'value' => 30,
                'type' => 'integer',
                'group' => 'analytics',
                'label' => 'Dashboard Refresh Interval (Seconds)',
                'description' => 'How often to refresh real-time dashboard',
                'is_public' => false,
                'sort_order' => 7,
            ],
            [
                'key' => 'analytics_alerts_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'analytics',
                'label' => 'Enable Analytics Alerts',
                'description' => 'Enable alerts for traffic spikes, new leads, etc.',
                'is_public' => false,
                'sort_order' => 8,
            ],
            [
                'key' => 'analytics_predictive_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'analytics',
                'label' => 'Enable Predictive Analytics',
                'description' => 'Enable forecasting and predictive insights',
                'is_public' => false,
                'sort_order' => 9,
            ],
            [
                'key' => 'analytics_export_formats',
                'value' => json_encode(['json', 'csv', 'excel', 'pdf']),
                'type' => 'array',
                'group' => 'analytics',
                'label' => 'Export Formats',
                'description' => 'Available formats for data export',
                'is_public' => false,
                'sort_order' => 10,
            ],
        ];

        $created = 0;
        foreach ($settings as $settingData) {
            $existing = BusinessSetting::where('key', $settingData['key'])->first();

            if (!$existing || $this->option('force')) {
                if ($existing) {
                    $existing->delete();
                }

                BusinessSetting::create($settingData);
                $created++;
                $this->line("  ✓ Created setting: {$settingData['key']}");
            } else {
                $this->line("  - Setting already exists: {$settingData['key']}");
            }
        }

        $this->info("Created {$created} analytics settings.");
    }

    protected function createSampleAnalyticsData(): void
    {
        $this->info('Creating sample analytics data...');

        // Create sample analytics events for the last 30 days
        $events = [
            'page_view',
            'form_submit',
            'button_click',
            'download',
            'email_open',
            'email_click',
            'phone_click',
            'service_view',
        ];

        $pages = [
            '/',
            '/services',
            '/services/web-development',
            '/services/mobile-development',
            '/services/digital-marketing',
            '/services/graphic-design',
            '/contact',
            '/about',
            '/portfolio',
        ];

        $sources = [
            'organic',
            'google',
            'facebook',
            'linkedin',
            'direct',
            'referral',
            'email',
            'whatsapp',
        ];

        $devices = ['desktop', 'mobile', 'tablet'];
        $browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];
        $cities = ['Salem', 'Chennai', 'Bangalore', 'Coimbatore', 'Madurai'];

        $created = 0;
        for ($day = 29; $day >= 0; $day--) {
            $date = now()->subDays($day);

            // Create 20-100 events per day
            $eventsPerDay = rand(20, 100);

            for ($i = 0; $i < $eventsPerDay; $i++) {
                $visitorId = 'visitor_' . rand(1000, 9999);
                $sessionId = 'session_' . rand(10000, 99999);

                $eventTime = $date->copy()->addMinutes(rand(0, 1439));

                AnalyticsEvent::create([
                    'event_name' => $events[array_rand($events)],
                    'page_url' => 'https://bhavitech.com' . $pages[array_rand($pages)],
                    'visitor_id' => $visitorId,
                    'session_id' => $sessionId,
                    'utm_source' => $sources[array_rand($sources)],
                    'utm_medium' => rand(0, 1) ? 'cpc' : 'organic',
                    'utm_campaign' => 'sample_campaign_' . rand(1, 5),
                    'device_type' => $devices[array_rand($devices)],
                    'browser' => $browsers[array_rand($browsers)],
                    'city' => $cities[array_rand($cities)],
                    'country' => 'India',
                    'custom_properties' => [
                        'screen_resolution' => '1920x1080',
                        'user_agent' => 'Sample User Agent',
                        'referrer' => rand(0, 1) ? 'https://google.com' : null,
                        'session_duration' => rand(30, 600), // 30 seconds to 10 minutes
                    ],
                    'event_timestamp' => $eventTime,
                    'created_at' => $eventTime,
                ]);

                $created++;
            }
        }

        $this->info("Created {$created} sample analytics events.");
    }
}
