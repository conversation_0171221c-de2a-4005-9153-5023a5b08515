<?php

namespace App\Console\Commands;

use App\Models\BusinessSetting;
use App\Models\SocialMediaPost;
use App\Services\ContentGenerationService;
use App\Models\User;
use Illuminate\Console\Command;
use Carbon\Carbon;

class SetupSocialMediaAutomation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bhavitech:setup-social-media {--force : Force recreation of settings}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup social media automation and default content for Bhavitech';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up social media automation for Bhavitech...');

        if ($this->option('force')) {
            $this->warn('Force mode: Resetting social media settings...');
        }

        $this->setupSocialMediaSettings();
        $this->createSamplePosts();

        $this->info('✅ Social media automation setup completed!');

        return Command::SUCCESS;
    }

    protected function setupSocialMediaSettings(): void
    {
        $this->info('Setting up social media settings...');

        $settings = [
            [
                'key' => 'social_media_auto_post',
                'value' => true,
                'type' => 'boolean',
                'group' => 'social_media',
                'label' => 'Enable Auto Posting',
                'description' => 'Automatically post scheduled content to social media',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'social_media_cross_post',
                'value' => true,
                'type' => 'boolean',
                'group' => 'social_media',
                'label' => 'Enable Cross Posting',
                'description' => 'Post content across multiple platforms simultaneously',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'social_media_optimal_timing',
                'value' => true,
                'type' => 'boolean',
                'group' => 'social_media',
                'label' => 'Use Optimal Timing',
                'description' => 'Post content at optimal times for each platform',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'social_media_hashtag_strategy',
                'value' => 'auto',
                'type' => 'string',
                'group' => 'social_media',
                'label' => 'Hashtag Strategy',
                'description' => 'How to handle hashtags (auto, manual, mixed)',
                'options' => ['auto', 'manual', 'mixed'],
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'social_media_content_approval',
                'value' => false,
                'type' => 'boolean',
                'group' => 'social_media',
                'label' => 'Require Content Approval',
                'description' => 'Require manual approval before posting',
                'is_public' => false,
                'sort_order' => 5,
            ],
            [
                'key' => 'social_media_brand_voice',
                'value' => 'professional_friendly',
                'type' => 'string',
                'group' => 'social_media',
                'label' => 'Brand Voice',
                'description' => 'Tone of voice for social media content',
                'options' => ['professional', 'friendly', 'professional_friendly', 'casual', 'authoritative'],
                'is_public' => false,
                'sort_order' => 6,
            ],
        ];

        $created = 0;
        foreach ($settings as $settingData) {
            $existing = BusinessSetting::where('key', $settingData['key'])->first();

            if (!$existing || $this->option('force')) {
                if ($existing) {
                    $existing->delete();
                }

                BusinessSetting::create($settingData);
                $created++;
                $this->line("  ✓ Created setting: {$settingData['key']}");
            } else {
                $this->line("  - Setting already exists: {$settingData['key']}");
            }
        }

        $this->info("Created {$created} social media settings.");
    }

    protected function createSamplePosts(): void
    {
        $this->info('Creating sample social media posts...');

        $creator = User::first();
        if (!$creator) {
            $this->error('No users found. Please create a user first.');
            return;
        }

        $contentService = app(ContentGenerationService::class);
        $samplePosts = [
            [
                'type' => 'promotional',
                'service' => 'web development',
                'platform' => 'facebook',
            ],
            [
                'type' => 'educational',
                'service' => 'digital marketing',
                'platform' => 'linkedin',
            ],
            [
                'type' => 'behind_the_scenes',
                'service' => 'mobile development',
                'platform' => 'instagram',
            ],
            [
                'type' => 'client_success',
                'service' => 'graphic design',
                'platform' => 'facebook',
            ],
        ];

        $created = 0;
        foreach ($samplePosts as $postData) {
            $content = $contentService->generateContent($postData);

            $post = SocialMediaPost::create([
                'title' => "Sample {$postData['type']} post",
                'content' => $content['content'],
                'hashtags' => $content['hashtags'],
                'post_type' => 'text',
                'cross_post_platforms' => [$postData['platform']],
                'status' => 'draft',
                'scheduled_at' => now()->addDays($created + 1)->setHour(10),
                'created_by' => $creator->id,
            ]);

            $created++;
            $this->line("  ✓ Created sample post: {$post->title}");
        }

        $this->info("Created {$created} sample social media posts.");
    }
}
