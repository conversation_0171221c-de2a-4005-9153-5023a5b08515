<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="scroll-smooth">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>@yield('title', 'Bhavitech - Leading Digital Marketing & Web Development Company')</title>
    <meta name="description" content="@yield('description', 'Bhavitech offers cutting-edge web development, mobile app development, digital marketing, and AI-powered business solutions in Salem, Tamil Nadu.')">
    <meta name="keywords" content="@yield('keywords', 'web development, mobile app development, digital marketing, SEO, social media marketing, Salem, Tamil Nadu, Bhavitech')">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('og_title', 'Bhavitech - Leading Digital Marketing & Web Development Company')">
    <meta property="og:description" content="@yield('og_description', 'Transform your business with our cutting-edge digital solutions. Web development, mobile apps, digital marketing, and AI-powered tools.')">
    <meta property="og:image" content="@yield('og_image', asset('images/bhavitech-og-image.svg'))">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Bhavitech">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('twitter_title', 'Bhavitech - Leading Digital Marketing & Web Development Company')">
    <meta name="twitter:description" content="@yield('twitter_description', 'Transform your business with our cutting-edge digital solutions.')">
    <meta name="twitter:image" content="@yield('twitter_image', asset('images/bhavitech-twitter-image.svg'))">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ asset('images/favicon.svg') }}">
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/favicon-16x16.png') }}">

    <!-- Web App Manifest -->
    <link rel="manifest" href="{{ asset('manifest.json') }}">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="{{ url()->current() }}">
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Schema.org structured data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Bhavitech",
        "url": "{{ config('app.url') }}",
        "logo": "{{ asset('images/logo.svg') }}",
        "description": "Leading digital marketing and web development company in Salem, Tamil Nadu",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "Convent Road, Fairlands",
            "addressLocality": "Salem",
            "postalCode": "636016",
            "addressCountry": "IN"
        },
        "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+91-7010860889",
            "contactType": "customer service",
            "email": "<EMAIL>"
        },
        "sameAs": [
            "https://www.facebook.com/bhavitech",
            "https://www.linkedin.com/company/bhavitech",
            "https://www.instagram.com/bhavitech"
        ]
    }
    </script>
    
    @stack('head')
    
    <!-- Styles -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    
    <!-- Google Analytics -->
    @if(config('services.google_analytics.id'))
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ config('services.google_analytics.id') }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ config('services.google_analytics.id') }}');
    </script>
    @endif
    
    <!-- Facebook Pixel -->
    @if(config('services.facebook_pixel.id'))
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '{{ config('services.facebook_pixel.id') }}');
        fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
        src="https://www.facebook.com/tr?id={{ config('services.facebook_pixel.id') }}&ev=PageView&noscript=1"
    /></noscript>
    @endif
</head>

<body class="font-sans antialiased bg-white text-gray-900">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary-600 text-white px-4 py-2 rounded-md z-50">
        Skip to main content
    </a>
    
    <!-- Navigation -->
    @include('components.navigation')
    
    <!-- Main Content -->
    <main id="main-content">
        @yield('content')
    </main>
    
    <!-- Footer -->
    @include('components.footer')
    
    <!-- WhatsApp Float Button -->
    @include('components.whatsapp-float')
    
    <!-- Live Chat Widget -->
    @include('components.live-chat')
    
    <!-- Cookie Consent -->
    @include('components.cookie-consent')
    
    <!-- Scripts -->
    @stack('scripts')

    <!-- Service Worker for PWA -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }
    </script>

</body>
</html>
