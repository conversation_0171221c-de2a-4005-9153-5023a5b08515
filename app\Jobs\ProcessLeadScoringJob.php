<?php

namespace App\Jobs;

use App\Models\Lead;
use App\Services\AutomatedLeadScoringService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessLeadScoringJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    protected ?int $leadId;
    protected bool $forceUpdate;

    /**
     * Create a new job instance.
     */
    public function __construct(?int $leadId = null, bool $forceUpdate = false)
    {
        $this->leadId = $leadId;
        $this->forceUpdate = $forceUpdate;
    }

    /**
     * Execute the job.
     */
    public function handle(AutomatedLeadScoringService $scoringService): void
    {
        try {
            if ($this->leadId) {
                // Score specific lead
                $lead = Lead::find($this->leadId);

                if (!$lead) {
                    Log::warning('Lead not found for scoring', ['lead_id' => $this->leadId]);
                    return;
                }

                $oldScore = $lead->score;
                $scoringService->updateLeadScore($lead);

                Log::info('Individual lead scored', [
                    'lead_id' => $lead->id,
                    'old_score' => $oldScore,
                    'new_score' => $lead->fresh()->score,
                ]);

            } else {
                // Batch scoring
                $results = $scoringService->batchUpdateLeadScores();

                Log::info('Batch lead scoring completed', [
                    'updated' => $results['updated'],
                    'errors' => $results['errors'],
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Lead scoring job failed', [
                'lead_id' => $this->leadId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Lead scoring job failed permanently', [
            'lead_id' => $this->leadId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
