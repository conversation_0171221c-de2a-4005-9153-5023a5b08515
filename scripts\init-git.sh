#!/bin/bash

# Git Repository Initialization Script for 1 Crore Email Management System
# This script initializes Git repository with proper configuration and branch structure

set -e

echo ""
echo "🌿 Initializing Git Repository for 1 Crore Email Management System..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Git is installed
if ! command -v git &> /dev/null; then
    echo -e "${RED}❌ Git is not installed${NC}"
    echo "Please install Git:"
    echo "  Ubuntu/Debian: sudo apt install git"
    echo "  macOS: brew install git"
    echo "  CentOS/RHEL: sudo yum install git"
    exit 1
fi

echo -e "${GREEN}✅ Git is available${NC}"
git --version

# Initialize Git repository if not already initialized
if [ ! -d ".git" ]; then
    echo ""
    echo -e "${BLUE}📁 Initializing Git repository...${NC}"
    git init
    echo -e "${GREEN}✅ Git repository initialized${NC}"
else
    echo ""
    echo -e "${BLUE}📁 Git repository already exists${NC}"
fi

# Configure Git user (if not already configured)
echo ""
echo -e "${BLUE}👤 Configuring Git user...${NC}"

if ! git config user.name &> /dev/null; then
    read -p "Enter your name: " username
    git config user.name "$username"
fi

if ! git config user.email &> /dev/null; then
    read -p "Enter your email: " email
    git config user.email "$email"
fi

echo -e "${GREEN}✅ Git user configured:${NC}"
echo "Name: $(git config user.name)"
echo "Email: $(git config user.email)"

# Set up Git configuration for the project
echo ""
echo -e "${BLUE}⚙️ Setting up Git configuration...${NC}"
git config core.autocrlf input
git config core.safecrlf false
git config pull.rebase false
git config init.defaultBranch main
git config branch.autosetupmerge always
git config branch.autosetuprebase always

echo -e "${GREEN}✅ Git configuration completed${NC}"

# Create initial commit if no commits exist
if ! git rev-parse HEAD &> /dev/null; then
    echo ""
    echo -e "${BLUE}📝 Creating initial commit...${NC}"
    git add .
    git commit -m "🎉 Initial commit: 1 Crore Email Management System

Features:
- Laravel 11 application
- MySQL database with optimizations
- Email list management for massive datasets
- Geographic filtering capabilities
- Advanced analytics dashboard
- WhatsApp integration
- Comprehensive admin panel

System Specifications:
- Supports 1 crore (10 million) emails
- Geographic filtering by state/city
- Daily sending limits (500 emails per account)
- Performance optimized for massive datasets
- Production-ready with monitoring tools"
    
    echo -e "${GREEN}✅ Initial commit created${NC}"
else
    echo ""
    echo -e "${BLUE}📝 Repository already has commits${NC}"
fi

# Create and switch to development branch
echo ""
echo -e "${BLUE}🌿 Setting up branch structure...${NC}"

if git checkout -b development 2>/dev/null; then
    echo -e "${GREEN}✅ Created and switched to 'development' branch${NC}"
elif git checkout development 2>/dev/null; then
    echo -e "${GREEN}✅ Switched to existing 'development' branch${NC}"
else
    echo -e "${YELLOW}⚠️ Could not create/switch to development branch${NC}"
fi

# Create feature branches
echo ""
echo -e "${BLUE}🔧 Creating feature branches...${NC}"

branches=(
    "feature/email-campaigns"
    "feature/analytics"
    "feature/whatsapp-integration"
    "feature/performance-optimization"
    "feature/api-development"
    "feature/mobile-app"
    "feature/advanced-segmentation"
    "feature/automation-workflows"
)

for branch in "${branches[@]}"; do
    if git checkout -b "$branch" development 2>/dev/null; then
        echo -e "${GREEN}✅ Created branch: $branch${NC}"
    else
        echo -e "${YELLOW}⚠️ Branch $branch already exists or could not be created${NC}"
    fi
done

# Switch back to main branch
if git checkout main 2>/dev/null || git checkout master 2>/dev/null; then
    echo -e "${GREEN}✅ Switched back to main branch${NC}"
else
    echo -e "${YELLOW}⚠️ Could not switch to main/master branch${NC}"
fi

echo ""
echo -e "${BLUE}📊 Current branch structure:${NC}"
git branch -a

echo ""
echo -e "${BLUE}📋 Git Repository Information:${NC}"
echo "Repository: $(pwd)"
echo "Current branch: $(git branch --show-current 2>/dev/null || echo 'Unable to determine')"
echo "Total commits: $(git rev-list --count HEAD 2>/dev/null || echo '0')"
echo "Remote origin: $(git remote get-url origin 2>/dev/null || echo 'No remote origin configured')"

echo ""
echo -e "${BLUE}📝 Next Steps:${NC}"
echo "1. Configure remote repository: git remote add origin [URL]"
echo "2. Push to remote: git push -u origin main"
echo "3. Set up branch protection rules on GitHub/GitLab"
echo "4. Configure CI/CD workflows"
echo ""
echo -e "${GREEN}✅ Git repository initialization completed!${NC}"
echo ""
