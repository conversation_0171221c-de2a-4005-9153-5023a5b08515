<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_validations', function (Blueprint $table) {
            $table->id();
            $table->string('email')->unique();
            $table->boolean('is_valid_format')->default(false);
            $table->boolean('domain_exists')->default(false);
            $table->boolean('is_disposable')->default(false);
            $table->boolean('is_role_based')->default(false);
            $table->integer('risk_score')->default(0);
            $table->integer('engagement_prediction')->default(0);
            $table->integer('deliverability_score')->default(0);
            $table->timestamp('validated_at')->nullable();
            $table->timestamps();

            $table->index(['email', 'validated_at']);
            $table->index(['risk_score', 'deliverability_score']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_validations');
    }
};
