<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('leads', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('company')->nullable();
            $table->string('source')->default('website'); // website, social, referral, etc.
            $table->string('status')->default('new'); // new, contacted, qualified, converted, lost
            $table->integer('score')->default(0); // AI-powered lead scoring
            $table->string('service_interest')->nullable(); // web-dev, mobile, marketing, design
            $table->text('message')->nullable();
            $table->json('metadata')->nullable(); // Additional data like UTM params, page visited, etc.
            $table->timestamp('last_contacted_at')->nullable();
            $table->timestamp('qualified_at')->nullable();
            $table->timestamp('converted_at')->nullable();
            $table->foreignId('assigned_to')->nullable()->constrained('users');
            $table->timestamps();

            $table->index(['status', 'score']);
            $table->index(['source', 'created_at']);
            $table->index('assigned_to');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('leads');
    }
};
