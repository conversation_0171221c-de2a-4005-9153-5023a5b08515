# MySQL Configuration for 1 Crore Email Management System
# Optimized for handling massive email datasets efficiently
# Place this file in your MySQL configuration directory

[mysqld]
# Basic Settings
default-storage-engine = innodb
sql_mode = NO_ENGINE_SUBSTITUTION,STRICT_TRANS_TABLES
default-time-zone = '+00:00'

# Connection Settings
max_connections = 1000
max_connect_errors = 1000000
connect_timeout = 60
wait_timeout = 28800
interactive_timeout = 28800
net_read_timeout = 60
net_write_timeout = 60

# Buffer Pool Settings (adjust based on available RAM)
# For 8GB RAM system
innodb_buffer_pool_size = 4G
innodb_buffer_pool_instances = 8

# For 16GB RAM system (uncomment below)
# innodb_buffer_pool_size = 8G
# innodb_buffer_pool_instances = 16

# For 32GB+ RAM system (uncomment below)
# innodb_buffer_pool_size = 16G
# innodb_buffer_pool_instances = 16

# InnoDB Log Settings
innodb_log_file_size = 1G
innodb_log_buffer_size = 128M
innodb_log_files_in_group = 2

# InnoDB Performance Settings
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_io_capacity = 2000
innodb_io_capacity_max = 4000
innodb_read_io_threads = 8
innodb_write_io_threads = 8
innodb_thread_concurrency = 0
innodb_purge_threads = 4

# InnoDB Buffer and Cache Settings
innodb_change_buffer_max_size = 25
innodb_adaptive_hash_index = 1
innodb_stats_on_metadata = 0
innodb_stats_persistent = 1
innodb_stats_auto_recalc = 1

# Query Cache Settings (for read-heavy workloads)
query_cache_type = 1
query_cache_size = 512M
query_cache_limit = 64M
query_cache_min_res_unit = 4K

# Table Cache Settings
table_open_cache = 4000
table_definition_cache = 2000

# Temporary Tables
tmp_table_size = 1G
max_heap_table_size = 1G

# MyISAM Settings (for temporary tables)
key_buffer_size = 256M
myisam_sort_buffer_size = 128M
myisam_max_sort_file_size = 2G
myisam_repair_threads = 1

# Sort and Group Settings
sort_buffer_size = 4M
read_buffer_size = 2M
read_rnd_buffer_size = 8M
join_buffer_size = 8M

# Thread Settings
thread_cache_size = 100
thread_stack = 256K

# Logging Settings
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1
min_examined_row_limit = 1000

# General Query Log (disable in production for performance)
general_log = 0
general_log_file = /var/log/mysql/general.log

# Binary Logging (for replication and backup)
log_bin = /var/log/mysql/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M
sync_binlog = 1

# Character Set and Collation
character-set-server = utf8mb4
collation-server = utf8mb4_0900_ai_ci
init_connect = 'SET NAMES utf8mb4'

# Security Settings
local_infile = 0
skip_name_resolve = 1

# Performance Schema (for monitoring)
performance_schema = 1
performance_schema_max_table_instances = 12500
performance_schema_max_table_handles = 4000

# Partitioning Support
partition = 1

# Group Replication (for clustering)
# Uncomment for multi-master setup
# gtid_mode = ON
# enforce_gtid_consistency = ON
# binlog_checksum = NONE
# log_slave_updates = ON
# log_bin = binlog
# binlog_format = ROW
# master_info_repository = TABLE
# relay_log_info_repository = TABLE
# transaction_write_set_extraction = XXHASH64

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4

[mysqldump]
quick
quote-names
max_allowed_packet = 1G
single-transaction
routines
triggers

[mysql_safe]
log-error = /var/log/mysql/error.log
pid-file = /var/run/mysqld/mysqld.pid

# Optimization Notes:
# 1. Adjust innodb_buffer_pool_size to 70-80% of available RAM
# 2. Monitor slow query log and optimize queries accordingly
# 3. Use EXPLAIN to analyze query performance
# 4. Consider partitioning large tables (contacts, email_logs)
# 5. Implement read replicas for scaling read operations
# 6. Regular maintenance: OPTIMIZE TABLE, ANALYZE TABLE
# 7. Monitor disk I/O and consider SSD storage for better performance
