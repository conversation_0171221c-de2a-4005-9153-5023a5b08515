<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Invoice;
use App\Models\SupportTicket;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class CustomerPortalController extends Controller
{
    public function __construct()
    {
        // Middleware is now handled in routes/web.php
    }

    public function dashboard()
    {
        $user = Auth::user();

        // Get customer's projects
        $projects = Project::where('customer_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get customer's invoices
        $invoices = Invoice::where('customer_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get customer's support tickets
        $tickets = SupportTicket::where('customer_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Calculate statistics
        $stats = [
            'total_projects' => Project::where('customer_id', $user->id)->count(),
            'active_projects' => Project::where('customer_id', $user->id)->where('status', 'in_progress')->count(),
            'completed_projects' => Project::where('customer_id', $user->id)->where('status', 'completed')->count(),
            'total_invoices' => Invoice::where('customer_id', $user->id)->count(),
            'pending_invoices' => Invoice::where('customer_id', $user->id)->where('status', 'pending')->count(),
            'paid_invoices' => Invoice::where('customer_id', $user->id)->where('status', 'paid')->count(),
            'open_tickets' => SupportTicket::where('customer_id', $user->id)->where('status', 'open')->count(),
            'total_amount_paid' => Invoice::where('customer_id', $user->id)->where('status', 'paid')->sum('total_amount'),
            'pending_amount' => Invoice::where('customer_id', $user->id)->where('status', 'pending')->sum('total_amount'),
        ];

        return view('customer.dashboard', compact('projects', 'invoices', 'tickets', 'stats'));
    }

    public function projects()
    {
        $user = Auth::user();
        $projects = Project::where('customer_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('customer.projects.index', compact('projects'));
    }

    public function showProject(Project $project)
    {
        $user = Auth::user();

        if ($project->customer_id !== $user->id) {
            abort(403, 'Access denied.');
        }

        return view('customer.projects.show', compact('project'));
    }

    public function invoices()
    {
        $user = Auth::user();
        $invoices = Invoice::where('customer_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('customer.invoices.index', compact('invoices'));
    }

    public function showInvoice(Invoice $invoice)
    {
        $user = Auth::user();

        if ($invoice->customer_id !== $user->id) {
            abort(403, 'Access denied.');
        }

        return view('customer.invoices.show', compact('invoice'));
    }

    public function support()
    {
        $user = Auth::user();
        $tickets = SupportTicket::where('customer_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('customer.support.index', compact('tickets'));
    }

    public function createTicket(Request $request)
    {
        $validated = $request->validate([
            'subject' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:low,medium,high,urgent',
            'category' => 'required|in:technical,billing,general,feature_request',
        ]);

        $user = Auth::user();

        $ticket = SupportTicket::create([
            'customer_id' => $user->id,
            'ticket_number' => 'TKT-' . date('Y') . '-' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT),
            'title' => $validated['subject'],
            'description' => $validated['description'],
            'priority' => $validated['priority'],
            'category' => $validated['category'],
            'status' => 'open',
        ]);

        return redirect()->route('customer.support')
            ->with('success', 'Support ticket created successfully. Ticket #' . $ticket->ticket_number);
    }







    public function profile()
    {
        $user = Auth::user();

        return view('customer.profile', compact('user'));
    }

    public function updateProfile(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . Auth::id(),
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
        ]);

        $user = Auth::user();
        $user->update($validated);

        return redirect()->route('customer.profile')
            ->with('success', 'Profile updated successfully.');
    }

    public function changePassword()
    {
        return view('customer.profile.change-password');
    }

    public function updatePassword(Request $request)
    {
        $validated = $request->validate([
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed',
        ]);

        $customer = Auth::user();

        try {
            // Verify current password
            if (!Hash::check($validated['current_password'], $customer->password)) {
                return back()->withErrors(['current_password' => 'Current password is incorrect.']);
            }

            // Update password
            $customer->update([
                'password' => Hash::make($validated['new_password'])
            ]);

            return redirect()->route('customer.profile')
                ->with('success', 'Password changed successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['current_password' => $e->getMessage()]);
        }
    }

    public function activity()
    {
        $customer = Auth::user();
        // Activity tracking would be implemented here
        $activity = [];

        return view('customer.activity', compact('activity'));
    }
}
