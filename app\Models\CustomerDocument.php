<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class CustomerDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'project_id',
        'title',
        'description',
        'category',
        'filename',
        'file_path',
        'file_size',
        'mime_type',
        'is_public',
        'uploaded_by',
        'download_count',
    ];

    protected $casts = [
        'file_size' => 'integer',
        'is_public' => 'boolean',
        'download_count' => 'integer',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    public function scopeByCustomer($query, int $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function scopeByProject($query, int $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function getFileSizeHumanAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2) . ' ' . $units[$i];
    }

    public function getFileExtensionAttribute(): string
    {
        return pathinfo($this->filename, PATHINFO_EXTENSION);
    }

    public function getFileIconAttribute(): string
    {
        $extension = strtolower($this->file_extension);

        return match ($extension) {
            'pdf' => 'file-pdf',
            'doc', 'docx' => 'file-word',
            'xls', 'xlsx' => 'file-excel',
            'ppt', 'pptx' => 'file-powerpoint',
            'jpg', 'jpeg', 'png', 'gif' => 'file-image',
            'mp4', 'avi', 'mov' => 'file-video',
            'mp3', 'wav' => 'file-audio',
            'zip', 'rar' => 'file-archive',
            default => 'file',
        };
    }

    public function getDownloadUrlAttribute(): string
    {
        return route('customer.documents.download', $this);
    }

    public function incrementDownloadCount(): void
    {
        $this->increment('download_count');
    }

    public function canBeDownloadedBy(User $user): bool
    {
        // Customer can download their own documents
        if ($this->customer_id === $user->id) {
            return true;
        }

        // Staff can download any document
        if (in_array($user->role, ['admin', 'staff', 'manager'])) {
            return true;
        }

        // Public documents can be downloaded by anyone
        return $this->is_public;
    }

    public function delete()
    {
        // Delete the physical file when the record is deleted
        if ($this->file_path && Storage::exists($this->file_path)) {
            Storage::delete($this->file_path);
        }

        return parent::delete();
    }
}
