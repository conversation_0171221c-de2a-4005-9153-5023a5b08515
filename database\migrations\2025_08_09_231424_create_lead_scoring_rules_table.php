<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_scoring_rules', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('rule_type', ['demographic', 'behavioral', 'engagement', 'firmographic']);
            $table->string('field_name'); // The field to evaluate (email, company, etc.)
            $table->enum('operator', ['equals', 'contains', 'starts_with', 'ends_with', 'greater_than', 'less_than', 'in_list', 'not_in_list']);
            $table->text('field_value'); // Value to compare against
            $table->integer('score_points'); // Points to add/subtract
            $table->boolean('is_positive')->default(true); // Add or subtract points
            $table->integer('priority')->default(0); // Rule execution order
            $table->boolean('is_active')->default(true);
            $table->json('conditions')->nullable(); // Additional complex conditions
            $table->timestamps();

            $table->index(['rule_type', 'is_active']);
            $table->index(['field_name', 'is_active']);
            $table->index('priority');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_scoring_rules');
    }
};
