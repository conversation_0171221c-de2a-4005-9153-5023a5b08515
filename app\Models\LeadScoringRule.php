<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LeadScoringRule extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'rule_type',
        'field_name',
        'operator',
        'field_value',
        'score_points',
        'is_positive',
        'priority',
        'is_active',
        'conditions',
    ];

    protected $casts = [
        'conditions' => 'array',
        'is_positive' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('rule_type', $type);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('priority', 'asc');
    }

    // Methods
    public function evaluateForLead(Lead $lead): int
    {
        if (!$this->is_active) {
            return 0;
        }

        $fieldValue = $this->getFieldValue($lead);

        if ($this->matchesCondition($fieldValue)) {
            return $this->is_positive ? $this->score_points : -$this->score_points;
        }

        return 0;
    }

    protected function getFieldValue(Lead $lead)
    {
        return match ($this->field_name) {
            'email' => $lead->email,
            'company' => $lead->company,
            'phone' => $lead->phone,
            'source' => $lead->source,
            'service_interest' => $lead->service_interest,
            'message_length' => strlen($lead->message ?? ''),
            'created_at' => $lead->created_at,
            default => data_get($lead, $this->field_name),
        };
    }

    protected function matchesCondition($value): bool
    {
        return match ($this->operator) {
            'equals' => $value == $this->field_value,
            'contains' => str_contains(strtolower($value), strtolower($this->field_value)),
            'starts_with' => str_starts_with(strtolower($value), strtolower($this->field_value)),
            'ends_with' => str_ends_with(strtolower($value), strtolower($this->field_value)),
            'greater_than' => $value > $this->field_value,
            'less_than' => $value < $this->field_value,
            'in_list' => in_array($value, explode(',', $this->field_value)),
            'not_in_list' => !in_array($value, explode(',', $this->field_value)),
            default => false,
        };
    }

    public static function calculateLeadScore(Lead $lead): int
    {
        $totalScore = 0;
        $rules = static::active()->ordered()->get();

        foreach ($rules as $rule) {
            $score = $rule->evaluateForLead($lead);
            $totalScore += $score;

            \Log::debug('Lead scoring rule applied', [
                'lead_id' => $lead->id,
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
                'score_added' => $score,
                'total_score' => $totalScore,
            ]);
        }

        return max(0, $totalScore); // Ensure score is not negative
    }

    public static function getDefaultRules(): array
    {
        return [
            [
                'name' => 'Business Email Domain',
                'description' => 'Higher score for business email domains',
                'rule_type' => 'demographic',
                'field_name' => 'email',
                'operator' => 'not_in_list',
                'field_value' => 'gmail.com,yahoo.com,hotmail.com,outlook.com',
                'score_points' => 20,
                'is_positive' => true,
                'priority' => 1,
            ],
            [
                'name' => 'Company Provided',
                'description' => 'Lead provided company information',
                'rule_type' => 'demographic',
                'field_name' => 'company',
                'operator' => 'contains',
                'field_value' => 'a', // Any company name will contain at least one character
                'score_points' => 15,
                'is_positive' => true,
                'priority' => 2,
            ],
            [
                'name' => 'Phone Number Provided',
                'description' => 'Lead provided phone number',
                'rule_type' => 'demographic',
                'field_name' => 'phone',
                'operator' => 'contains',
                'field_value' => '0', // Phone numbers will contain digits
                'score_points' => 10,
                'is_positive' => true,
                'priority' => 3,
            ],
            [
                'name' => 'Detailed Message',
                'description' => 'Lead provided detailed message (>100 characters)',
                'rule_type' => 'behavioral',
                'field_name' => 'message_length',
                'operator' => 'greater_than',
                'field_value' => '100',
                'score_points' => 15,
                'is_positive' => true,
                'priority' => 4,
            ],
        ];
    }
}
