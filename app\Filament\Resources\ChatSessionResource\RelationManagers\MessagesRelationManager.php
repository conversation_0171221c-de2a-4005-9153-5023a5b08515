<?php

namespace App\Filament\Resources\ChatSessionResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MessagesRelationManager extends RelationManager
{
    protected static string $relationship = 'messages';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('sender_type')
                    ->options([
                        'visitor' => 'Visitor',
                        'agent' => 'Agent',
                        'bot' => 'Bot',
                        'system' => 'System',
                    ])
                    ->required(),
                Forms\Components\TextInput::make('sender_name')
                    ->label('Sender Name'),
                Forms\Components\Select::make('message_type')
                    ->options([
                        'text' => 'Text',
                        'file' => 'File',
                        'image' => 'Image',
                        'system' => 'System',
                    ])
                    ->default('text')
                    ->required(),
                Forms\Components\Textarea::make('message')
                    ->required()
                    ->rows(4),
                Forms\Components\Toggle::make('is_internal')
                    ->label('Internal Note')
                    ->helperText('Internal notes are only visible to agents'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('message')
            ->columns([
                BadgeColumn::make('sender_type')
                    ->colors([
                        'primary' => 'visitor',
                        'success' => 'agent',
                        'warning' => 'bot',
                        'secondary' => 'system',
                    ]),
                TextColumn::make('sender_name')
                    ->label('Sender')
                    ->default('Unknown'),
                TextColumn::make('message')
                    ->label('Message')
                    ->limit(100)
                    ->wrap(),
                BadgeColumn::make('message_type')
                    ->colors([
                        'primary' => 'text',
                        'warning' => 'file',
                        'success' => 'image',
                        'secondary' => 'system',
                    ]),
                TextColumn::make('sent_at')
                    ->label('Sent')
                    ->dateTime()
                    ->sortable(),
                BadgeColumn::make('is_read')
                    ->label('Read')
                    ->formatStateUsing(fn ($state) => $state ? 'Read' : 'Unread')
                    ->colors([
                        'success' => fn ($state) => $state,
                        'danger' => fn ($state) => !$state,
                    ]),
                BadgeColumn::make('is_internal')
                    ->label('Internal')
                    ->formatStateUsing(fn ($state) => $state ? 'Internal' : 'Public')
                    ->colors([
                        'warning' => fn ($state) => $state,
                        'primary' => fn ($state) => !$state,
                    ]),
            ])
            ->filters([
                SelectFilter::make('sender_type')
                    ->options([
                        'visitor' => 'Visitor',
                        'agent' => 'Agent',
                        'bot' => 'Bot',
                        'system' => 'System',
                    ]),
                SelectFilter::make('message_type')
                    ->options([
                        'text' => 'Text',
                        'file' => 'File',
                        'image' => 'Image',
                        'system' => 'System',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['sender_id'] = auth()->id();
                        $data['sender_name'] = auth()->user()->name;
                        $data['sent_at'] = now();
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sent_at', 'asc');
    }
}
