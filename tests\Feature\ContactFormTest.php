<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Lead;

class ContactFormTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_contact_page_loads_successfully()
    {
        $response = $this->get('/contact');
        $response->assertStatus(200);
        $response->assertViewIs('contact');
    }

    public function test_contact_form_submission_creates_lead()
    {
        $formData = [
            'name' => '<PERSON> Do<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '+91 9876543210',
            'service' => 'web_development',
            'message' => 'This is a test message for contact form submission.'
        ];

        $response = $this->postJson('/contact', $formData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        $this->assertDatabaseHas('leads', [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'service_interest' => 'web_development',
            'source' => 'website'
        ]);
    }

    public function test_contact_form_validation_errors()
    {
        $response = $this->postJson('/contact', []);

        $response->assertStatus(422);
        $response->assertJson(['success' => false]);
        $response->assertJsonStructure(['errors']);
    }

    public function test_contact_form_email_validation()
    {
        $formData = [
            'name' => 'John Doe',
            'email' => 'invalid-email',
            'service' => 'web_development',
            'message' => 'Test message'
        ];

        $response = $this->postJson('/contact', $formData);
        $response->assertStatus(422);
        $response->assertJson(['success' => false]);
        $response->assertJsonStructure(['errors']);
    }

    public function test_contact_form_message_too_long_validation()
    {
        $formData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'service' => 'web_development',
            'message' => str_repeat('This is a very long message. ', 100) // Over 2000 characters
        ];

        $response = $this->postJson('/contact', $formData);
        $response->assertStatus(422);
        $response->assertJson(['success' => false]);
        $response->assertJsonStructure(['errors']);
    }

    public function test_contact_form_ajax_submission()
    {
        $formData = [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
            'service' => 'mobile_development',
            'message' => 'This is a test AJAX submission.'
        ];

        $response = $this->postJson('/contact', $formData);

        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
        $response->assertJsonStructure(['message']);

        $this->assertDatabaseHas('leads', [
            'name' => 'Jane Doe',
            'email' => '<EMAIL>'
        ]);
    }
}
