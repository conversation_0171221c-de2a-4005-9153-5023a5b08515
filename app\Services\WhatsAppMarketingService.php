<?php

namespace App\Services;

use App\Models\WhatsAppNumber;
use App\Models\WhatsAppMessage;
use App\Models\WhatsAppCampaign;
use App\Models\Lead;
use App\Models\Contact;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class WhatsAppMarketingService
{
    protected string $apiVersion = 'v18.0';
    protected string $baseUrl = 'https://graph.facebook.com';

    public function sendMessage(array $data): WhatsAppMessage
    {
        $whatsappNumber = $this->getAvailableNumber();
        
        if (!$whatsappNumber) {
            throw new \Exception('No available WhatsApp numbers');
        }

        $message = WhatsAppMessage::create([
            'whatsapp_number_id' => $whatsappNumber->id,
            'recipient_phone' => $this->formatPhoneNumber($data['phone']),
            'recipient_name' => $data['name'] ?? null,
            'message_type' => $data['type'] ?? 'text',
            'message_content' => $data['message'],
            'template_data' => $data['template_data'] ?? null,
            'media_data' => $data['media_data'] ?? null,
            'interactive_data' => $data['interactive_data'] ?? null,
            'campaign_id' => $data['campaign_id'] ?? null,
            'lead_id' => $data['lead_id'] ?? null,
            'is_automated' => $data['is_automated'] ?? false,
        ]);

        try {
            $response = $this->sendWhatsAppMessage($whatsappNumber, $message);
            
            if ($response['success']) {
                $message->markAsSent($response['message_id']);
                $whatsappNumber->incrementMessageCount();
                
                Log::info('WhatsApp message sent successfully', [
                    'message_id' => $message->id,
                    'whatsapp_message_id' => $response['message_id'],
                    'recipient' => $message->recipient_phone,
                ]);
            } else {
                $message->markAsFailed($response['error']);
                
                Log::error('WhatsApp message failed', [
                    'message_id' => $message->id,
                    'error' => $response['error'],
                ]);
            }
        } catch (\Exception $e) {
            $message->markAsFailed($e->getMessage());
            throw $e;
        }

        return $message;
    }

    protected function sendWhatsAppMessage(WhatsAppNumber $whatsappNumber, WhatsAppMessage $message): array
    {
        $url = "{$this->baseUrl}/{$this->apiVersion}/{$whatsappNumber->phone_number_id}/messages";
        
        $payload = $this->buildMessagePayload($message);
        
        $response = Http::withToken($whatsappNumber->access_token)
            ->post($url, $payload);

        if ($response->successful()) {
            $data = $response->json();
            return [
                'success' => true,
                'message_id' => $data['messages'][0]['id'] ?? null,
                'response' => $data,
            ];
        } else {
            $error = $response->json();
            return [
                'success' => false,
                'error' => $error['error']['message'] ?? 'Unknown error',
                'response' => $error,
            ];
        }
    }

    protected function buildMessagePayload(WhatsAppMessage $message): array
    {
        $payload = [
            'messaging_product' => 'whatsapp',
            'to' => $message->getFormattedPhone(),
        ];

        switch ($message->message_type) {
            case 'text':
                $payload['type'] = 'text';
                $payload['text'] = ['body' => $message->message_content];
                break;

            case 'template':
                $payload['type'] = 'template';
                $payload['template'] = $message->template_data;
                break;

            case 'media':
                $mediaData = $message->media_data;
                $payload['type'] = $mediaData['type']; // image, video, document, audio
                $payload[$mediaData['type']] = [
                    'link' => $mediaData['url'],
                    'caption' => $mediaData['caption'] ?? null,
                ];
                break;

            case 'interactive':
                $payload['type'] = 'interactive';
                $payload['interactive'] = $message->interactive_data;
                break;

            case 'location':
                $payload['type'] = 'location';
                $payload['location'] = $message->interactive_data;
                break;
        }

        return $payload;
    }

    public function sendBulkMessages(array $recipients, array $messageData): array
    {
        $results = [
            'sent' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        foreach ($recipients as $recipient) {
            try {
                $data = array_merge($messageData, [
                    'phone' => $recipient['phone'],
                    'name' => $recipient['name'] ?? null,
                    'lead_id' => $recipient['lead_id'] ?? null,
                ]);

                // Personalize message content
                if (isset($recipient['variables'])) {
                    $data['message'] = $this->personalizeMessage($messageData['message'], $recipient['variables']);
                }

                $this->sendMessage($data);
                $results['sent']++;

            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'phone' => $recipient['phone'],
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    protected function personalizeMessage(string $message, array $variables): string
    {
        foreach ($variables as $key => $value) {
            $message = str_replace("{{$key}}", $value, $message);
        }

        return $message;
    }

    public function sendToLeads(array $leadIds, array $messageData): array
    {
        $leads = Lead::whereIn('id', $leadIds)
            ->whereNotNull('phone')
            ->get();

        $recipients = $leads->map(function ($lead) {
            return [
                'phone' => $lead->phone,
                'name' => $lead->name,
                'lead_id' => $lead->id,
                'variables' => [
                    'name' => $lead->name,
                    'company' => $lead->company,
                    'service_interest' => $lead->service_interest,
                ],
            ];
        })->toArray();

        return $this->sendBulkMessages($recipients, $messageData);
    }

    public function createCampaign(array $data): WhatsAppCampaign
    {
        return WhatsAppCampaign::create([
            'name' => $data['name'],
            'message_content' => $data['message'],
            'message_type' => $data['type'] ?? 'text',
            'template_data' => $data['template_data'] ?? null,
            'media_data' => $data['media_data'] ?? null,
            'target_audience' => $data['target_audience'] ?? [],
            'scheduled_at' => $data['scheduled_at'] ?? null,
            'status' => 'draft',
            'created_by' => auth()->id(),
        ]);
    }

    public function executeCampaign(WhatsAppCampaign $campaign): array
    {
        if ($campaign->status !== 'scheduled' && $campaign->status !== 'draft') {
            throw new \Exception('Campaign is not ready to execute');
        }

        $recipients = $this->getCampaignRecipients($campaign);
        
        $messageData = [
            'message' => $campaign->message_content,
            'type' => $campaign->message_type,
            'template_data' => $campaign->template_data,
            'media_data' => $campaign->media_data,
            'campaign_id' => $campaign->id,
            'is_automated' => true,
        ];

        $results = $this->sendBulkMessages($recipients, $messageData);

        // Update campaign statistics
        $campaign->update([
            'status' => 'sent',
            'sent_at' => now(),
            'recipient_count' => count($recipients),
            'sent_count' => $results['sent'],
            'failed_count' => $results['failed'],
        ]);

        return $results;
    }

    protected function getCampaignRecipients(WhatsAppCampaign $campaign): array
    {
        $audience = $campaign->target_audience;
        $recipients = [];

        if (isset($audience['leads'])) {
            $leads = Lead::whereIn('id', $audience['leads'])
                ->whereNotNull('phone')
                ->get();

            foreach ($leads as $lead) {
                $recipients[] = [
                    'phone' => $lead->phone,
                    'name' => $lead->name,
                    'lead_id' => $lead->id,
                    'variables' => [
                        'name' => $lead->name,
                        'company' => $lead->company,
                        'service_interest' => $lead->service_interest,
                    ],
                ];
            }
        }

        if (isset($audience['contacts'])) {
            $contacts = Contact::whereIn('id', $audience['contacts'])
                ->whereNotNull('phone')
                ->get();

            foreach ($contacts as $contact) {
                $recipients[] = [
                    'phone' => $contact->phone,
                    'name' => $contact->name,
                    'variables' => [
                        'name' => $contact->name,
                        'company' => $contact->company,
                    ],
                ];
            }
        }

        if (isset($audience['phone_numbers'])) {
            foreach ($audience['phone_numbers'] as $phoneData) {
                $recipients[] = [
                    'phone' => $phoneData['phone'],
                    'name' => $phoneData['name'] ?? null,
                    'variables' => $phoneData['variables'] ?? [],
                ];
            }
        }

        return $recipients;
    }

    protected function getAvailableNumber(): ?WhatsAppNumber
    {
        return WhatsAppNumber::getNextAvailable();
    }

    protected function formatPhoneNumber(string $phone): string
    {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // Add India country code if not present
        if (!str_starts_with($phone, '91') && strlen($phone) === 10) {
            $phone = '91' . $phone;
        }
        
        return $phone;
    }

    public function handleWebhook(array $data): void
    {
        try {
            if (isset($data['entry'][0]['changes'][0]['value'])) {
                $value = $data['entry'][0]['changes'][0]['value'];
                
                if (isset($value['statuses'])) {
                    $this->handleStatusUpdate($value['statuses']);
                }
                
                if (isset($value['messages'])) {
                    $this->handleIncomingMessage($value['messages']);
                }
            }
        } catch (\Exception $e) {
            Log::error('WhatsApp webhook processing failed', [
                'error' => $e->getMessage(),
                'data' => $data,
            ]);
        }
    }

    protected function handleStatusUpdate(array $statuses): void
    {
        foreach ($statuses as $status) {
            $message = WhatsAppMessage::where('whatsapp_message_id', $status['id'])->first();
            
            if ($message) {
                switch ($status['status']) {
                    case 'delivered':
                        $message->markAsDelivered();
                        break;
                    case 'read':
                        $message->markAsRead();
                        break;
                    case 'failed':
                        $message->markAsFailed($status['errors'][0]['title'] ?? 'Message failed');
                        break;
                }

                $message->update([
                    'webhook_data' => array_merge($message->webhook_data ?? [], [$status])
                ]);
            }
        }
    }

    protected function handleIncomingMessage(array $messages): void
    {
        foreach ($messages as $messageData) {
            // Handle incoming messages (replies)
            // This could trigger automation workflows or create interaction records

            Log::info('Incoming WhatsApp message received', [
                'from' => $messageData['from'],
                'message_id' => $messageData['id'],
                'type' => $messageData['type'],
            ]);

            // Find lead by phone number and create interaction
            $phone = $this->formatPhoneNumber($messageData['from']);
            $lead = Lead::where('phone', $phone)->first();

            if ($lead) {
                $lead->interactions()->create([
                    'type' => 'whatsapp_reply',
                    'channel' => 'whatsapp',
                    'description' => 'Received WhatsApp reply',
                    'metadata' => [
                        'message_id' => $messageData['id'],
                        'message_type' => $messageData['type'],
                        'message_content' => $messageData['text']['body'] ?? null,
                    ],
                    'interaction_date' => now(),
                    'is_automated' => false,
                ]);

                // Update lead score for engagement
                app(LeadScoringService::class)->updateLeadScore($lead);
            }
        }
    }

    public function getMessageTemplates(): array
    {
        return [
            'welcome' => [
                'name' => 'welcome_message',
                'language' => 'en',
                'components' => [
                    [
                        'type' => 'header',
                        'parameters' => [
                            ['type' => 'text', 'text' => '{{company_name}}']
                        ]
                    ],
                    [
                        'type' => 'body',
                        'parameters' => [
                            ['type' => 'text', 'text' => '{{customer_name}}'],
                            ['type' => 'text', 'text' => '{{service_interest}}']
                        ]
                    ]
                ]
            ],
            'follow_up' => [
                'name' => 'follow_up_message',
                'language' => 'en',
                'components' => [
                    [
                        'type' => 'body',
                        'parameters' => [
                            ['type' => 'text', 'text' => '{{customer_name}}'],
                            ['type' => 'text', 'text' => '{{days_since_contact}}']
                        ]
                    ]
                ]
            ],
            'quote_ready' => [
                'name' => 'quote_ready',
                'language' => 'en',
                'components' => [
                    [
                        'type' => 'body',
                        'parameters' => [
                            ['type' => 'text', 'text' => '{{customer_name}}'],
                            ['type' => 'text', 'text' => '{{project_type}}'],
                            ['type' => 'text', 'text' => '{{quote_amount}}']
                        ]
                    ]
                ]
            ]
        ];
    }

    public function sendTemplateMessage(string $phone, string $templateName, array $parameters = []): WhatsAppMessage
    {
        $templates = $this->getMessageTemplates();

        if (!isset($templates[$templateName])) {
            throw new \Exception("Template '{$templateName}' not found");
        }

        $template = $templates[$templateName];

        // Replace parameters in template
        foreach ($template['components'] as &$component) {
            if (isset($component['parameters'])) {
                foreach ($component['parameters'] as &$param) {
                    $key = str_replace(['{{', '}}'], '', $param['text']);
                    if (isset($parameters[$key])) {
                        $param['text'] = $parameters[$key];
                    }
                }
            }
        }

        return $this->sendMessage([
            'phone' => $phone,
            'type' => 'template',
            'message' => "Template: {$templateName}",
            'template_data' => $template,
            'is_automated' => true,
        ]);
    }

    public function getCampaignAnalytics(WhatsAppCampaign $campaign): array
    {
        $messages = WhatsAppMessage::where('campaign_id', $campaign->id)->get();

        $analytics = [
            'total_sent' => $messages->where('status', '!=', 'pending')->count(),
            'delivered' => $messages->where('status', 'delivered')->count(),
            'read' => $messages->where('status', 'read')->count(),
            'failed' => $messages->where('status', 'failed')->count(),
            'delivery_rate' => 0,
            'read_rate' => 0,
            'response_rate' => 0,
        ];

        if ($analytics['total_sent'] > 0) {
            $analytics['delivery_rate'] = ($analytics['delivered'] / $analytics['total_sent']) * 100;
            $analytics['read_rate'] = ($analytics['read'] / $analytics['total_sent']) * 100;
        }

        // Calculate response rate (replies received)
        $responses = $messages->filter(function ($message) {
            return $message->lead &&
                   $message->lead->interactions()
                       ->where('type', 'whatsapp_reply')
                       ->where('interaction_date', '>', $message->sent_at)
                       ->exists();
        })->count();

        if ($analytics['total_sent'] > 0) {
            $analytics['response_rate'] = ($responses / $analytics['total_sent']) * 100;
        }

        return $analytics;
    }
}
