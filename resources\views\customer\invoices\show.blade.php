@extends('layouts.customer')

@section('title', 'Invoice #' . $invoice->number)

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Invoice #{{ $invoice->number }}</h1>
                    <p class="text-sm text-gray-600 mt-1">
                        Issued on {{ $invoice->created_at->format('F d, Y') }}
                        @if($invoice->due_date)
                            • Due {{ $invoice->due_date->format('F d, Y') }}
                        @endif
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        @if($invoice->status === 'paid') bg-green-100 text-green-800
                        @elseif($invoice->status === 'pending') bg-yellow-100 text-yellow-800
                        @elseif($invoice->status === 'overdue') bg-red-100 text-red-800
                        @elseif($invoice->status === 'cancelled') bg-gray-100 text-gray-800
                        @else bg-blue-100 text-blue-800
                        @endif">
                        {{ ucfirst($invoice->status) }}
                    </span>
                    <a href="{{ route('customer.invoices') }}" 
                       class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Invoices
                    </a>
                </div>
            </div>
        </div>

        <!-- Invoice Content -->
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- From -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">From</h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="font-semibold text-gray-900">Bhavitech Solutions</div>
                        <div class="text-gray-600 mt-1">
                            {{ config('company.address', 'Your Company Address') }}<br>
                            {{ config('company.city', 'Your City') }}, {{ config('company.state', 'Your State') }} {{ config('company.postal_code', 'Postal Code') }}<br>
                            {{ config('company.country', 'Country') }}
                        </div>
                        <div class="text-gray-600 mt-2">
                            <div>Phone: {{ config('company.phone', '+91 XXXXXXXXXX') }}</div>
                            <div>Email: {{ config('company.email', '<EMAIL>') }}</div>
                            @if(config('company.website'))
                                <div>Website: {{ config('company.website') }}</div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- To -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Bill To</h3>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="font-semibold text-gray-900">{{ $invoice->customer->name }}</div>
                        <div class="text-gray-600 mt-1">
                            @if($invoice->customer->company)
                                {{ $invoice->customer->company }}<br>
                            @endif
                            @if($invoice->customer->address)
                                {{ $invoice->customer->address }}
                            @endif
                        </div>
                        <div class="text-gray-600 mt-2">
                            <div>Email: {{ $invoice->customer->email }}</div>
                            @if($invoice->customer->phone)
                                <div>Phone: {{ $invoice->customer->phone }}</div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Details -->
            @if($invoice->description)
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Description</h3>
                <div class="bg-gray-50 rounded-lg p-4">
                    <p class="text-gray-700">{{ $invoice->description }}</p>
                </div>
            </div>
            @endif

            <!-- Invoice Items -->
            @if(isset($invoice->items) && is_array($invoice->items) && count($invoice->items) > 0)
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Items</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($invoice->items as $item)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['description'] ?? '' }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item['quantity'] ?? 1 }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{{ number_format($item['rate'] ?? 0, 2) }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{{ number_format(($item['quantity'] ?? 1) * ($item['rate'] ?? 0), 2) }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif

            <!-- Amount Summary -->
            <div class="border-t border-gray-200 pt-6">
                <div class="flex justify-end">
                    <div class="w-full max-w-md">
                        <div class="bg-gray-50 rounded-lg p-6">
                            @if(isset($invoice->subtotal))
                            <div class="flex justify-between py-2">
                                <span class="text-gray-600">Subtotal:</span>
                                <span class="text-gray-900">₹{{ number_format($invoice->subtotal, 2) }}</span>
                            </div>
                            @endif
                            
                            @if(isset($invoice->tax_amount) && $invoice->tax_amount > 0)
                            <div class="flex justify-between py-2">
                                <span class="text-gray-600">Tax {{ isset($invoice->tax_percentage) ? '(' . $invoice->tax_percentage . '%)' : '' }}:</span>
                                <span class="text-gray-900">₹{{ number_format($invoice->tax_amount, 2) }}</span>
                            </div>
                            @endif
                            
                            @if(isset($invoice->discount_amount) && $invoice->discount_amount > 0)
                            <div class="flex justify-between py-2">
                                <span class="text-gray-600">Discount:</span>
                                <span class="text-red-600">-₹{{ number_format($invoice->discount_amount, 2) }}</span>
                            </div>
                            @endif
                            
                            <div class="border-t border-gray-200 mt-4 pt-4">
                                <div class="flex justify-between">
                                    <span class="text-lg font-semibold text-gray-900">Total Amount:</span>
                                    <span class="text-lg font-semibold text-gray-900">₹{{ number_format($invoice->amount, 2) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            @if($invoice->status === 'paid' && $invoice->payment_date)
            <div class="mt-8">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-green-800">Payment Received</h3>
                            <div class="mt-1 text-sm text-green-700">
                                <p>This invoice was paid on {{ $invoice->payment_date->format('F d, Y') }}
                                @if($invoice->payment_method)
                                    via {{ ucfirst($invoice->payment_method) }}
                                @endif
                                @if($invoice->transaction_id)
                                    (Transaction ID: {{ $invoice->transaction_id }})
                                @endif
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Payment Instructions -->
            @if($invoice->status === 'pending' || $invoice->status === 'overdue')
            <div class="mt-8">
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-blue-800 mb-2">Payment Instructions</h3>
                    <div class="text-sm text-blue-700">
                        <p class="mb-2">Please make payment using one of the following methods:</p>
                        <ul class="list-disc list-inside space-y-1">
                            <li>Online payment via our customer portal</li>
                            <li>Bank transfer to our account (details will be provided)</li>
                            <li>UPI payment to our registered UPI ID</li>
                            <li>Contact us for other payment options</li>
                        </ul>
                        <p class="mt-2">
                            <strong>Questions?</strong> Contact us at {{ config('company.email', '<EMAIL>') }} 
                            or {{ config('company.phone', '+91 XXXXXXXXXX') }}
                        </p>
                    </div>
                </div>
            </div>
            @endif

            <!-- Actions -->
            <div class="mt-8 flex justify-end space-x-3">
                @if($invoice->status === 'pending' || $invoice->status === 'overdue')
                <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                    Pay Now
                </button>
                @endif
                
                <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                    </svg>
                    Download PDF
                </button>
                
                <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                    </svg>
                    Print
                </button>
            </div>
        </div>
    </div>

    <!-- Notes -->
    @if($invoice->notes)
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Notes</h3>
        <div class="text-gray-700">
            {{ $invoice->notes }}
        </div>
    </div>
    @endif
</div>
@endsection