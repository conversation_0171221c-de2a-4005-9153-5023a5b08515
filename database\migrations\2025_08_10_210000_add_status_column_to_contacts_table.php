<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations to add status column to contacts table.
     */
    public function up(): void
    {
        // Check if status column exists, if not add it
        if (!Schema::hasColumn('contacts', 'status')) {
            Schema::table('contacts', function (Blueprint $table) {
                $table->enum('status', ['subscribed', 'unsubscribed', 'bounced', 'complained', 'pending'])
                      ->default('subscribed')
                      ->after('country');
            });
        }

        // Add missing timestamp columns if they don't exist
        Schema::table('contacts', function (Blueprint $table) {
            if (!Schema::hasColumn('contacts', 'subscribed_at')) {
                $table->timestamp('subscribed_at')->nullable()->after('last_interaction_at');
            }
            if (!Schema::hasColumn('contacts', 'unsubscribed_at')) {
                $table->timestamp('unsubscribed_at')->nullable()->after('subscribed_at');
            }
            if (!Schema::hasColumn('contacts', 'bounced_at')) {
                $table->timestamp('bounced_at')->nullable()->after('unsubscribed_at');
            }
            if (!Schema::hasColumn('contacts', 'complained_at')) {
                $table->timestamp('complained_at')->nullable()->after('bounced_at');
            }
            if (!Schema::hasColumn('contacts', 'last_opened_at')) {
                $table->timestamp('last_opened_at')->nullable()->after('complained_at');
            }
            if (!Schema::hasColumn('contacts', 'last_clicked_at')) {
                $table->timestamp('last_clicked_at')->nullable()->after('last_opened_at');
            }

            // Add additional fields if they don't exist
            if (!Schema::hasColumn('contacts', 'postal_code')) {
                $table->string('postal_code')->nullable()->after('country');
            }
            if (!Schema::hasColumn('contacts', 'timezone')) {
                $table->string('timezone')->nullable()->after('postal_code');
            }
            if (!Schema::hasColumn('contacts', 'language')) {
                $table->string('language')->default('en')->after('timezone');
            }
            if (!Schema::hasColumn('contacts', 'notes')) {
                $table->text('notes')->nullable()->after('language');
            }
        });

        // Add indexes for the status column if they don't exist
        try {
            Schema::table('contacts', function (Blueprint $table) {
                $table->index(['status', 'is_active'], 'idx_contacts_status_active');
                $table->index(['status', 'engagement_score'], 'idx_contacts_status_engagement');
            });
        } catch (\Exception $e) {
            // Indexes might already exist, ignore the error
        }

        // Update existing records to have proper status based on is_subscribed
        DB::statement("
            UPDATE contacts
            SET status = CASE
                WHEN is_subscribed = 1 THEN 'subscribed'
                ELSE 'unsubscribed'
            END,
            subscribed_at = CASE
                WHEN is_subscribed = 1 AND subscribed_at IS NULL THEN created_at
                ELSE subscribed_at
            END,
            unsubscribed_at = CASE
                WHEN is_subscribed = 0 AND unsubscribed_at IS NULL THEN updated_at
                ELSE unsubscribed_at
            END
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contacts', function (Blueprint $table) {
            // Drop indexes first (if they exist)
            try {
                $table->dropIndex('idx_contacts_status_active');
                $table->dropIndex('idx_contacts_status_engagement');
            } catch (\Exception $e) {
                // Indexes might not exist, ignore the error
            }

            // Drop added columns (only if they exist)
            $columnsToCheck = [
                'status', 'subscribed_at', 'unsubscribed_at', 'bounced_at',
                'complained_at', 'last_opened_at', 'last_clicked_at',
                'postal_code', 'timezone', 'language', 'notes'
            ];

            foreach ($columnsToCheck as $column) {
                if (Schema::hasColumn('contacts', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
