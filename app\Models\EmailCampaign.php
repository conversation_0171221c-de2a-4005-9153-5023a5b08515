<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class EmailCampaign extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'subject',
        'content',
        'template_id',
        'status',
        'scheduled_at',
        'sent_at',
        'recipient_count',
        'sent_count',
        'delivered_count',
        'opened_count',
        'clicked_count',
        'bounced_count',
        'unsubscribed_count',
        'target_lists',
        'tags',
        'settings',
        'ab_test_config',
        'created_by',
    ];

    protected $casts = [
        'target_lists' => 'array',
        'tags' => 'array',
        'settings' => 'array',
        'ab_test_config' => 'array',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
    ];

    // Relationships
    public function template(): BelongsTo
    {
        return $this->belongsTo(EmailTemplate::class, 'template_id');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function recipients(): HasMany
    {
        return $this->hasMany(EmailCampaignRecipient::class, 'campaign_id');
    }

    // Scopes
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopeReadyToSend($query)
    {
        return $query->where('status', 'scheduled')
            ->where('scheduled_at', '<=', now());
    }

    // Methods
    public function isReadyToSend(): bool
    {
        return $this->status === 'scheduled' && $this->scheduled_at <= now();
    }

    public function markAsSent(): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    public function getOpenRate(): float
    {
        return $this->sent_count > 0 ? ($this->opened_count / $this->sent_count) * 100 : 0;
    }

    public function getClickRate(): float
    {
        return $this->sent_count > 0 ? ($this->clicked_count / $this->sent_count) * 100 : 0;
    }

    public function getBounceRate(): float
    {
        return $this->sent_count > 0 ? ($this->bounced_count / $this->sent_count) * 100 : 0;
    }

    public function getUnsubscribeRate(): float
    {
        return $this->sent_count > 0 ? ($this->unsubscribed_count / $this->sent_count) * 100 : 0;
    }

    public function getDeliveryRate(): float
    {
        return $this->sent_count > 0 ? ($this->delivered_count / $this->sent_count) * 100 : 0;
    }

    public function updateStats(array $stats): void
    {
        $this->update([
            'delivered_count' => $stats['delivered'] ?? $this->delivered_count,
            'opened_count' => $stats['opened'] ?? $this->opened_count,
            'clicked_count' => $stats['clicked'] ?? $this->clicked_count,
            'bounced_count' => $stats['bounced'] ?? $this->bounced_count,
            'unsubscribed_count' => $stats['unsubscribed'] ?? $this->unsubscribed_count,
        ]);
    }

    public function isABTest(): bool
    {
        return !empty($this->ab_test_config);
    }

    public function getWinningVariant(): ?string
    {
        if (!$this->isABTest()) {
            return null;
        }

        $config = $this->ab_test_config;
        $variantA = $config['variant_a'] ?? [];
        $variantB = $config['variant_b'] ?? [];

        $aPerformance = $this->calculateVariantPerformance($variantA);
        $bPerformance = $this->calculateVariantPerformance($variantB);

        return $aPerformance > $bPerformance ? 'A' : 'B';
    }

    protected function calculateVariantPerformance(array $variant): float
    {
        $sent = $variant['sent_count'] ?? 0;
        $opened = $variant['opened_count'] ?? 0;
        $clicked = $variant['clicked_count'] ?? 0;

        if ($sent === 0) {
            return 0;
        }

        // Weight clicks more than opens
        return (($opened / $sent) * 0.3) + (($clicked / $sent) * 0.7);
    }

    public function getPerformanceMetrics(): array
    {
        return [
            'sent_count' => $this->sent_count,
            'delivered_count' => $this->delivered_count,
            'opened_count' => $this->opened_count,
            'clicked_count' => $this->clicked_count,
            'bounced_count' => $this->bounced_count,
            'unsubscribed_count' => $this->unsubscribed_count,
            'open_rate' => round($this->getOpenRate(), 2),
            'click_rate' => round($this->getClickRate(), 2),
            'bounce_rate' => round($this->getBounceRate(), 2),
            'unsubscribe_rate' => round($this->getUnsubscribeRate(), 2),
            'delivery_rate' => round($this->getDeliveryRate(), 2),
        ];
    }
}
