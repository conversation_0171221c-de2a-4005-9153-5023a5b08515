<?php

namespace App\Jobs;

use App\Models\AutomationWorkflow;
use App\Models\Lead;
use App\Services\EmailAutomationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendAutomatedEmailJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    protected AutomationWorkflow $workflow;
    protected Lead $lead;
    protected int $stepIndex;

    /**
     * Create a new job instance.
     */
    public function __construct(AutomationWorkflow $workflow, Lead $lead, int $stepIndex)
    {
        $this->workflow = $workflow;
        $this->lead = $lead;
        $this->stepIndex = $stepIndex;
    }

    /**
     * Execute the job.
     */
    public function handle(EmailAutomationService $automationService): void
    {
        try {
            // Check if workflow is still active
            if (!$this->workflow->fresh()->canExecute()) {
                Log::info('Automation workflow no longer active, skipping step', [
                    'workflow_id' => $this->workflow->id,
                    'lead_id' => $this->lead->id,
                    'step_index' => $this->stepIndex,
                ]);
                return;
            }

            // Check if lead still exists and is valid
            $lead = $this->lead->fresh();
            if (!$lead) {
                Log::warning('Lead no longer exists for automation step', [
                    'workflow_id' => $this->workflow->id,
                    'lead_id' => $this->lead->id,
                    'step_index' => $this->stepIndex,
                ]);
                return;
            }

            // Get the workflow step
            $steps = $this->workflow->workflow_steps;
            if (!isset($steps[$this->stepIndex])) {
                Log::warning('Workflow step not found', [
                    'workflow_id' => $this->workflow->id,
                    'lead_id' => $lead->id,
                    'step_index' => $this->stepIndex,
                ]);
                return;
            }

            $step = $steps[$this->stepIndex];

            // Execute the step
            $automationService->executeWorkflowStep($this->workflow, $lead, $step);

            Log::info('Automated email workflow step executed', [
                'workflow_id' => $this->workflow->id,
                'lead_id' => $lead->id,
                'step_index' => $this->stepIndex,
                'action' => $step['action'] ?? 'unknown',
            ]);

        } catch (\Exception $e) {
            Log::error('Automated email job failed', [
                'workflow_id' => $this->workflow->id,
                'lead_id' => $this->lead->id,
                'step_index' => $this->stepIndex,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Automated email job failed permanently', [
            'workflow_id' => $this->workflow->id,
            'lead_id' => $this->lead->id,
            'step_index' => $this->stepIndex,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
