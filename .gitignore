/.phpunit.cache
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/vendor
.env
.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode

# Email Management System Specific
/storage/app/massive_imports/*
!/storage/app/massive_imports/.gitkeep
/storage/app/exports/*
!/storage/app/exports/.gitkeep
/storage/app/backups/*
!/storage/app/backups/.gitkeep
/storage/app/temp/*
!/storage/app/temp/.gitkeep

# Large CSV Files (over 10MB)
*.csv
!sample_*.csv
!/storage/app/sample_*.csv

# Email Campaign Assets
/storage/app/campaign-assets/*
!/storage/app/campaign-assets/.gitkeep

# Email Attachments
/storage/app/attachments/*
!/storage/app/attachments/.gitkeep

# Performance Logs
/storage/logs/performance/
/storage/logs/mysql/
/storage/logs/email-delivery/

# Database Backups
*.sql
*.sql.gz
*.dump
/backups/

# Email Provider Configurations (sensitive)
/config/providers/
/storage/app/provider-configs/

# WhatsApp Integration
/storage/app/whatsapp/media/*
!/storage/app/whatsapp/media/.gitkeep

# Testing & Development
/tests/coverage/
/tests/reports/
.php_cs.cache
.php-cs-fixer.cache

# OS & Editor
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# Maintenance
maintenance.flag
.maintenance
