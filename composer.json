{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "abraham/twitteroauth": "^6.1", "barryvdh/laravel-dompdf": "^2.2", "filament/filament": "^3.3", "filament/spatie-laravel-media-library-plugin": "^3.2", "filament/spatie-laravel-settings-plugin": "^3.2", "filament/spatie-laravel-tags-plugin": "^3.2", "google/apiclient": "^2.15", "guzzlehttp/guzzle": "*", "intervention/image": "*", "laravel/framework": "^11.0", "laravel/sanctum": "*", "laravel/scout": "*", "laravel/socialite": "*", "laravel/telescope": "^5.0", "laravel/tinker": "^2.9", "league/csv": "*", "livewire/livewire": "^3.4", "maatwebsite/excel": "^3.1", "openai-php/client": "^0.8.4", "phpoffice/phpspreadsheet": "^1.29", "predis/predis": "^3.2", "pusher/pusher-php-server": "*", "razorpay/razorpay": "^2.9", "spatie/laravel-activitylog": "^4.10", "spatie/laravel-analytics": "^5.6", "spatie/laravel-backup": "^8.6", "spatie/laravel-data": "^4.4", "spatie/laravel-medialibrary": "*", "spatie/laravel-permission": "^6.21", "spatie/laravel-query-builder": "^5.8", "spatie/laravel-sitemap": "^7.2", "spatie/laravel-webhook-client": "^3.3", "stripe/stripe-php": "^13.12", "twilio/sdk": "^7.16"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^10.5", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --ansi"]}, "extra": {"branch-alias": {"dev-master": "11.x-dev"}, "laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}