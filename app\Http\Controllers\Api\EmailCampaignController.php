<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\EmailCampaign;
use App\Models\EmailTemplate;
use App\Services\EmailMarketingService;
use App\Services\EmailAutomationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class EmailCampaignController extends Controller
{
    public function __construct(
        protected EmailMarketingService $emailService,
        protected EmailAutomationService $automationService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $campaigns = EmailCampaign::with(['template', 'creator'])
            ->when($request->status, fn($q) => $q->where('status', $request->status))
            ->when($request->search, fn($q) => $q->where('name', 'like', "%{$request->search}%"))
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $campaigns,
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'template_id' => 'nullable|exists:email_templates,id',
            'recipients' => 'required|array|min:1',
            'recipients.*.email' => 'required|email',
            'recipients.*.name' => 'nullable|string',
            'recipients.*.data' => 'nullable|array',
            'settings' => 'nullable|array',
            'ab_test_config' => 'nullable|array',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        $campaign = $this->emailService->createCampaign($validated);

        if ($validated['scheduled_at'] ?? null) {
            $this->emailService->scheduleCampaign(
                $campaign,
                new \DateTime($validated['scheduled_at'])
            );
        }

        return response()->json([
            'success' => true,
            'message' => 'Campaign created successfully',
            'data' => $campaign->load(['template', 'recipients']),
        ], 201);
    }

    public function show(EmailCampaign $campaign): JsonResponse
    {
        $campaign->load(['template', 'creator', 'recipients']);

        return response()->json([
            'success' => true,
            'data' => [
                'campaign' => $campaign,
                'analytics' => $this->emailService->getCampaignAnalytics($campaign),
            ],
        ]);
    }

    public function update(Request $request, EmailCampaign $campaign): JsonResponse
    {
        if ($campaign->status === 'sent') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot update sent campaigns',
            ], 400);
        }

        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'subject' => 'sometimes|string|max:255',
            'content' => 'sometimes|string',
            'template_id' => 'nullable|exists:email_templates,id',
            'settings' => 'nullable|array',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        $campaign->update($validated);

        if (isset($validated['scheduled_at'])) {
            $this->emailService->scheduleCampaign(
                $campaign,
                new \DateTime($validated['scheduled_at'])
            );
        }

        return response()->json([
            'success' => true,
            'message' => 'Campaign updated successfully',
            'data' => $campaign->fresh(),
        ]);
    }

    public function destroy(EmailCampaign $campaign): JsonResponse
    {
        if ($campaign->status === 'sent') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete sent campaigns',
            ], 400);
        }

        $campaign->delete();

        return response()->json([
            'success' => true,
            'message' => 'Campaign deleted successfully',
        ]);
    }

    public function send(EmailCampaign $campaign): JsonResponse
    {
        try {
            $results = $this->emailService->sendCampaign($campaign);

            return response()->json([
                'success' => true,
                'message' => 'Campaign sent successfully',
                'data' => $results,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send campaign: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function schedule(Request $request, EmailCampaign $campaign): JsonResponse
    {
        $validated = $request->validate([
            'scheduled_at' => 'required|date|after:now',
        ]);

        try {
            $this->emailService->scheduleCampaign(
                $campaign,
                new \DateTime($validated['scheduled_at'])
            );

            return response()->json([
                'success' => true,
                'message' => 'Campaign scheduled successfully',
                'data' => $campaign->fresh(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to schedule campaign: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function addRecipients(Request $request, EmailCampaign $campaign): JsonResponse
    {
        $validated = $request->validate([
            'recipients' => 'required|array|min:1',
            'recipients.*.email' => 'required|email',
            'recipients.*.name' => 'nullable|string',
            'recipients.*.data' => 'nullable|array',
        ]);

        $added = $this->emailService->addRecipientsToCampaign(
            $campaign,
            $validated['recipients']
        );

        return response()->json([
            'success' => true,
            'message' => "Added {$added} recipients to campaign",
            'data' => ['added_count' => $added],
        ]);
    }

    public function addLeads(Request $request, EmailCampaign $campaign): JsonResponse
    {
        $validated = $request->validate([
            'lead_ids' => 'nullable|array',
            'lead_ids.*' => 'exists:leads,id',
        ]);

        $added = $this->emailService->addLeadsToCampaign(
            $campaign,
            $validated['lead_ids'] ?? null
        );

        return response()->json([
            'success' => true,
            'message' => "Added {$added} leads to campaign",
            'data' => ['added_count' => $added],
        ]);
    }

    public function analytics(EmailCampaign $campaign): JsonResponse
    {
        $analytics = $this->emailService->getCampaignAnalytics($campaign);

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    public function templates(): JsonResponse
    {
        $templates = EmailTemplate::active()
            ->orderBy('usage_count', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $templates,
        ]);
    }
}
