<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Lead;
use App\Services\AutomatedLeadScoringService;
use App\Services\LeadIntelligenceService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LeadScoringController extends Controller
{
    public function __construct(
        protected AutomatedLeadScoringService $scoringService,
        protected LeadIntelligenceService $intelligenceService
    ) {}

    public function getLeadScore(Lead $lead): JsonResponse
    {
        $scoringResult = $this->scoringService->calculateLeadScore($lead);

        return response()->json([
            'success' => true,
            'data' => $scoringResult,
        ]);
    }

    public function updateLeadScore(Lead $lead): JsonResponse
    {
        try {
            $updatedLead = $this->scoringService->updateLeadScore($lead);

            return response()->json([
                'success' => true,
                'message' => 'Lead score updated successfully',
                'data' => [
                    'lead_id' => $updatedLead->id,
                    'old_score' => $lead->score,
                    'new_score' => $updatedLead->score,
                    'grade' => $updatedLead->grade,
                    'priority' => $updatedLead->priority,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update lead score: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function batchUpdateScores(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'lead_ids' => 'nullable|array',
            'lead_ids.*' => 'exists:leads,id',
            'force_update' => 'nullable|boolean',
        ]);

        try {
            $results = $this->scoringService->batchUpdateLeadScores(
                $validated['lead_ids'] ?? null
            );

            return response()->json([
                'success' => true,
                'message' => 'Batch lead scoring completed',
                'data' => $results,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Batch scoring failed: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function getLeadInsights(Lead $lead): JsonResponse
    {
        $insights = $this->intelligenceService->generateLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => $insights,
        ]);
    }

    public function getLeadProfile(Lead $lead): JsonResponse
    {
        $insights = $this->intelligenceService->generateLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => $insights['lead_profile'],
        ]);
    }

    public function getEngagementTimeline(Lead $lead): JsonResponse
    {
        $insights = $this->intelligenceService->generateLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => $insights['engagement_timeline'],
        ]);
    }

    public function getBehavioralPatterns(Lead $lead): JsonResponse
    {
        $insights = $this->intelligenceService->generateLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => $insights['behavioral_patterns'],
        ]);
    }

    public function getIntentSignals(Lead $lead): JsonResponse
    {
        $insights = $this->intelligenceService->generateLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => $insights['intent_signals'],
        ]);
    }

    public function getConversionProbability(Lead $lead): JsonResponse
    {
        $insights = $this->intelligenceService->generateLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => $insights['conversion_probability'],
        ]);
    }

    public function getOptimalContactTime(Lead $lead): JsonResponse
    {
        $insights = $this->intelligenceService->generateLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => $insights['optimal_contact_time'],
        ]);
    }

    public function getPersonalizationData(Lead $lead): JsonResponse
    {
        $insights = $this->intelligenceService->generateLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => $insights['personalization_data'],
        ]);
    }

    public function getRiskFactors(Lead $lead): JsonResponse
    {
        $insights = $this->intelligenceService->generateLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => $insights['risk_factors'],
        ]);
    }

    public function getOpportunityScore(Lead $lead): JsonResponse
    {
        $insights = $this->intelligenceService->generateLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => $insights['opportunity_score'],
        ]);
    }

    public function getLeadsByScore(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'min_score' => 'nullable|numeric|min:0|max:100',
            'max_score' => 'nullable|numeric|min:0|max:100',
            'grade' => 'nullable|in:A+,A,B+,B,C+,C,D,F',
            'priority' => 'nullable|in:urgent,high,medium,low,very_low',
            'limit' => 'nullable|integer|min:1|max:100',
        ]);

        $query = Lead::query();

        if (isset($validated['min_score'])) {
            $query->where('score', '>=', $validated['min_score']);
        }

        if (isset($validated['max_score'])) {
            $query->where('score', '<=', $validated['max_score']);
        }

        if (isset($validated['grade'])) {
            $query->where('grade', $validated['grade']);
        }

        if (isset($validated['priority'])) {
            $query->where('priority', $validated['priority']);
        }

        $leads = $query->orderBy('score', 'desc')
            ->limit($validated['limit'] ?? 50)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $leads,
        ]);
    }

    public function getScoreDistribution(): JsonResponse
    {
        $distribution = Lead::selectRaw('
            CASE
                WHEN score >= 80 THEN "80-100"
                WHEN score >= 60 THEN "60-79"
                WHEN score >= 40 THEN "40-59"
                WHEN score >= 20 THEN "20-39"
                ELSE "0-19"
            END as score_range,
            COUNT(*) as count
        ')
        ->groupBy('score_range')
        ->get();

        $gradeDistribution = Lead::select('grade', \DB::raw('count(*) as count'))
            ->groupBy('grade')
            ->get();

        $priorityDistribution = Lead::select('priority', \DB::raw('count(*) as count'))
            ->groupBy('priority')
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'score_ranges' => $distribution,
                'grade_distribution' => $gradeDistribution,
                'priority_distribution' => $priorityDistribution,
                'total_leads' => Lead::count(),
                'avg_score' => Lead::avg('score'),
            ],
        ]);
    }

    public function getScoringRules(): JsonResponse
    {
        // Return the scoring rules configuration
        $rules = [
            'demographic' => [
                'company_size' => ['weight' => 15, 'max_score' => 20],
                'industry_match' => ['weight' => 10, 'max_score' => 15],
                'location' => ['weight' => 5, 'max_score' => 10],
                'contact_completeness' => ['weight' => 8, 'max_score' => 10],
            ],
            'behavioral' => [
                'website_engagement' => ['weight' => 20, 'max_score' => 25],
                'email_engagement' => ['weight' => 15, 'max_score' => 20],
                'whatsapp_engagement' => ['weight' => 12, 'max_score' => 15],
                'social_media_engagement' => ['weight' => 8, 'max_score' => 10],
            ],
            'interaction' => [
                'response_speed' => ['weight' => 10, 'max_score' => 15],
                'interaction_frequency' => ['weight' => 12, 'max_score' => 15],
                'content_consumption' => ['weight' => 8, 'max_score' => 10],
                'form_submissions' => ['weight' => 15, 'max_score' => 20],
            ],
            'intent' => [
                'service_page_views' => ['weight' => 18, 'max_score' => 25],
                'pricing_inquiries' => ['weight' => 20, 'max_score' => 25],
                'quote_requests' => ['weight' => 25, 'max_score' => 30],
                'direct_contact_attempts' => ['weight' => 22, 'max_score' => 25],
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'scoring_rules' => $rules,
                'grade_thresholds' => [
                    'A+' => 80,
                    'A' => 70,
                    'B+' => 60,
                    'B' => 50,
                    'C+' => 40,
                    'C' => 30,
                    'D' => 20,
                    'F' => 0,
                ],
                'priority_mapping' => [
                    'urgent' => 'Score >= 70 and created within 3 days',
                    'high' => 'Score >= 60',
                    'medium' => 'Score >= 40',
                    'low' => 'Score >= 20',
                    'very_low' => 'Score < 20',
                ],
            ],
        ]);
    }

    public function getTopLeads(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'limit' => 'nullable|integer|min:1|max:50',
            'timeframe' => 'nullable|in:today,week,month,all',
        ]);

        $query = Lead::query();

        // Apply timeframe filter
        switch ($validated['timeframe'] ?? 'all') {
            case 'today':
                $query->whereDate('created_at', today());
                break;
            case 'week':
                $query->where('created_at', '>=', now()->subWeek());
                break;
            case 'month':
                $query->where('created_at', '>=', now()->subMonth());
                break;
        }

        $topLeads = $query->orderBy('score', 'desc')
            ->limit($validated['limit'] ?? 20)
            ->get();

        // Add insights for each lead
        $leadsWithInsights = $topLeads->map(function ($lead) {
            $scoringResult = $this->scoringService->calculateLeadScore($lead);
            return [
                'lead' => $lead,
                'scoring' => $scoringResult,
                'next_actions' => $scoringResult['next_actions'],
                'recommendations' => $scoringResult['recommendations'],
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $leadsWithInsights,
        ]);
    }

    public function getLeadRecommendations(Lead $lead): JsonResponse
    {
        $scoringResult = $this->scoringService->calculateLeadScore($lead);
        $insights = $this->intelligenceService->generateLeadInsights($lead);

        return response()->json([
            'success' => true,
            'data' => [
                'scoring_recommendations' => $scoringResult['recommendations'],
                'next_actions' => $scoringResult['next_actions'],
                'personalization_data' => $insights['personalization_data'],
                'optimal_contact_time' => $insights['optimal_contact_time'],
                'risk_factors' => $insights['risk_factors'],
                'content_recommendations' => $insights['personalization_data']['content_recommendations'] ?? [],
            ],
        ]);
    }

    public function exportLeadScores(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'format' => 'nullable|in:json,csv,excel',
            'filters' => 'nullable|array',
        ]);

        $query = Lead::query();

        // Apply filters if provided
        if (isset($validated['filters'])) {
            $filters = $validated['filters'];

            if (isset($filters['min_score'])) {
                $query->where('score', '>=', $filters['min_score']);
            }

            if (isset($filters['grade'])) {
                $query->where('grade', $filters['grade']);
            }

            if (isset($filters['priority'])) {
                $query->where('priority', $filters['priority']);
            }
        }

        $leads = $query->orderBy('score', 'desc')->get();

        $exportData = $leads->map(function ($lead) {
            return [
                'id' => $lead->id,
                'name' => $lead->name,
                'email' => $lead->email,
                'company' => $lead->company,
                'service_interest' => $lead->service_interest,
                'source' => $lead->source,
                'score' => $lead->score,
                'grade' => $lead->grade,
                'priority' => $lead->priority,
                'status' => $lead->status,
                'created_at' => $lead->created_at,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $exportData,
            'format' => $validated['format'] ?? 'json',
            'total_records' => $exportData->count(),
        ]);
    }
}
