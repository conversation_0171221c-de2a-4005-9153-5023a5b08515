<?php

namespace App\Services;

use App\Models\MetalRate;
use App\Models\MetalRateAlert;
use App\Models\MetalRateSubscription;
use App\Models\BusinessSetting;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class MetalRatesService
{
    protected array $supportedMetals = [
        'gold' => ['symbol' => 'XAU', 'unit' => 'gram', 'currency' => 'INR'],
        'silver' => ['symbol' => 'XAG', 'unit' => 'gram', 'currency' => 'INR'],
        'platinum' => ['symbol' => 'XPT', 'unit' => 'gram', 'currency' => 'INR'],
        'palladium' => ['symbol' => 'XPD', 'unit' => 'gram', 'currency' => 'INR'],
        'copper' => ['symbol' => 'HG', 'unit' => 'kg', 'currency' => 'INR'],
        'aluminum' => ['symbol' => 'ALI', 'unit' => 'kg', 'currency' => 'INR'],
        'zinc' => ['symbol' => 'ZN', 'unit' => 'kg', 'currency' => 'INR'],
        'nickel' => ['symbol' => 'NI', 'unit' => 'kg', 'currency' => 'INR'],
    ];

    protected array $apiProviders = [
        'metals_api' => [
            'base_url' => 'https://metals-api.com/api',
            'requires_key' => true,
        ],
        'fixer_io' => [
            'base_url' => 'https://api.fixer.io/v1',
            'requires_key' => true,
        ],
        'currencylayer' => [
            'base_url' => 'https://api.currencylayer.com',
            'requires_key' => true,
        ],
        'mock' => [
            'base_url' => 'mock',
            'requires_key' => false,
        ],
    ];

    public function fetchCurrentRates(array $metals = null): array
    {
        $metals = $metals ?? array_keys($this->supportedMetals);
        $provider = $this->getActiveProvider();
        
        try {
            $rates = $this->fetchFromProvider($provider, $metals);
            $this->storeRates($rates);
            
            return [
                'success' => true,
                'rates' => $rates,
                'provider' => $provider,
                'timestamp' => now(),
            ];
        } catch (\Exception $e) {
            Log::error('Failed to fetch metal rates', [
                'provider' => $provider,
                'metals' => $metals,
                'error' => $e->getMessage(),
            ]);

            // Fallback to cached rates
            return $this->getCachedRates($metals);
        }
    }

    protected function fetchFromProvider(string $provider, array $metals): array
    {
        return match ($provider) {
            'metals_api' => $this->fetchFromMetalsAPI($metals),
            'fixer_io' => $this->fetchFromFixerIO($metals),
            'currencylayer' => $this->fetchFromCurrencyLayer($metals),
            'mock' => $this->generateMockRates($metals),
            default => throw new \Exception("Unsupported provider: {$provider}"),
        };
    }

    protected function fetchFromMetalsAPI(array $metals): array
    {
        $apiKey = BusinessSetting::getValue('metals_api_key');
        if (!$apiKey) {
            throw new \Exception('Metals API key not configured');
        }

        $symbols = collect($metals)->map(fn($metal) => $this->supportedMetals[$metal]['symbol'])->join(',');
        
        $response = Http::timeout(30)->get($this->apiProviders['metals_api']['base_url'] . '/latest', [
            'access_key' => $apiKey,
            'base' => 'USD',
            'symbols' => $symbols,
        ]);

        if (!$response->successful()) {
            throw new \Exception('Metals API request failed: ' . $response->body());
        }

        $data = $response->json();
        
        if (!$data['success']) {
            throw new \Exception('Metals API error: ' . ($data['error']['info'] ?? 'Unknown error'));
        }

        return $this->convertToINR($data['rates'], $metals);
    }

    protected function fetchFromFixerIO(array $metals): array
    {
        $apiKey = BusinessSetting::getValue('fixer_io_api_key');
        if (!$apiKey) {
            throw new \Exception('Fixer.io API key not configured');
        }

        // Fixer.io doesn't have direct metal rates, so we'll use mock data
        return $this->generateMockRates($metals);
    }

    protected function fetchFromCurrencyLayer(array $metals): array
    {
        $apiKey = BusinessSetting::getValue('currencylayer_api_key');
        if (!$apiKey) {
            throw new \Exception('CurrencyLayer API key not configured');
        }

        // CurrencyLayer doesn't have direct metal rates, so we'll use mock data
        return $this->generateMockRates($metals);
    }

    protected function generateMockRates(array $metals): array
    {
        $baseRates = [
            'gold' => 6500,      // INR per gram
            'silver' => 85,      // INR per gram
            'platinum' => 3200,  // INR per gram
            'palladium' => 2800, // INR per gram
            'copper' => 750,     // INR per kg
            'aluminum' => 180,   // INR per kg
            'zinc' => 220,       // INR per kg
            'nickel' => 1800,    // INR per kg
        ];

        $rates = [];
        foreach ($metals as $metal) {
            if (isset($baseRates[$metal])) {
                // Add some random fluctuation (±2%)
                $fluctuation = (rand(-200, 200) / 10000);
                $rates[$metal] = round($baseRates[$metal] * (1 + $fluctuation), 2);
            }
        }

        return $rates;
    }

    protected function convertToINR(array $usdRates, array $metals): array
    {
        // Get USD to INR conversion rate
        $usdToInr = $this->getUSDToINRRate();
        
        $inrRates = [];
        foreach ($metals as $metal) {
            $symbol = $this->supportedMetals[$metal]['symbol'];
            if (isset($usdRates[$symbol])) {
                // Convert from USD per troy ounce to INR per gram
                $usdPerOunce = $usdRates[$symbol];
                $usdPerGram = $usdPerOunce / 31.1035; // Troy ounce to gram
                $inrPerGram = $usdPerGram * $usdToInr;
                
                $inrRates[$metal] = round($inrPerGram, 2);
            }
        }

        return $inrRates;
    }

    protected function getUSDToINRRate(): float
    {
        // This would typically fetch from a currency API
        // For now, using a reasonable exchange rate
        return 83.50; // USD to INR
    }

    protected function storeRates(array $rates): void
    {
        foreach ($rates as $metal => $rate) {
            MetalRate::create([
                'metal' => $metal,
                'rate' => $rate,
                'currency' => $this->supportedMetals[$metal]['currency'],
                'unit' => $this->supportedMetals[$metal]['unit'],
                'source' => $this->getActiveProvider(),
                'fetched_at' => now(),
            ]);
        }

        // Cache the rates for 5 minutes
        Cache::put('metal_rates_latest', $rates, 300);
    }

    protected function getCachedRates(array $metals): array
    {
        $cached = Cache::get('metal_rates_latest', []);
        
        if (empty($cached)) {
            // Get latest rates from database
            $latestRates = MetalRate::whereIn('metal', $metals)
                ->latest('fetched_at')
                ->get()
                ->groupBy('metal')
                ->map(fn($rates) => $rates->first()->rate)
                ->toArray();

            return [
                'success' => true,
                'rates' => $latestRates,
                'provider' => 'database_cache',
                'timestamp' => now(),
                'warning' => 'Using cached rates due to API failure',
            ];
        }

        return [
            'success' => true,
            'rates' => array_intersect_key($cached, array_flip($metals)),
            'provider' => 'memory_cache',
            'timestamp' => now(),
        ];
    }

    public function getHistoricalRates(string $metal, int $days = 30): array
    {
        $rates = MetalRate::where('metal', $metal)
            ->where('fetched_at', '>=', now()->subDays($days))
            ->orderBy('fetched_at')
            ->get();

        return [
            'metal' => $metal,
            'period_days' => $days,
            'data_points' => $rates->count(),
            'rates' => $rates->map(function ($rate) {
                return [
                    'date' => $rate->fetched_at->format('Y-m-d'),
                    'time' => $rate->fetched_at->format('H:i:s'),
                    'rate' => $rate->rate,
                    'currency' => $rate->currency,
                    'unit' => $rate->unit,
                ];
            })->toArray(),
            'statistics' => $this->calculateStatistics($rates),
        ];
    }

    protected function calculateStatistics($rates): array
    {
        if ($rates->isEmpty()) {
            return [];
        }

        $values = $rates->pluck('rate');
        
        return [
            'min' => $values->min(),
            'max' => $values->max(),
            'avg' => round($values->avg(), 2),
            'current' => $rates->last()->rate,
            'previous' => $rates->count() > 1 ? $rates->slice(-2, 1)->first()->rate : null,
            'change' => $rates->count() > 1 ? 
                round($rates->last()->rate - $rates->slice(-2, 1)->first()->rate, 2) : 0,
            'change_percent' => $rates->count() > 1 ? 
                round((($rates->last()->rate - $rates->slice(-2, 1)->first()->rate) / $rates->slice(-2, 1)->first()->rate) * 100, 2) : 0,
        ];
    }

    public function createAlert(array $alertData): MetalRateAlert
    {
        return MetalRateAlert::create([
            'user_id' => $alertData['user_id'] ?? null,
            'metal' => $alertData['metal'],
            'condition' => $alertData['condition'], // 'above', 'below', 'change_percent'
            'threshold' => $alertData['threshold'],
            'notification_channels' => $alertData['notification_channels'] ?? ['email'],
            'is_active' => true,
        ]);
    }

    public function checkAlerts(): array
    {
        $activeAlerts = MetalRateAlert::where('is_active', true)->get();
        $triggeredAlerts = [];

        foreach ($activeAlerts as $alert) {
            $currentRate = $this->getCurrentRate($alert->metal);
            
            if ($this->shouldTriggerAlert($alert, $currentRate)) {
                $this->triggerAlert($alert, $currentRate);
                $triggeredAlerts[] = $alert;
            }
        }

        return $triggeredAlerts;
    }

    protected function getCurrentRate(string $metal): ?float
    {
        $cached = Cache::get('metal_rates_latest', []);
        
        if (isset($cached[$metal])) {
            return $cached[$metal];
        }

        $latest = MetalRate::where('metal', $metal)
            ->latest('fetched_at')
            ->first();

        return $latest?->rate;
    }

    protected function shouldTriggerAlert(MetalRateAlert $alert, ?float $currentRate): bool
    {
        if (!$currentRate) {
            return false;
        }

        return match ($alert->condition) {
            'above' => $currentRate > $alert->threshold,
            'below' => $currentRate < $alert->threshold,
            'change_percent' => $this->checkPercentageChange($alert, $currentRate),
            default => false,
        };
    }

    protected function checkPercentageChange(MetalRateAlert $alert, float $currentRate): bool
    {
        $previousRate = MetalRate::where('metal', $alert->metal)
            ->where('fetched_at', '<', now()->subHour())
            ->latest('fetched_at')
            ->first()?->rate;

        if (!$previousRate) {
            return false;
        }

        $changePercent = abs(($currentRate - $previousRate) / $previousRate * 100);
        return $changePercent >= $alert->threshold;
    }

    protected function triggerAlert(MetalRateAlert $alert, float $currentRate): void
    {
        $channels = $alert->notification_channels;

        foreach ($channels as $channel) {
            match ($channel) {
                'email' => $this->sendEmailAlert($alert, $currentRate),
                'whatsapp' => $this->sendWhatsAppAlert($alert, $currentRate),
                'sms' => $this->sendSMSAlert($alert, $currentRate),
                default => null,
            };
        }

        // Update last triggered timestamp
        $alert->update(['last_triggered_at' => now()]);
    }

    protected function sendEmailAlert(MetalRateAlert $alert, float $currentRate): void
    {
        // This would integrate with the email service
        Log::info('Email alert triggered', [
            'alert_id' => $alert->id,
            'metal' => $alert->metal,
            'current_rate' => $currentRate,
            'threshold' => $alert->threshold,
        ]);
    }

    protected function sendWhatsAppAlert(MetalRateAlert $alert, float $currentRate): void
    {
        // This would integrate with the WhatsApp service
        Log::info('WhatsApp alert triggered', [
            'alert_id' => $alert->id,
            'metal' => $alert->metal,
            'current_rate' => $currentRate,
            'threshold' => $alert->threshold,
        ]);
    }

    protected function sendSMSAlert(MetalRateAlert $alert, float $currentRate): void
    {
        // This would integrate with an SMS service
        Log::info('SMS alert triggered', [
            'alert_id' => $alert->id,
            'metal' => $alert->metal,
            'current_rate' => $currentRate,
            'threshold' => $alert->threshold,
        ]);
    }

    public function subscribeToUpdates(array $subscriptionData): MetalRateSubscription
    {
        return MetalRateSubscription::create([
            'email' => $subscriptionData['email'],
            'phone' => $subscriptionData['phone'] ?? null,
            'metals' => $subscriptionData['metals'] ?? array_keys($this->supportedMetals),
            'frequency' => $subscriptionData['frequency'] ?? 'daily', // hourly, daily, weekly
            'notification_channels' => $subscriptionData['notification_channels'] ?? ['email'],
            'is_active' => true,
        ]);
    }

    public function broadcastRateUpdates(): array
    {
        $subscriptions = MetalRateSubscription::where('is_active', true)->get();
        $sent = 0;

        foreach ($subscriptions as $subscription) {
            if ($this->shouldSendUpdate($subscription)) {
                $this->sendRateUpdate($subscription);
                $sent++;
            }
        }

        return [
            'total_subscriptions' => $subscriptions->count(),
            'updates_sent' => $sent,
        ];
    }

    protected function shouldSendUpdate(MetalRateSubscription $subscription): bool
    {
        $lastSent = $subscription->last_sent_at;
        
        if (!$lastSent) {
            return true;
        }

        return match ($subscription->frequency) {
            'hourly' => $lastSent->lt(now()->subHour()),
            'daily' => $lastSent->lt(now()->subDay()),
            'weekly' => $lastSent->lt(now()->subWeek()),
            default => false,
        };
    }

    protected function sendRateUpdate(MetalRateSubscription $subscription): void
    {
        $rates = $this->fetchCurrentRates($subscription->metals);
        
        foreach ($subscription->notification_channels as $channel) {
            match ($channel) {
                'email' => $this->sendEmailUpdate($subscription, $rates),
                'whatsapp' => $this->sendWhatsAppUpdate($subscription, $rates),
                'sms' => $this->sendSMSUpdate($subscription, $rates),
                default => null,
            };
        }

        $subscription->update(['last_sent_at' => now()]);
    }

    protected function sendEmailUpdate(MetalRateSubscription $subscription, array $rates): void
    {
        // Email implementation would go here
        Log::info('Rate update email sent', [
            'subscription_id' => $subscription->id,
            'email' => $subscription->email,
            'metals' => $subscription->metals,
        ]);
    }

    protected function sendWhatsAppUpdate(MetalRateSubscription $subscription, array $rates): void
    {
        // WhatsApp implementation would go here
        Log::info('Rate update WhatsApp sent', [
            'subscription_id' => $subscription->id,
            'phone' => $subscription->phone,
            'metals' => $subscription->metals,
        ]);
    }

    protected function sendSMSUpdate(MetalRateSubscription $subscription, array $rates): void
    {
        // SMS implementation would go here
        Log::info('Rate update SMS sent', [
            'subscription_id' => $subscription->id,
            'phone' => $subscription->phone,
            'metals' => $subscription->metals,
        ]);
    }

    protected function getActiveProvider(): string
    {
        return BusinessSetting::getValue('metal_rates_provider', 'mock');
    }

    public function getSupportedMetals(): array
    {
        return $this->supportedMetals;
    }

    public function getProviders(): array
    {
        return $this->apiProviders;
    }

    public function formatRateForDisplay(string $metal, float $rate): string
    {
        $config = $this->supportedMetals[$metal];
        return "₹{$rate} per {$config['unit']}";
    }

    public function generateRateReport(array $metals, int $days = 7): array
    {
        $report = [
            'generated_at' => now(),
            'period_days' => $days,
            'metals' => [],
        ];

        foreach ($metals as $metal) {
            $historical = $this->getHistoricalRates($metal, $days);
            $report['metals'][$metal] = $historical;
        }

        return $report;
    }
}
