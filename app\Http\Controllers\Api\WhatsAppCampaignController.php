<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WhatsAppCampaign;
use App\Models\WhatsAppNumber;
use App\Services\WhatsAppMarketingService;
use App\Services\WhatsAppAutomationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class WhatsAppCampaignController extends Controller
{
    public function __construct(
        protected WhatsAppMarketingService $whatsappService,
        protected WhatsAppAutomationService $automationService
    ) {}

    public function index(Request $request): JsonResponse
    {
        $campaigns = WhatsAppCampaign::with(['creator'])
            ->when($request->status, fn($q) => $q->where('status', $request->status))
            ->when($request->search, fn($q) => $q->where('name', 'like', "%{$request->search}%"))
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $campaigns,
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'message' => 'required|string',
            'type' => 'required|in:text,template,media,interactive',
            'template_data' => 'nullable|array',
            'media_data' => 'nullable|array',
            'target_audience' => 'required|array',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        $campaign = $this->whatsappService->createCampaign($validated);

        if ($validated['scheduled_at'] ?? null) {
            $campaign->update([
                'status' => 'scheduled',
                'scheduled_at' => $validated['scheduled_at'],
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'WhatsApp campaign created successfully',
            'data' => $campaign,
        ], 201);
    }

    public function show(WhatsAppCampaign $campaign): JsonResponse
    {
        $campaign->load(['creator']);

        return response()->json([
            'success' => true,
            'data' => [
                'campaign' => $campaign,
                'analytics' => $this->whatsappService->getCampaignAnalytics($campaign),
            ],
        ]);
    }

    public function update(Request $request, WhatsAppCampaign $campaign): JsonResponse
    {
        if ($campaign->status === 'sent') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot update sent campaigns',
            ], 400);
        }

        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'message' => 'sometimes|string',
            'type' => 'sometimes|in:text,template,media,interactive',
            'template_data' => 'nullable|array',
            'media_data' => 'nullable|array',
            'target_audience' => 'sometimes|array',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        $campaign->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Campaign updated successfully',
            'data' => $campaign->fresh(),
        ]);
    }

    public function destroy(WhatsAppCampaign $campaign): JsonResponse
    {
        if ($campaign->status === 'sent') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete sent campaigns',
            ], 400);
        }

        $campaign->delete();

        return response()->json([
            'success' => true,
            'message' => 'Campaign deleted successfully',
        ]);
    }

    public function send(WhatsAppCampaign $campaign): JsonResponse
    {
        try {
            $results = $this->whatsappService->executeCampaign($campaign);

            return response()->json([
                'success' => true,
                'message' => 'Campaign sent successfully',
                'data' => $results,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send campaign: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function sendToLeads(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'lead_ids' => 'required|array|min:1',
            'lead_ids.*' => 'exists:leads,id',
            'message' => 'required|string',
            'type' => 'required|in:text,template,media,interactive',
            'template_data' => 'nullable|array',
            'media_data' => 'nullable|array',
        ]);

        try {
            $results = $this->whatsappService->sendToLeads(
                $validated['lead_ids'],
                [
                    'message' => $validated['message'],
                    'type' => $validated['type'],
                    'template_data' => $validated['template_data'] ?? null,
                    'media_data' => $validated['media_data'] ?? null,
                    'is_automated' => false,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Messages sent to leads successfully',
                'data' => $results,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send messages: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function sendBulk(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'recipients' => 'required|array|min:1',
            'recipients.*.phone' => 'required|string',
            'recipients.*.name' => 'nullable|string',
            'recipients.*.variables' => 'nullable|array',
            'message' => 'required|string',
            'type' => 'required|in:text,template,media,interactive',
            'template_data' => 'nullable|array',
            'media_data' => 'nullable|array',
        ]);

        try {
            $results = $this->whatsappService->sendBulkMessages(
                $validated['recipients'],
                [
                    'message' => $validated['message'],
                    'type' => $validated['type'],
                    'template_data' => $validated['template_data'] ?? null,
                    'media_data' => $validated['media_data'] ?? null,
                    'is_automated' => false,
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Bulk messages sent successfully',
                'data' => $results,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send bulk messages: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function analytics(WhatsAppCampaign $campaign): JsonResponse
    {
        $analytics = $this->whatsappService->getCampaignAnalytics($campaign);

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    public function getNumbers(): JsonResponse
    {
        $numbers = WhatsAppNumber::active()->get();

        return response()->json([
            'success' => true,
            'data' => $numbers,
        ]);
    }

    public function getTemplates(): JsonResponse
    {
        $templates = $this->whatsappService->getMessageTemplates();

        return response()->json([
            'success' => true,
            'data' => $templates,
        ]);
    }

    public function webhook(Request $request): JsonResponse
    {
        // Verify webhook (in production, verify the signature)
        $mode = $request->get('hub_mode');
        $token = $request->get('hub_verify_token');
        $challenge = $request->get('hub_challenge');

        if ($mode === 'subscribe' && $token === config('services.whatsapp.webhook_verify_token')) {
            return response($challenge, 200);
        }

        // Handle webhook data
        if ($request->isMethod('post')) {
            $this->whatsappService->handleWebhook($request->all());
        }

        return response()->json(['success' => true]);
    }
}
