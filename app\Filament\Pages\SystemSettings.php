<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Notifications\Notification;
use App\Models\Setting;
use Filament\Forms\Components\Tabs;

class SystemSettings extends Page implements HasForms
{
    use InteractsWithForms;

    protected static ?string $navigationIcon = 'heroicon-o-cog-8-tooth';

    protected static ?string $navigationLabel = 'System Settings';

    protected static ?string $title = 'System Configuration';

    protected static ?string $navigationGroup = 'Administration';

    protected static ?int $navigationSort = 98;

    protected static string $view = 'filament.pages.system-settings';

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill($this->getSettingsData());
    }

    protected function getSettingsData(): array
    {
        $settings = Setting::where('is_active', true)->get();
        $data = [];

        foreach ($settings as $setting) {
            $data[$setting->key] = $setting->value;
        }

        return $data;
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('System Configuration')
                    ->tabs([
                        // General Settings Tab
                        Tabs\Tab::make('General')
                            ->icon('heroicon-o-cog-6-tooth')
                            ->schema([
                                Forms\Components\Section::make('Application Settings')
                                    ->schema([
                                        Forms\Components\TextInput::make('app.name')
                                            ->label('Application Name')
                                            ->required()
                                            ->maxLength(255),

                                        Forms\Components\Textarea::make('app.description')
                                            ->label('Application Description')
                                            ->rows(3)
                                            ->maxLength(500),

                                        Forms\Components\Select::make('app.timezone')
                                            ->label('Default Timezone')
                                            ->options([
                                                'Asia/Kolkata' => 'India (IST)',
                                                'UTC' => 'UTC',
                                                'America/New_York' => 'Eastern Time',
                                                'Europe/London' => 'London Time',
                                                'Asia/Tokyo' => 'Japan Time',
                                                'Australia/Sydney' => 'Sydney Time',
                                            ])
                                            ->searchable(),

                                        Forms\Components\Select::make('app.locale')
                                            ->label('Default Language')
                                            ->options([
                                                'en' => 'English',
                                                'hi' => 'Hindi',
                                                'ta' => 'Tamil',
                                                'te' => 'Telugu',
                                                'kn' => 'Kannada',
                                                'ml' => 'Malayalam',
                                            ])
                                            ->searchable(),
                                    ])->columns(2),
                            ]),

                        // Email Configuration Tab
                        Tabs\Tab::make('Email')
                            ->icon('heroicon-o-envelope')
                            ->schema([
                                Forms\Components\Section::make('Email Provider Settings')
                                    ->schema([
                                        Forms\Components\Select::make('email.default_provider')
                                            ->label('Default Email Provider')
                                            ->options([
                                                'smtp' => 'SMTP',
                                                'ses' => 'Amazon SES',
                                                'mailgun' => 'Mailgun',
                                                'postmark' => 'Postmark',
                                                'sendgrid' => 'SendGrid',
                                            ])
                                            ->required(),

                                        Forms\Components\TextInput::make('email.daily_send_limit')
                                            ->label('Daily Send Limit')
                                            ->numeric()
                                            ->required()
                                            ->minValue(1)
                                            ->maxValue(10000)
                                            ->helperText('Maximum emails per day per account'),

                                        Forms\Components\TextInput::make('email.batch_size')
                                            ->label('Batch Size')
                                            ->numeric()
                                            ->required()
                                            ->minValue(1)
                                            ->maxValue(1000)
                                            ->helperText('Emails processed per batch'),

                                        Forms\Components\TextInput::make('email.retry_attempts')
                                            ->label('Retry Attempts')
                                            ->numeric()
                                            ->required()
                                            ->minValue(1)
                                            ->maxValue(10)
                                            ->helperText('Retry attempts for failed emails'),

                                        Forms\Components\TextInput::make('email.bounce_threshold')
                                            ->label('Bounce Threshold (%)')
                                            ->numeric()
                                            ->required()
                                            ->minValue(1)
                                            ->maxValue(50)
                                            ->helperText('Bounce rate threshold for account suspension'),
                                    ])->columns(2),

                                Forms\Components\Section::make('SMTP Configuration')
                                    ->schema([
                                        Forms\Components\TextInput::make('smtp.host')
                                            ->label('SMTP Host')
                                            ->required()
                                            ->maxLength(255),

                                        Forms\Components\TextInput::make('smtp.port')
                                            ->label('SMTP Port')
                                            ->numeric()
                                            ->required()
                                            ->minValue(1)
                                            ->maxValue(65535),

                                        Forms\Components\Select::make('smtp.encryption')
                                            ->label('Encryption')
                                            ->options([
                                                'tls' => 'TLS',
                                                'ssl' => 'SSL',
                                                'none' => 'None',
                                            ])
                                            ->required(),

                                        Forms\Components\TextInput::make('smtp.username')
                                            ->label('SMTP Username')
                                            ->email()
                                            ->maxLength(255),

                                        Forms\Components\TextInput::make('smtp.password')
                                            ->label('SMTP Password')
                                            ->password()
                                            ->maxLength(255)
                                            ->helperText('Use app password for Gmail'),
                                    ])->columns(2),
                            ]),

                        // Performance Settings Tab
                        Tabs\Tab::make('Performance')
                            ->icon('heroicon-o-bolt')
                            ->schema([
                                Forms\Components\Section::make('Import & Processing')
                                    ->schema([
                                        Forms\Components\TextInput::make('performance.import_chunk_size')
                                            ->label('Import Chunk Size')
                                            ->numeric()
                                            ->required()
                                            ->minValue(100)
                                            ->maxValue(10000)
                                            ->helperText('Contacts imported per chunk'),

                                        Forms\Components\TextInput::make('performance.max_memory_usage')
                                            ->label('Max Memory Usage (MB)')
                                            ->numeric()
                                            ->required()
                                            ->minValue(128)
                                            ->maxValue(2048)
                                            ->helperText('Maximum memory for bulk operations'),

                                        Forms\Components\Toggle::make('performance.enable_query_optimization')
                                            ->label('Enable Query Optimization')
                                            ->helperText('Automatic query optimization for large datasets'),
                                    ])->columns(2),

                                Forms\Components\Section::make('Caching')
                                    ->schema([
                                        Forms\Components\TextInput::make('performance.query_cache_ttl')
                                            ->label('Query Cache TTL (seconds)')
                                            ->numeric()
                                            ->required()
                                            ->minValue(60)
                                            ->maxValue(86400)
                                            ->helperText('Cache duration for database queries'),
                                    ])->columns(1),
                            ]),

                        // Security Settings Tab
                        Tabs\Tab::make('Security')
                            ->icon('heroicon-o-shield-check')
                            ->schema([
                                Forms\Components\Section::make('Authentication & Access')
                                    ->schema([
                                        Forms\Components\Toggle::make('security.enable_2fa')
                                            ->label('Enable Two-Factor Authentication')
                                            ->helperText('Require 2FA for admin users'),

                                        Forms\Components\TextInput::make('security.session_timeout')
                                            ->label('Session Timeout (seconds)')
                                            ->numeric()
                                            ->required()
                                            ->minValue(300)
                                            ->maxValue(86400)
                                            ->helperText('User session timeout duration'),

                                        Forms\Components\TextInput::make('security.api_rate_limit')
                                            ->label('API Rate Limit (per hour)')
                                            ->numeric()
                                            ->required()
                                            ->minValue(100)
                                            ->maxValue(10000)
                                            ->helperText('Maximum API requests per hour'),

                                        Forms\Components\Toggle::make('security.encrypt_sensitive_data')
                                            ->label('Encrypt Sensitive Data')
                                            ->helperText('Encrypt sensitive data in database'),
                                    ])->columns(2),
                            ]),

                        // Geographic Settings Tab
                        Tabs\Tab::make('Geographic')
                            ->icon('heroicon-o-globe-alt')
                            ->schema([
                                Forms\Components\Section::make('Geographic Configuration')
                                    ->schema([
                                        Forms\Components\Select::make('geographic.default_country')
                                            ->label('Default Country')
                                            ->options([
                                                'India' => 'India',
                                                'United States' => 'United States',
                                                'United Kingdom' => 'United Kingdom',
                                                'Canada' => 'Canada',
                                                'Australia' => 'Australia',
                                            ])
                                            ->searchable()
                                            ->required(),

                                        Forms\Components\Toggle::make('geographic.enable_state_filtering')
                                            ->label('Enable State Filtering')
                                            ->helperText('Allow filtering contacts by state'),

                                        Forms\Components\Toggle::make('geographic.enable_city_filtering')
                                            ->label('Enable City Filtering')
                                            ->helperText('Allow filtering contacts by city'),

                                        Forms\Components\TagsInput::make('geographic.supported_states')
                                            ->label('Supported States')
                                            ->placeholder('Add states')
                                            ->helperText('List of states available for filtering'),
                                    ])->columns(2),
                            ]),

                        // WhatsApp Integration Tab
                        Tabs\Tab::make('WhatsApp')
                            ->icon('heroicon-o-chat-bubble-left-right')
                            ->schema([
                                Forms\Components\Section::make('WhatsApp Configuration')
                                    ->schema([
                                        Forms\Components\Toggle::make('whatsapp.enable_integration')
                                            ->label('Enable WhatsApp Integration')
                                            ->helperText('Enable WhatsApp messaging features'),

                                        Forms\Components\TextInput::make('whatsapp.api_endpoint')
                                            ->label('API Endpoint')
                                            ->url()
                                            ->maxLength(255)
                                            ->helperText('WhatsApp API endpoint URL'),

                                        Forms\Components\TextInput::make('whatsapp.api_token')
                                            ->label('API Token')
                                            ->password()
                                            ->maxLength(255)
                                            ->helperText('WhatsApp API authentication token'),

                                        Forms\Components\TextInput::make('whatsapp.daily_message_limit')
                                            ->label('Daily Message Limit')
                                            ->numeric()
                                            ->required()
                                            ->minValue(1)
                                            ->maxValue(10000)
                                            ->helperText('Maximum WhatsApp messages per day'),
                                    ])->columns(2),
                            ]),

                        // API Configuration Tab
                        Tabs\Tab::make('API')
                            ->icon('heroicon-o-code-bracket')
                            ->schema([
                                Forms\Components\Section::make('API Settings')
                                    ->schema([
                                        Forms\Components\Toggle::make('api.enable_public_api')
                                            ->label('Enable Public API')
                                            ->helperText('Allow external API access'),

                                        Forms\Components\Toggle::make('api.require_authentication')
                                            ->label('Require Authentication')
                                            ->helperText('Require API key for access'),

                                        Forms\Components\Select::make('api.default_response_format')
                                            ->label('Default Response Format')
                                            ->options([
                                                'json' => 'JSON',
                                                'xml' => 'XML',
                                            ])
                                            ->required(),
                                    ])->columns(2),
                            ]),

                        // Backup & Monitoring Tab
                        Tabs\Tab::make('Backup & Monitoring')
                            ->icon('heroicon-o-server')
                            ->schema([
                                Forms\Components\Section::make('Backup Configuration')
                                    ->schema([
                                        Forms\Components\Toggle::make('backup.enable_auto_backup')
                                            ->label('Enable Auto Backup')
                                            ->helperText('Automatically backup database'),

                                        Forms\Components\Select::make('backup.backup_frequency')
                                            ->label('Backup Frequency')
                                            ->options([
                                                'hourly' => 'Hourly',
                                                'daily' => 'Daily',
                                                'weekly' => 'Weekly',
                                                'monthly' => 'Monthly',
                                            ])
                                            ->required(),

                                        Forms\Components\TextInput::make('backup.retention_days')
                                            ->label('Retention Days')
                                            ->numeric()
                                            ->required()
                                            ->minValue(1)
                                            ->maxValue(365)
                                            ->helperText('Days to keep backups'),
                                    ])->columns(2),

                                Forms\Components\Section::make('Monitoring Settings')
                                    ->schema([
                                        Forms\Components\Toggle::make('monitoring.enable_performance_monitoring')
                                            ->label('Enable Performance Monitoring')
                                            ->helperText('Monitor system performance'),

                                        Forms\Components\Toggle::make('monitoring.log_slow_queries')
                                            ->label('Log Slow Queries')
                                            ->helperText('Log slow database queries'),

                                        Forms\Components\TextInput::make('monitoring.slow_query_threshold')
                                            ->label('Slow Query Threshold (ms)')
                                            ->numeric()
                                            ->required()
                                            ->minValue(100)
                                            ->maxValue(10000)
                                            ->helperText('Threshold for slow query logging'),
                                    ])->columns(2),
                            ]),
                    ])->columnSpanFull(),
            ])
            ->statePath('data');
    }

    public function save(): void
    {
        $data = $this->form->getState();

        foreach ($data as $key => $value) {
            Setting::updateOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'last_modified_at' => now(),
                    'modified_by' => auth()->id(),
                ]
            );
        }

        // Clear settings cache
        Setting::clearCache();

        Notification::make()
            ->title('Settings Updated')
            ->body('System settings have been successfully updated.')
            ->success()
            ->send();
    }

    protected function getFormActions(): array
    {
        return [
            Forms\Components\Actions\Action::make('save')
                ->label('Save Settings')
                ->action('save')
                ->color('primary')
                ->icon('heroicon-o-check'),

            Forms\Components\Actions\Action::make('reset')
                ->label('Reset to Defaults')
                ->action('resetToDefaults')
                ->color('danger')
                ->icon('heroicon-o-arrow-path')
                ->requiresConfirmation()
                ->modalHeading('Reset Settings')
                ->modalDescription('Are you sure you want to reset all settings to their default values?'),
        ];
    }

    public function resetToDefaults(): void
    {
        // Run the settings seeder to reset to defaults
        \Artisan::call('db:seed', ['--class' => 'SettingsSeeder']);

        // Clear cache
        Setting::clearCache();

        // Refresh form data
        $this->form->fill($this->getSettingsData());

        Notification::make()
            ->title('Settings Reset')
            ->body('All settings have been reset to their default values.')
            ->warning()
            ->send();
    }

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->check();
    }

    public static function canAccess(): bool
    {
        return auth()->check();
    }
}
