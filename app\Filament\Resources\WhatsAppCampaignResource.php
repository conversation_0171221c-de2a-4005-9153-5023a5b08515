<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WhatsAppCampaignResource\Pages;
use App\Models\WhatsAppCampaign;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;

class WhatsAppCampaignResource extends Resource
{
    protected static ?string $model = WhatsAppCampaign::class;
    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';
    protected static ?string $navigationGroup = 'WhatsApp Marketing';
    protected static ?string $navigationLabel = 'Campaigns';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Campaign Details')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Select::make('type')
                            ->options([
                                'broadcast' => 'Broadcast Message',
                                'promotional' => 'Promotional',
                                'transactional' => 'Transactional',
                                'automated' => 'Automated Response',
                            ])
                            ->required(),
                        Forms\Components\Select::make('status')
                            ->options([
                                'draft' => 'Draft',
                                'scheduled' => 'Scheduled',
                                'sending' => 'Sending',
                                'sent' => 'Sent',
                                'paused' => 'Paused',
                                'cancelled' => 'Cancelled',
                            ])
                            ->required(),
                    ])->columns(3),

                Forms\Components\Section::make('Message Content')
                    ->schema([
                        Forms\Components\Textarea::make('message')
                            ->required()
                            ->rows(5)
                            ->maxLength(4096)
                            ->helperText('Maximum 4096 characters for WhatsApp messages')
                            ->columnSpanFull(),
                        Forms\Components\FileUpload::make('media_url')
                            ->label('Media Attachment')
                            ->acceptedFileTypes(['image/*', 'video/*', 'audio/*', 'application/pdf'])
                            ->maxSize(16384) // 16MB
                            ->directory('whatsapp-media')
                            ->columnSpanFull(),
                        Forms\Components\Select::make('media_type')
                            ->options([
                                'image' => 'Image',
                                'video' => 'Video',
                                'audio' => 'Audio',
                                'document' => 'Document',
                            ])
                            ->visible(fn ($get) => $get('media_url')),
                    ]),

                Forms\Components\Section::make('Targeting & Recipients')
                    ->schema([
                        Forms\Components\TagsInput::make('phone_numbers')
                            ->label('Phone Numbers')
                            ->placeholder('Add phone numbers (with country code)')
                            ->helperText('Enter phone numbers with country code (e.g., +91XXXXXXXXXX)'),
                        Forms\Components\TextInput::make('estimated_recipients')
                            ->label('Estimated Recipients')
                            ->numeric()
                            ->disabled()
                            ->helperText('Automatically calculated based on phone numbers'),
                        Forms\Components\Textarea::make('recipient_notes')
                            ->label('Recipient Notes')
                            ->placeholder('Add notes about the target audience')
                            ->rows(2),
                    ])->columns(2),

                Forms\Components\Section::make('Scheduling & Delivery')
                    ->schema([
                        Forms\Components\DateTimePicker::make('scheduled_at')
                            ->label('Schedule Send Time'),
                        Forms\Components\Select::make('timezone')
                            ->options([
                                'Asia/Kolkata' => 'India (IST)',
                                'UTC' => 'UTC',
                                'America/New_York' => 'Eastern Time',
                                'Europe/London' => 'London Time',
                            ])
                            ->default('Asia/Kolkata'),
                        Forms\Components\Select::make('sender_number')
                            ->label('Sender WhatsApp Number')
                            ->options([
                                '+************' => '+91 98765 43210 (Primary)',
                                '+************' => '+91 98765 43211 (Secondary)',
                                '+************' => '+91 98765 43212 (Support)',
                            ])
                            ->required(),
                    ])->columns(3),

                Forms\Components\Section::make('Advanced Options')
                    ->schema([
                        Forms\Components\Toggle::make('track_delivery')
                            ->label('Track Delivery Status')
                            ->default(true),
                        Forms\Components\Toggle::make('track_reads')
                            ->label('Track Read Receipts')
                            ->default(true),
                        Forms\Components\TextInput::make('delivery_rate_limit')
                            ->label('Messages per Minute')
                            ->numeric()
                            ->default(60)
                            ->minValue(1)
                            ->maxValue(1000),
                        Forms\Components\Textarea::make('notes')
                            ->label('Campaign Notes')
                            ->rows(3),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('type')
                    ->colors([
                        'primary' => 'broadcast',
                        'success' => 'promotional',
                        'warning' => 'transactional',
                        'info' => 'automated',
                    ]),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'draft',
                        'warning' => 'scheduled',
                        'info' => 'sending',
                        'success' => 'sent',
                        'danger' => 'paused',
                        'gray' => 'cancelled',
                    ]),
                Tables\Columns\TextColumn::make('recipients_count')
                    ->label('Recipients')
                    ->numeric(),
                Tables\Columns\TextColumn::make('delivered_count')
                    ->label('Delivered')
                    ->numeric(),
                Tables\Columns\TextColumn::make('read_count')
                    ->label('Read')
                    ->numeric(),
                Tables\Columns\TextColumn::make('delivery_rate')
                    ->label('Delivery Rate')
                    ->formatStateUsing(fn ($state) => $state ? number_format($state, 2) . '%' : 'N/A'),
                Tables\Columns\TextColumn::make('sender_number')
                    ->label('Sender')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('scheduled_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'broadcast' => 'Broadcast',
                        'promotional' => 'Promotional',
                        'transactional' => 'Transactional',
                        'automated' => 'Automated',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'scheduled' => 'Scheduled',
                        'sending' => 'Sending',
                        'sent' => 'Sent',
                        'paused' => 'Paused',
                        'cancelled' => 'Cancelled',
                    ]),
                Tables\Filters\SelectFilter::make('sender_number')
                    ->options([
                        '+************' => '+91 98765 43210',
                        '+************' => '+91 98765 43211',
                        '+************' => '+91 98765 43212',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->action(function (WhatsAppCampaign $record) {
                        $newCampaign = $record->replicate();
                        $newCampaign->name = $record->name . ' (Copy)';
                        $newCampaign->status = 'draft';
                        $newCampaign->scheduled_at = null;
                        $newCampaign->save();
                        
                        return redirect()->route('filament.admin.resources.whats-app-campaigns.edit', $newCampaign);
                    }),
                Tables\Actions\Action::make('send_test')
                    ->icon('heroicon-o-paper-airplane')
                    ->form([
                        Forms\Components\TextInput::make('test_phone')
                            ->tel()
                            ->required()
                            ->label('Test Phone Number')
                            ->placeholder('+************'),
                    ])
                    ->action(function (WhatsAppCampaign $record, array $data) {
                        // Send test WhatsApp message logic here
                        \Filament\Notifications\Notification::make()
                            ->title('Test message sent successfully')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('pause')
                        ->label('Pause Campaigns')
                        ->icon('heroicon-o-pause')
                        ->action(function ($records) {
                            $records->each(function ($record) {
                                $record->update(['status' => 'paused']);
                            });
                        })
                        ->requiresConfirmation(),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Campaign Overview')
                    ->schema([
                        Infolists\Components\TextEntry::make('name'),
                        Infolists\Components\TextEntry::make('type')
                            ->badge(),
                        Infolists\Components\TextEntry::make('status')
                            ->badge(),
                        Infolists\Components\TextEntry::make('sender_number'),
                        Infolists\Components\TextEntry::make('scheduled_at')
                            ->dateTime(),
                    ])->columns(3),

                Infolists\Components\Section::make('Performance Metrics')
                    ->schema([
                        Infolists\Components\TextEntry::make('recipients_count')
                            ->label('Total Recipients'),
                        Infolists\Components\TextEntry::make('delivered_count')
                            ->label('Delivered'),
                        Infolists\Components\TextEntry::make('read_count')
                            ->label('Read'),
                        Infolists\Components\TextEntry::make('failed_count')
                            ->label('Failed'),
                        Infolists\Components\TextEntry::make('delivery_rate')
                            ->label('Delivery Rate')
                            ->formatStateUsing(fn ($state) => $state ? number_format($state, 2) . '%' : 'N/A'),
                        Infolists\Components\TextEntry::make('read_rate')
                            ->label('Read Rate')
                            ->formatStateUsing(fn ($state) => $state ? number_format($state, 2) . '%' : 'N/A'),
                    ])->columns(3),

                Infolists\Components\Section::make('Message Content')
                    ->schema([
                        Infolists\Components\TextEntry::make('message')
                            ->columnSpanFull(),
                        Infolists\Components\ImageEntry::make('media_url')
                            ->label('Media Attachment')
                            ->visible(fn ($record) => $record->media_url && $record->media_type === 'image'),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWhatsAppCampaigns::route('/'),
            'create' => Pages\CreateWhatsAppCampaign::route('/create'),
            'view' => Pages\ViewWhatsAppCampaign::route('/{record}'),
            'edit' => Pages\EditWhatsAppCampaign::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', 'sending')->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }
}
