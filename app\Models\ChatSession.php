<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class ChatSession extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'visitor_id',
        'user_id',
        'agent_id',
        'status',
        'visitor_name',
        'visitor_email',
        'visitor_phone',
        'initial_message',
        'source_page',
        'visitor_info',
        'started_at',
        'last_activity_at',
        'assigned_at',
        'closed_at',
        'closing_reason',
        'message_count',
        'agent_response_time',
        'is_resolved',
        'satisfaction_rating',
        'feedback',
    ];

    protected $casts = [
        'visitor_info' => 'array',
        'started_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'assigned_at' => 'datetime',
        'closed_at' => 'datetime',
        'is_resolved' => 'boolean',
        'satisfaction_rating' => 'decimal:1',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function agent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'agent_id');
    }

    public function messages(): HasMany
    {
        return $this->hasMany(ChatMessage::class);
    }

    public function latestMessage(): HasMany
    {
        return $this->hasMany(ChatMessage::class)->latest();
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeWaiting($query)
    {
        return $query->where('status', 'waiting');
    }

    public function scopeAssigned($query)
    {
        return $query->where('status', 'assigned');
    }

    public function scopeClosed($query)
    {
        return $query->where('status', 'closed');
    }

    public function scopeForAgent($query, $agentId)
    {
        return $query->where('agent_id', $agentId);
    }

    public function scopeUnassigned($query)
    {
        return $query->whereNull('agent_id')->where('status', '!=', 'closed');
    }

    public function scopeRecentActivity($query, $minutes = 30)
    {
        return $query->where('last_activity_at', '>=', now()->subMinutes($minutes));
    }

    // Boot method to generate session_id
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($session) {
            if (empty($session->session_id)) {
                $session->session_id = 'chat_' . Str::random(16) . '_' . time();
            }
        });
    }

    // Business Logic Methods
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    public function isWaiting(): bool
    {
        return $this->status === 'waiting';
    }

    public function isAssigned(): bool
    {
        return $this->status === 'assigned';
    }

    public function isClosed(): bool
    {
        return $this->status === 'closed';
    }

    public function assignToAgent(User $agent): void
    {
        $this->update([
            'agent_id' => $agent->id,
            'status' => 'assigned',
            'assigned_at' => now(),
        ]);
    }

    public function closeSession(string $reason = null): void
    {
        $this->update([
            'status' => 'closed',
            'closed_at' => now(),
            'closing_reason' => $reason,
        ]);
    }

    public function updateActivity(): void
    {
        $this->update(['last_activity_at' => now()]);
    }

    public function incrementMessageCount(): void
    {
        $this->increment('message_count');
        $this->updateActivity();
    }

    public function getResponseTimeAttribute(): ?string
    {
        if (!$this->agent_response_time) {
            return null;
        }

        $minutes = floor($this->agent_response_time / 60);
        $seconds = $this->agent_response_time % 60;

        if ($minutes > 0) {
            return "{$minutes}m {$seconds}s";
        }

        return "{$seconds}s";
    }

    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'active' => 'green',
            'waiting' => 'yellow',
            'assigned' => 'blue',
            'closed' => 'gray',
            'abandoned' => 'red',
            default => 'gray',
        };
    }
}
