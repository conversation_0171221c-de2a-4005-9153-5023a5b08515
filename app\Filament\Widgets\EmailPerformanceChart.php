<?php

namespace App\Filament\Widgets;

use App\Models\EmailAccount;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class EmailPerformanceChart extends ChartWidget
{
    protected static ?string $heading = 'Email Account Performance & Daily Limits';
    
    protected static ?int $sort = 3;
    
    protected function getData(): array
    {
        $emailAccounts = EmailAccount::where('is_active', true)
            ->select('name', 'daily_send_limit', 'emails_sent_today')
            ->get();
        
        $labels = [];
        $limitData = [];
        $sentData = [];
        $utilizationData = [];
        
        foreach ($emailAccounts as $account) {
            $labels[] = $account->name;
            $limitData[] = $account->daily_send_limit;
            $sentData[] = $account->emails_sent_today;
            
            $utilization = $account->daily_send_limit > 0 
                ? ($account->emails_sent_today / $account->daily_send_limit) * 100 
                : 0;
            $utilizationData[] = round($utilization, 1);
        }
        
        return [
            'datasets' => [
                [
                    'label' => 'Daily Limit',
                    'data' => $limitData,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 2,
                    'type' => 'bar',
                ],
                [
                    'label' => 'Emails Sent Today',
                    'data' => $sentData,
                    'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                    'borderColor' => 'rgba(255, 99, 132, 1)',
                    'borderWidth' => 2,
                    'type' => 'bar',
                ],
                [
                    'label' => 'Utilization %',
                    'data' => $utilizationData,
                    'backgroundColor' => 'rgba(75, 192, 192, 0.8)',
                    'borderColor' => 'rgba(75, 192, 192, 1)',
                    'borderWidth' => 2,
                    'type' => 'line',
                    'yAxisID' => 'y1',
                ],
            ],
            'labels' => $labels,
        ];
    }
    
    protected function getType(): string
    {
        return 'bar';
    }
    
    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'interaction' => [
                'mode' => 'index',
                'intersect' => false,
            ],
            'scales' => [
                'y' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'left',
                    'title' => [
                        'display' => true,
                        'text' => 'Number of Emails'
                    ]
                ],
                'y1' => [
                    'type' => 'linear',
                    'display' => true,
                    'position' => 'right',
                    'title' => [
                        'display' => true,
                        'text' => 'Utilization %'
                    ],
                    'grid' => [
                        'drawOnChartArea' => false,
                    ],
                    'max' => 100,
                ],
            ],
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'callbacks' => [
                        'label' => 'function(context) {
                            if (context.datasetIndex === 2) {
                                return context.dataset.label + ": " + context.parsed.y + "%";
                            }
                            return context.dataset.label + ": " + context.parsed.y.toLocaleString() + " emails";
                        }'
                    ]
                ]
            ],
        ];
    }
}
