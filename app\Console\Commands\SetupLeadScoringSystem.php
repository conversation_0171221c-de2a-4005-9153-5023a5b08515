<?php

namespace App\Console\Commands;

use App\Models\BusinessSetting;
use App\Models\Lead;
use App\Services\AutomatedLeadScoringService;
use App\Jobs\ProcessLeadScoringJob;
use Illuminate\Console\Command;

class SetupLeadScoringSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bhavitech:setup-lead-scoring {--force : Force recreation of settings} {--score-existing : Score existing leads}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup automated lead scoring system for Bhavitech';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up automated lead scoring system for Bhavitech...');

        if ($this->option('force')) {
            $this->warn('Force mode: Resetting lead scoring settings...');
        }

        $this->setupLeadScoringSettings();

        if ($this->option('score-existing')) {
            $this->scoreExistingLeads();
        }

        $this->info('✅ Automated lead scoring system setup completed!');

        return Command::SUCCESS;
    }

    protected function setupLeadScoringSettings(): void
    {
        $this->info('Setting up lead scoring settings...');

        $settings = [
            [
                'key' => 'lead_scoring_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'lead_scoring',
                'label' => 'Enable Lead Scoring',
                'description' => 'Enable automated lead scoring system',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'lead_scoring_auto_update',
                'value' => true,
                'type' => 'boolean',
                'group' => 'lead_scoring',
                'label' => 'Auto Update Scores',
                'description' => 'Automatically update lead scores when new interactions occur',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'lead_scoring_frequency',
                'value' => 'daily',
                'type' => 'string',
                'group' => 'lead_scoring',
                'label' => 'Scoring Frequency',
                'description' => 'How often to recalculate lead scores',
                'options' => ['hourly', 'daily', 'weekly'],
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'lead_scoring_min_threshold',
                'value' => 20,
                'type' => 'integer',
                'group' => 'lead_scoring',
                'label' => 'Minimum Score Threshold',
                'description' => 'Minimum score for qualified leads',
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'lead_scoring_high_priority_threshold',
                'value' => 70,
                'type' => 'integer',
                'group' => 'lead_scoring',
                'label' => 'High Priority Threshold',
                'description' => 'Score threshold for high priority leads',
                'is_public' => false,
                'sort_order' => 5,
            ],
            [
                'key' => 'lead_scoring_decay_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'lead_scoring',
                'label' => 'Enable Score Decay',
                'description' => 'Reduce scores over time for inactive leads',
                'is_public' => false,
                'sort_order' => 6,
            ],
            [
                'key' => 'lead_scoring_decay_rate',
                'value' => 5,
                'type' => 'integer',
                'group' => 'lead_scoring',
                'label' => 'Score Decay Rate (%)',
                'description' => 'Percentage to reduce score per week of inactivity',
                'is_public' => false,
                'sort_order' => 7,
            ],
            [
                'key' => 'lead_scoring_notifications_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'lead_scoring',
                'label' => 'Enable Scoring Notifications',
                'description' => 'Send notifications when lead scores change significantly',
                'is_public' => false,
                'sort_order' => 8,
            ],
            [
                'key' => 'lead_scoring_notification_threshold',
                'value' => 20,
                'type' => 'integer',
                'group' => 'lead_scoring',
                'label' => 'Notification Threshold',
                'description' => 'Score change threshold to trigger notifications',
                'is_public' => false,
                'sort_order' => 9,
            ],
            [
                'key' => 'lead_scoring_intelligence_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'lead_scoring',
                'label' => 'Enable Lead Intelligence',
                'description' => 'Enable advanced lead insights and recommendations',
                'is_public' => false,
                'sort_order' => 10,
            ],
        ];

        $created = 0;
        foreach ($settings as $settingData) {
            $existing = BusinessSetting::where('key', $settingData['key'])->first();

            if (!$existing || $this->option('force')) {
                if ($existing) {
                    $existing->delete();
                }

                BusinessSetting::create($settingData);
                $created++;
                $this->line("  ✓ Created setting: {$settingData['key']}");
            } else {
                $this->line("  - Setting already exists: {$settingData['key']}");
            }
        }

        $this->info("Created {$created} lead scoring settings.");
    }

    protected function scoreExistingLeads(): void
    {
        $this->info('Scoring existing leads...');

        $leads = Lead::whereNull('score')
            ->orWhere('score', 0)
            ->get();

        if ($leads->isEmpty()) {
            $this->info('No leads found that need scoring.');
            return;
        }

        $this->info("Found {$leads->count()} leads to score.");

        $scoringService = app(AutomatedLeadScoringService::class);
        $scored = 0;
        $errors = 0;

        $progressBar = $this->output->createProgressBar($leads->count());
        $progressBar->start();

        foreach ($leads as $lead) {
            try {
                $scoringService->updateLeadScore($lead);
                $scored++;
            } catch (\Exception $e) {
                $errors++;
                $this->error("Failed to score lead {$lead->id}: " . $e->getMessage());
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        $this->info("Scoring completed: {$scored} leads scored, {$errors} errors.");

        // Queue batch scoring job for future updates
        ProcessLeadScoringJob::dispatch();
        $this->info('Queued batch scoring job for ongoing updates.');
    }
}
