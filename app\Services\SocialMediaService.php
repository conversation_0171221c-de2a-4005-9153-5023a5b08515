<?php

namespace App\Services;

use App\Models\SocialMediaAccount;
use App\Models\SocialMediaPost;
use App\Models\BusinessSetting;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class SocialMediaService
{
    protected array $platformConfigs = [
        'facebook' => [
            'api_version' => 'v18.0',
            'base_url' => 'https://graph.facebook.com',
            'scopes' => ['pages_manage_posts', 'pages_read_engagement', 'pages_show_list'],
        ],
        'instagram' => [
            'api_version' => 'v18.0',
            'base_url' => 'https://graph.facebook.com',
            'scopes' => ['instagram_basic', 'instagram_content_publish'],
        ],
        'linkedin' => [
            'api_version' => 'v2',
            'base_url' => 'https://api.linkedin.com',
            'scopes' => ['w_member_social', 'r_liteprofile'],
        ],
        'twitter' => [
            'api_version' => '2',
            'base_url' => 'https://api.twitter.com',
            'scopes' => ['tweet.read', 'tweet.write', 'users.read'],
        ],
    ];

    public function createPost(array $data): SocialMediaPost
    {
        $post = SocialMediaPost::create([
            'title' => $data['title'] ?? null,
            'content' => $data['content'],
            'media_urls' => $data['media_urls'] ?? [],
            'hashtags' => $data['hashtags'] ?? [],
            'status' => $data['status'] ?? 'draft',
            'scheduled_at' => $data['scheduled_at'] ?? null,
            'post_type' => $data['post_type'] ?? 'text',
            'cross_post_platforms' => $data['platforms'] ?? [],
            'targeting' => $data['targeting'] ?? [],
            'boost_post' => $data['boost_post'] ?? false,
            'boost_budget' => $data['boost_budget'] ?? null,
            'campaign_id' => $data['campaign_id'] ?? null,
            'created_by' => auth()->id(),
        ]);

        return $post;
    }

    public function schedulePost(SocialMediaPost $post, Carbon $scheduledAt): bool
    {
        $post->update([
            'status' => 'scheduled',
            'scheduled_at' => $scheduledAt,
        ]);

        return true;
    }

    public function publishPost(SocialMediaPost $post): array
    {
        $results = [
            'success' => [],
            'failed' => [],
        ];

        $platforms = $post->cross_post_platforms;
        
        foreach ($platforms as $platform) {
            try {
                $account = $this->getActiveAccount($platform);
                
                if (!$account) {
                    $results['failed'][$platform] = 'No active account found';
                    continue;
                }

                $platformPostId = $this->publishToPlatform($post, $account);
                
                if ($platformPostId) {
                    $results['success'][$platform] = $platformPostId;
                } else {
                    $results['failed'][$platform] = 'Failed to publish';
                }

            } catch (\Exception $e) {
                $results['failed'][$platform] = $e->getMessage();
                Log::error("Failed to publish to {$platform}", [
                    'post_id' => $post->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Update post status and platform posts
        $post->markAsPublished($results['success']);

        return $results;
    }

    protected function publishToPlatform(SocialMediaPost $post, SocialMediaAccount $account): ?string
    {
        return match ($account->platform) {
            'facebook' => $this->publishToFacebook($post, $account),
            'instagram' => $this->publishToInstagram($post, $account),
            'linkedin' => $this->publishToLinkedIn($post, $account),
            'twitter' => $this->publishToTwitter($post, $account),
            default => null,
        };
    }

    protected function publishToFacebook(SocialMediaPost $post, SocialMediaAccount $account): ?string
    {
        $config = $this->platformConfigs['facebook'];
        $url = "{$config['base_url']}/{$config['api_version']}/{$account->account_id}/feed";

        $payload = [
            'message' => $post->getFormattedContent(),
            'access_token' => $account->access_token,
        ];

        // Add media if present
        if (!empty($post->media_urls)) {
            $payload['link'] = $post->media_urls[0];
        }

        $response = Http::post($url, $payload);

        if ($response->successful()) {
            $data = $response->json();
            return $data['id'] ?? null;
        }

        throw new \Exception('Facebook API error: ' . $response->body());
    }

    protected function publishToInstagram(SocialMediaPost $post, SocialMediaAccount $account): ?string
    {
        $config = $this->platformConfigs['instagram'];
        
        // Instagram requires media for posts
        if (empty($post->media_urls)) {
            throw new \Exception('Instagram posts require media');
        }

        // Step 1: Create media container
        $containerUrl = "{$config['base_url']}/{$config['api_version']}/{$account->account_id}/media";
        
        $containerPayload = [
            'image_url' => $post->media_urls[0],
            'caption' => $post->getFormattedContent(),
            'access_token' => $account->access_token,
        ];

        $containerResponse = Http::post($containerUrl, $containerPayload);
        
        if (!$containerResponse->successful()) {
            throw new \Exception('Instagram container creation failed: ' . $containerResponse->body());
        }

        $containerId = $containerResponse->json()['id'];

        // Step 2: Publish the media
        $publishUrl = "{$config['base_url']}/{$config['api_version']}/{$account->account_id}/media_publish";
        
        $publishPayload = [
            'creation_id' => $containerId,
            'access_token' => $account->access_token,
        ];

        $publishResponse = Http::post($publishUrl, $publishPayload);

        if ($publishResponse->successful()) {
            $data = $publishResponse->json();
            return $data['id'] ?? null;
        }

        throw new \Exception('Instagram publish failed: ' . $publishResponse->body());
    }

    protected function publishToLinkedIn(SocialMediaPost $post, SocialMediaAccount $account): ?string
    {
        $config = $this->platformConfigs['linkedin'];
        $url = "{$config['base_url']}/v2/ugcPosts";

        $payload = [
            'author' => "urn:li:person:{$account->account_id}",
            'lifecycleState' => 'PUBLISHED',
            'specificContent' => [
                'com.linkedin.ugc.ShareContent' => [
                    'shareCommentary' => [
                        'text' => $post->getFormattedContent(),
                    ],
                    'shareMediaCategory' => 'NONE',
                ],
            ],
            'visibility' => [
                'com.linkedin.ugc.MemberNetworkVisibility' => 'PUBLIC',
            ],
        ];

        // Add media if present
        if (!empty($post->media_urls)) {
            $payload['specificContent']['com.linkedin.ugc.ShareContent']['shareMediaCategory'] = 'IMAGE';
            $payload['specificContent']['com.linkedin.ugc.ShareContent']['media'] = [
                [
                    'status' => 'READY',
                    'description' => [
                        'text' => $post->title ?? '',
                    ],
                    'media' => $post->media_urls[0],
                ],
            ];
        }

        $response = Http::withToken($account->access_token)
            ->post($url, $payload);

        if ($response->successful()) {
            $data = $response->json();
            return $data['id'] ?? null;
        }

        throw new \Exception('LinkedIn API error: ' . $response->body());
    }

    protected function publishToTwitter(SocialMediaPost $post, SocialMediaAccount $account): ?string
    {
        $config = $this->platformConfigs['twitter'];
        $url = "{$config['base_url']}/2/tweets";

        $payload = [
            'text' => $post->getFormattedContent(),
        ];

        // Add media if present
        if (!empty($post->media_urls)) {
            // Note: Twitter requires media to be uploaded first
            // This is a simplified version - in production, implement media upload
            $payload['media'] = ['media_ids' => []];
        }

        $response = Http::withToken($account->access_token)
            ->post($url, $payload);

        if ($response->successful()) {
            $data = $response->json();
            return $data['data']['id'] ?? null;
        }

        throw new \Exception('Twitter API error: ' . $response->body());
    }

    protected function getActiveAccount(string $platform): ?SocialMediaAccount
    {
        return SocialMediaAccount::active()
            ->byPlatform($platform)
            ->where('auto_post', true)
            ->first();
    }

    public function getAccountAnalytics(SocialMediaAccount $account, int $days = 30): array
    {
        return match ($account->platform) {
            'facebook' => $this->getFacebookAnalytics($account, $days),
            'instagram' => $this->getInstagramAnalytics($account, $days),
            'linkedin' => $this->getLinkedInAnalytics($account, $days),
            'twitter' => $this->getTwitterAnalytics($account, $days),
            default => [],
        };
    }

    protected function getFacebookAnalytics(SocialMediaAccount $account, int $days): array
    {
        $config = $this->platformConfigs['facebook'];
        $since = now()->subDays($days)->timestamp;
        $until = now()->timestamp;

        $url = "{$config['base_url']}/{$config['api_version']}/{$account->account_id}/insights";
        
        $params = [
            'metric' => 'page_impressions,page_reach,page_engaged_users,page_post_engagements',
            'since' => $since,
            'until' => $until,
            'access_token' => $account->access_token,
        ];

        $response = Http::get($url, $params);

        if ($response->successful()) {
            return $this->formatFacebookAnalytics($response->json());
        }

        return [];
    }

    protected function getInstagramAnalytics(SocialMediaAccount $account, int $days): array
    {
        $config = $this->platformConfigs['instagram'];
        $url = "{$config['base_url']}/{$config['api_version']}/{$account->account_id}/insights";
        
        $params = [
            'metric' => 'impressions,reach,profile_views,website_clicks',
            'period' => 'day',
            'access_token' => $account->access_token,
        ];

        $response = Http::get($url, $params);

        if ($response->successful()) {
            return $this->formatInstagramAnalytics($response->json());
        }

        return [];
    }

    protected function formatFacebookAnalytics(array $data): array
    {
        $analytics = [
            'impressions' => 0,
            'reach' => 0,
            'engaged_users' => 0,
            'post_engagements' => 0,
        ];

        foreach ($data['data'] ?? [] as $metric) {
            $name = $metric['name'];
            $value = $metric['values'][0]['value'] ?? 0;
            
            switch ($name) {
                case 'page_impressions':
                    $analytics['impressions'] = $value;
                    break;
                case 'page_reach':
                    $analytics['reach'] = $value;
                    break;
                case 'page_engaged_users':
                    $analytics['engaged_users'] = $value;
                    break;
                case 'page_post_engagements':
                    $analytics['post_engagements'] = $value;
                    break;
            }
        }

        return $analytics;
    }

    protected function formatInstagramAnalytics(array $data): array
    {
        $analytics = [
            'impressions' => 0,
            'reach' => 0,
            'profile_views' => 0,
            'website_clicks' => 0,
        ];

        foreach ($data['data'] ?? [] as $metric) {
            $name = $metric['name'];
            $values = $metric['values'] ?? [];
            $total = array_sum(array_column($values, 'value'));
            
            switch ($name) {
                case 'impressions':
                    $analytics['impressions'] = $total;
                    break;
                case 'reach':
                    $analytics['reach'] = $total;
                    break;
                case 'profile_views':
                    $analytics['profile_views'] = $total;
                    break;
                case 'website_clicks':
                    $analytics['website_clicks'] = $total;
                    break;
            }
        }

        return $analytics;
    }

    protected function getLinkedInAnalytics(SocialMediaAccount $account, int $days): array
    {
        // LinkedIn analytics implementation
        return [
            'impressions' => 0,
            'clicks' => 0,
            'likes' => 0,
            'comments' => 0,
            'shares' => 0,
        ];
    }

    protected function getTwitterAnalytics(SocialMediaAccount $account, int $days): array
    {
        // Twitter analytics implementation
        return [
            'impressions' => 0,
            'engagements' => 0,
            'retweets' => 0,
            'likes' => 0,
            'replies' => 0,
        ];
    }

    public function generateContentSuggestions(string $industry = 'technology'): array
    {
        $suggestions = [
            'technology' => [
                'Behind the scenes of our latest project development',
                'Tech tip Tuesday: Best practices for web security',
                'Client success story: How we increased their online presence by 300%',
                'Industry insight: The future of mobile app development',
                'Team spotlight: Meet our talented developers',
                'Quick tutorial: Essential SEO tips for small businesses',
                'Before and after: Website transformation showcase',
                'Trending now: Latest design trends for 2024',
            ],
        ];

        return $suggestions[$industry] ?? $suggestions['technology'];
    }

    public function getOptimalPostingTimes(string $platform): array
    {
        $optimalTimes = [
            'facebook' => [
                'weekdays' => ['09:00', '13:00', '15:00'],
                'weekends' => ['12:00', '14:00'],
            ],
            'instagram' => [
                'weekdays' => ['11:00', '14:00', '17:00'],
                'weekends' => ['10:00', '13:00'],
            ],
            'linkedin' => [
                'weekdays' => ['08:00', '12:00', '17:00'],
                'weekends' => [], // Not recommended
            ],
            'twitter' => [
                'weekdays' => ['09:00', '12:00', '15:00', '18:00'],
                'weekends' => ['10:00', '14:00'],
            ],
        ];

        return $optimalTimes[$platform] ?? [];
    }
}
