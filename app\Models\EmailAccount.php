<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Casts\Attribute;

class EmailAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'provider',
        'smtp_host',
        'smtp_port',
        'encryption',
        'username',
        'password',
        'access_token',
        'refresh_token',
        'token_expires_at',
        'is_active',
        'is_primary',
        'daily_send_limit',
        'emails_sent_today',
        'last_email_sent_at',
        'last_reset_at',
        'status',
        'status_message',
        'settings',
        'cost_per_email',
    ];

    protected $casts = [
        'settings' => 'array',
        'is_active' => 'boolean',
        'is_primary' => 'boolean',
        'cost_per_email' => 'decimal:4',
        'token_expires_at' => 'datetime',
        'last_email_sent_at' => 'datetime',
        'last_reset_at' => 'datetime',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    public function scopeAvailable($query)
    {
        return $query->active()->whereRaw('emails_sent_today < daily_send_limit');
    }

    public function scopeByProvider($query, $provider)
    {
        return $query->where('provider', $provider);
    }

    // Accessors & Mutators
    protected function password(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? decrypt($value) : null,
            set: fn ($value) => $value ? encrypt($value) : null,
        );
    }

    protected function accessToken(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? decrypt($value) : null,
            set: fn ($value) => $value ? encrypt($value) : null,
        );
    }

    protected function refreshToken(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? decrypt($value) : null,
            set: fn ($value) => $value ? encrypt($value) : null,
        );
    }

    // Methods
    public function canSendEmail(): bool
    {
        return $this->is_active &&
               $this->status === 'active' &&
               $this->emails_sent_today < $this->daily_send_limit &&
               !$this->isTokenExpired();
    }

    public function isTokenExpired(): bool
    {
        return $this->token_expires_at && $this->token_expires_at->isPast();
    }

    public function getRemainingEmails(): int
    {
        return max(0, $this->daily_send_limit - $this->emails_sent_today);
    }

    public function incrementEmailCount(): void
    {
        $this->increment('emails_sent_today');
        $this->update(['last_email_sent_at' => now()]);
    }

    public function resetDailyCount(): void
    {
        $this->update([
            'emails_sent_today' => 0,
            'last_reset_at' => now(),
        ]);
    }

    public function shouldResetCount(): bool
    {
        return !$this->last_reset_at || $this->last_reset_at->isYesterday();
    }

    public function updateStatus(string $status, string $message = null): void
    {
        $this->update([
            'status' => $status,
            'status_message' => $message,
        ]);
    }

    public static function getNextAvailable(): ?self
    {
        return static::available()->orderBy('emails_sent_today')->first();
    }

    public static function getPrimary(): ?self
    {
        return static::primary()->active()->first();
    }

    public function getMailConfig(): array
    {
        return [
            'transport' => 'smtp',
            'host' => $this->smtp_host,
            'port' => $this->smtp_port,
            'encryption' => $this->encryption,
            'username' => $this->username ?: $this->email,
            'password' => $this->password,
            'from' => [
                'address' => $this->email,
                'name' => $this->name,
            ],
        ];
    }
}
