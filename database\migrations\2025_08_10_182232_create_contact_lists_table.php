<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contact_lists', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['static', 'dynamic'])->default('static');
            $table->enum('status', ['active', 'inactive', 'archived'])->default('active');
            $table->json('segment_rules')->nullable(); // For dynamic lists
            $table->boolean('double_opt_in')->default(false);
            $table->boolean('auto_cleanup')->default(false);
            $table->enum('cleanup_frequency', ['daily', 'weekly', 'monthly'])->nullable();
            $table->json('tags')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();

            $table->index(['status', 'type']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contact_lists');
    }
};
