---
name: 🐛 Bug Report
about: Create a report to help us improve the email management system
title: '[BUG] '
labels: ['bug', 'needs-triage']
assignees: ''
---

# 🐛 Bug Report

## 📋 Bug Description
<!-- A clear and concise description of what the bug is -->

## 📧 Email System Component
<!-- Which part of the email system is affected? -->
- [ ] 📧 Email Import System
- [ ] 📬 Email Campaigns
- [ ] 🌍 Geographic Filtering
- [ ] 📊 Analytics Dashboard
- [ ] 🗄️ MySQL Database
- [ ] 📱 WhatsApp Integration
- [ ] 🔍 Contact Management
- [ ] 🎨 Admin Panel
- [ ] ⚡ Performance
- [ ] 🔒 Security

## 🔄 Steps to Reproduce
<!-- Steps to reproduce the behavior -->
1. Go to '...'
2. Click on '...'
3. Scroll down to '...'
4. See error

## ✅ Expected Behavior
<!-- A clear and concise description of what you expected to happen -->

## ❌ Actual Behavior
<!-- A clear and concise description of what actually happened -->

## 📸 Screenshots
<!-- If applicable, add screenshots to help explain your problem -->

## 🌐 Environment
<!-- Please complete the following information -->

### 💻 System Information
- **OS**: [e.g. Windows 10, Ubuntu 20.04, macOS 12]
- **Browser**: [e.g. Chrome 91, Firefox 89, Safari 14]
- **PHP Version**: [e.g. 8.2.0]
- **Laravel Version**: [e.g. 11.0]
- **MySQL Version**: [e.g. 8.0.28]

### 📧 Email System Information
- **Total Contacts**: [e.g. 1,000,000]
- **Email Accounts**: [e.g. 5 accounts]
- **Daily Send Limit**: [e.g. 2,500 emails/day]
- **Geographic Regions**: [e.g. 15 states]

## 📊 Performance Impact
<!-- How does this bug affect system performance? -->
- [ ] No performance impact
- [ ] Minor performance degradation
- [ ] Significant performance impact
- [ ] System unusable

## 🔒 Security Impact
<!-- Does this bug have security implications? -->
- [ ] No security impact
- [ ] Minor security concern
- [ ] Significant security vulnerability
- [ ] Critical security issue

## 📝 Error Messages
<!-- Include any error messages or logs -->

```
[Paste error messages here]
```

## 🗄️ Database Information
<!-- If database-related, provide relevant information -->
- **Table affected**: [e.g. contacts, email_campaigns]
- **Record count**: [e.g. 10,000,000 contacts]
- **Query performance**: [e.g. slow queries, timeouts]

## 📧 Email-Specific Details
<!-- If email-related, provide additional context -->
- **Email volume**: [e.g. 50,000 emails/day]
- **Geographic filter**: [e.g. Tamil Nadu contacts]
- **Campaign type**: [e.g. Newsletter, Promotional]
- **Delivery status**: [e.g. Bounced, Delivered, Pending]

## 🔍 Additional Context
<!-- Add any other context about the problem here -->

## 🚨 Urgency Level
- [ ] 🔴 Critical (System down, data loss)
- [ ] 🟠 High (Major feature broken)
- [ ] 🟡 Medium (Minor feature issue)
- [ ] 🟢 Low (Cosmetic issue)

## 🔧 Possible Solution
<!-- If you have suggestions on how to fix the bug -->

## 📋 Checklist
- [ ] I have searched for existing issues
- [ ] I have provided all required information
- [ ] I have tested this on the latest version
- [ ] I have included error messages/logs
- [ ] I have specified the affected email system component
