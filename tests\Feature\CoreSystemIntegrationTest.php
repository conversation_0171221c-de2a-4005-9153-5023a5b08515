<?php

namespace Tests\Feature;

use App\Models\Lead;
use App\Models\User;
use App\Models\EmailCampaign;
use App\Models\WhatsAppCampaign;
use App\Models\SocialMediaPost;
use App\Services\LeadScoringService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CoreSystemIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /** @test */
    public function it_can_create_and_manage_leads()
    {
        $leadData = [
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '+919876543210',
            'company' => 'Example Corp',
            'source' => 'website_contact_form',
            'service_interest' => 'web_development',
            'message' => 'I need a new website for my business',
            'status' => 'new',
        ];

        $lead = Lead::create($leadData);
        
        $this->assertDatabaseHas('leads', [
            'email' => '<EMAIL>',
            'status' => 'new',
        ]);

        // Test lead scoring if service exists
        if (app()->bound(LeadScoringService::class)) {
            $scoringService = app(LeadScoringService::class);
            $score = $scoringService->calculateScore($lead);
            
            $this->assertIsNumeric($score);
            $this->assertGreaterThanOrEqual(0, $score);
            $this->assertLessThanOrEqual(100, $score);
        }

        // Test lead status updates
        $lead->update(['status' => 'contacted']);
        $this->assertEquals('contacted', $lead->fresh()->status);
    }

    /** @test */
    public function it_can_create_email_campaigns()
    {
        $campaignData = [
            'name' => 'Test Email Campaign',
            'subject' => 'Welcome to Bhavitech',
            'content' => '<h1>Welcome!</h1><p>Thank you for your interest.</p>',
            'type' => 'newsletter',
            'status' => 'draft',
        ];

        $campaign = EmailCampaign::create($campaignData);
        
        $this->assertDatabaseHas('email_campaigns', [
            'name' => 'Test Email Campaign',
            'status' => 'draft',
        ]);

        // Test campaign status updates
        $campaign->update(['status' => 'scheduled']);
        $this->assertEquals('scheduled', $campaign->fresh()->status);
    }

    /** @test */
    public function it_can_create_whatsapp_campaigns()
    {
        $campaignData = [
            'name' => 'Test WhatsApp Campaign',
            'message' => 'Hello! Thank you for your interest in our services.',
            'type' => 'broadcast',
            'status' => 'draft',
            'sender_number' => '+919876543210',
        ];

        $campaign = WhatsAppCampaign::create($campaignData);
        
        $this->assertDatabaseHas('whatsapp_campaigns', [
            'name' => 'Test WhatsApp Campaign',
            'status' => 'draft',
        ]);

        // Test campaign updates
        $campaign->update(['status' => 'scheduled']);
        $this->assertEquals('scheduled', $campaign->fresh()->status);
    }

    /** @test */
    public function it_can_create_social_media_posts()
    {
        $postData = [
            'title' => 'Test Social Media Post',
            'content' => 'Check out our latest web development services! #webdev #bhavitech',
            'post_type' => 'text',
            'platforms' => ['facebook', 'instagram', 'linkedin'],
            'status' => 'draft',
        ];

        $post = SocialMediaPost::create($postData);
        
        $this->assertDatabaseHas('social_media_posts', [
            'title' => 'Test Social Media Post',
            'status' => 'draft',
        ]);

        // Test post updates
        $post->update(['status' => 'published']);
        $this->assertEquals('published', $post->fresh()->status);
    }

    /** @test */
    public function it_can_handle_contact_form_submissions()
    {
        $formData = [
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'phone' => '+919876543211',
            'company' => 'Smith Industries',
            'service_interest' => 'digital_marketing',
            'budget' => '1l_3l',
            'timeline' => '3_months',
            'message' => 'I need help with digital marketing for my business.',
        ];

        $response = $this->post('/contact', $formData);
        
        // Should redirect back with success message
        $response->assertRedirect();
        
        // Verify lead was created
        $this->assertDatabaseHas('leads', [
            'email' => '<EMAIL>',
            'service_interest' => 'digital_marketing',
            'status' => 'new',
        ]);
    }

    /** @test */
    public function it_validates_contact_form_data()
    {
        // Test with invalid data
        $response = $this->post('/contact', [
            'name' => '',
            'email' => 'invalid-email',
            'message' => '',
        ]);
        
        $response->assertSessionHasErrors(['name', 'email', 'message']);
    }

    /** @test */
    public function it_can_access_frontend_pages()
    {
        // Test home page
        $response = $this->get('/');
        $response->assertStatus(200);

        // Test services page
        $response = $this->get('/services');
        $response->assertStatus(200);

        // Test portfolio page
        $response = $this->get('/portfolio');
        $response->assertStatus(200);

        // Test contact page
        $response = $this->get('/contact');
        $response->assertStatus(200);

        // Test about page
        $response = $this->get('/about');
        $response->assertStatus(200);
    }

    /** @test */
    public function it_maintains_data_relationships()
    {
        // Create a user
        $user = User::factory()->create();
        
        // Create a lead and assign it to the user
        $lead = Lead::factory()->create([
            'assigned_to' => $user->id,
        ]);

        // Test relationship
        $this->assertEquals($user->id, $lead->assignedTo->id);
        $this->assertTrue($user->assignedLeads->contains($lead));
    }

    /** @test */
    public function it_handles_database_operations_efficiently()
    {
        // Create multiple leads
        $leads = Lead::factory()->count(50)->create();
        
        $this->assertCount(50, $leads);

        // Test bulk operations
        Lead::whereIn('id', $leads->pluck('id'))->update(['status' => 'contacted']);
        
        $contactedCount = Lead::where('status', 'contacted')->count();
        $this->assertEquals(50, $contactedCount);
    }

    /** @test */
    public function it_handles_json_data_fields()
    {
        // Test lead with metadata
        $lead = Lead::create([
            'name' => 'Test Lead',
            'email' => '<EMAIL>',
            'source' => 'website',
            'service_interest' => 'web_development',
            'message' => 'Test message',
            'status' => 'new',
            'metadata' => [
                'utm_source' => 'google',
                'utm_campaign' => 'summer_2024',
                'browser' => 'Chrome',
                'device' => 'Desktop',
            ],
        ]);

        $this->assertIsArray($lead->metadata);
        $this->assertEquals('google', $lead->metadata['utm_source']);
        $this->assertEquals('Chrome', $lead->metadata['browser']);
    }

    /** @test */
    public function it_handles_error_scenarios_gracefully()
    {
        // Test creating lead with duplicate email (should still work as emails aren't unique)
        $leadData = [
            'name' => 'First Lead',
            'email' => '<EMAIL>',
            'source' => 'website',
            'service_interest' => 'web_development',
            'message' => 'First message',
            'status' => 'new',
        ];

        $lead1 = Lead::create($leadData);
        $this->assertNotNull($lead1);

        // Create another lead with same email
        $leadData['name'] = 'Second Lead';
        $leadData['message'] = 'Second message';
        
        $lead2 = Lead::create($leadData);
        $this->assertNotNull($lead2);
        $this->assertNotEquals($lead1->id, $lead2->id);
    }

    /** @test */
    public function it_can_handle_large_text_content()
    {
        // Test with large content
        $largeContent = str_repeat('This is a test content. ', 1000);
        
        $campaign = EmailCampaign::create([
            'name' => 'Large Content Campaign',
            'subject' => 'Test Subject',
            'content' => $largeContent,
            'type' => 'newsletter',
            'status' => 'draft',
        ]);

        $this->assertNotNull($campaign);
        $this->assertEquals($largeContent, $campaign->content);
    }

    /** @test */
    public function it_handles_special_characters_in_data()
    {
        // Test with special characters
        $lead = Lead::create([
            'name' => 'José María García-López',
            'email' => '<EMAIL>',
            'company' => 'García & López S.A.',
            'source' => 'website',
            'service_interest' => 'web_development',
            'message' => 'Hola! Necesito una página web para mi empresa. ¿Pueden ayudarme?',
            'status' => 'new',
        ]);

        $this->assertNotNull($lead);
        $this->assertEquals('José María García-López', $lead->name);
        $this->assertEquals('García & López S.A.', $lead->company);
    }

    /** @test */
    public function it_can_handle_concurrent_operations()
    {
        // Simulate concurrent lead creation
        $leads = [];
        
        for ($i = 0; $i < 10; $i++) {
            $leads[] = Lead::create([
                'name' => "Concurrent Lead {$i}",
                'email' => "concurrent{$i}@example.com",
                'source' => 'api',
                'service_interest' => 'web_development',
                'message' => "Concurrent message {$i}",
                'status' => 'new',
            ]);
        }

        $this->assertCount(10, $leads);
        
        // Verify all leads were created
        $this->assertEquals(10, Lead::where('source', 'api')->count());
    }
}
