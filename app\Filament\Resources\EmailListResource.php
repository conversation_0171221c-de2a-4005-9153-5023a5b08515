<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EmailListResource\Pages;
use App\Models\ContactList;
use App\Models\Contact;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use League\Csv\Reader;
use League\Csv\Writer;

class EmailListResource extends Resource
{
    protected static ?string $model = ContactList::class;

    protected static ?string $navigationIcon = 'heroicon-o-envelope';

    protected static ?string $navigationLabel = 'Email Lists';

    protected static ?string $modelLabel = 'Email List';

    protected static ?string $pluralModelLabel = 'Email Lists';

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationGroup = 'Email Marketing';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('List Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true)
                            ->live(onBlur: true),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(1000)
                            ->rows(3),
                        Forms\Components\Select::make('type')
                            ->options([
                                'static' => 'Static List (Manual)',
                                'dynamic' => 'Dynamic List (Smart Segments)',
                            ])
                            ->default('static')
                            ->required()
                            ->live(),
                        Forms\Components\Select::make('status')
                            ->options([
                                'active' => 'Active',
                                'inactive' => 'Inactive',
                                'archived' => 'Archived',
                            ])
                            ->default('active')
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('Bulk Import')
                    ->schema([
                        Forms\Components\FileUpload::make('csv_file')
                            ->label('Upload CSV File')
                            ->acceptedFileTypes(['text/csv', 'application/csv'])
                            ->maxSize(50000) // 50MB
                            ->helperText('Upload a CSV file with columns: first_name, last_name, email, phone, company, etc.')
                            ->disk('local')
                            ->directory('imports'),
                        Forms\Components\Checkbox::make('skip_duplicates')
                            ->label('Skip Duplicate Emails')
                            ->default(true),
                        Forms\Components\Checkbox::make('validate_emails')
                            ->label('Validate Email Addresses')
                            ->default(true),
                    ])
                    ->visible(fn (Forms\Get $get): bool => $get('type') === 'static'),

                Forms\Components\Section::make('Smart Segment Rules')
                    ->schema([
                        Forms\Components\Repeater::make('segment_rules')
                            ->schema([
                                Forms\Components\Select::make('field')
                                    ->options([
                                        'engagement_score' => 'Engagement Score',
                                        'industry' => 'Industry',
                                        'city' => 'City',
                                        'state' => 'State',
                                        'country' => 'Country',
                                        'company' => 'Company',
                                        'source' => 'Source',
                                        'is_subscribed' => 'Subscription Status',
                                        'last_interaction_at' => 'Last Interaction',
                                        'created_at' => 'Date Added',
                                        'phone' => 'Phone Number',
                                        'tags' => 'Tags',
                                    ])
                                    ->required()
                                    ->live(),
                                Forms\Components\Select::make('operator')
                                    ->options([
                                        '=' => 'Equals',
                                        '!=' => 'Not Equals',
                                        '>' => 'Greater Than',
                                        '>=' => 'Greater Than or Equal',
                                        '<' => 'Less Than',
                                        '<=' => 'Less Than or Equal',
                                        'like' => 'Contains',
                                        'not_like' => 'Does Not Contain',
                                        'in' => 'In List',
                                        'not_in' => 'Not In List',
                                    ])
                                    ->required(),
                                Forms\Components\TextInput::make('value')
                                    ->required(),
                            ])
                            ->columns(3)
                            ->addActionLabel('Add Rule')
                            ->defaultItems(1),
                    ])
                    ->visible(fn (Forms\Get $get): bool => $get('type') === 'dynamic'),

                Forms\Components\Section::make('List Settings')
                    ->schema([
                        Forms\Components\Toggle::make('double_opt_in')
                            ->label('Require Double Opt-in')
                            ->helperText('New subscribers must confirm via email'),
                        Forms\Components\Toggle::make('auto_cleanup')
                            ->label('Auto Cleanup')
                            ->helperText('Remove bounced/unsubscribed contacts'),
                        Forms\Components\Select::make('cleanup_frequency')
                            ->label('Cleanup Frequency')
                            ->options([
                                'daily' => 'Daily',
                                'weekly' => 'Weekly',
                                'monthly' => 'Monthly',
                            ])
                            ->default('weekly')
                            ->visible(fn (Forms\Get $get): bool => $get('auto_cleanup')),
                        Forms\Components\TagsInput::make('tags')
                            ->placeholder('Add tags for organization...'),
                    ])->columns(2),

                Forms\Components\Section::make('Manual Contact Selection')
                    ->schema([
                        Forms\Components\Select::make('contacts')
                            ->relationship('contacts', 'email')
                            ->multiple()
                            ->preload()
                            ->searchable(['first_name', 'last_name', 'email', 'company'])
                            ->getOptionLabelFromRecordUsing(fn (Contact $record): string => 
                                "{$record->first_name} {$record->last_name} ({$record->email})" . 
                                ($record->company ? " - {$record->company}" : '')
                            )
                            ->helperText('For large lists, use CSV import instead'),
                    ])
                    ->visible(fn (Forms\Get $get): bool => $get('type') === 'static'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'static' => 'info',
                        'dynamic' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'warning',
                        'archived' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('contacts_count')
                    ->label('Total Contacts')
                    ->getStateUsing(fn (ContactList $record): int => $record->contacts()->count())
                    ->numeric()
                    ->sortable()
                    ->formatStateUsing(fn (int $state): string => number_format($state)),
                Tables\Columns\TextColumn::make('subscribed_count')
                    ->label('Subscribed')
                    ->getStateUsing(fn (ContactList $record): int =>
                        $record->contacts()->wherePivot('status', 'subscribed')->count()
                    )
                    ->numeric()
                    ->formatStateUsing(fn (int $state): string => number_format($state)),
                Tables\Columns\TextColumn::make('geographic_distribution')
                    ->label('Top Locations')
                    ->getStateUsing(function (ContactList $record): string {
                        $locations = $record->contacts()
                            ->selectRaw('state, COUNT(*) as count')
                            ->groupBy('state')
                            ->orderByDesc('count')
                            ->limit(3)
                            ->pluck('count', 'state')
                            ->toArray();

                        if (empty($locations)) return 'No data';

                        $result = [];
                        foreach ($locations as $state => $count) {
                            $result[] = $state . ' (' . number_format($count) . ')';
                        }

                        return implode(', ', $result);
                    })
                    ->wrap(),
                Tables\Columns\TextColumn::make('engagement_rate')
                    ->label('Engagement %')
                    ->getStateUsing(function (ContactList $record): string {
                        $total = $record->contacts()->count();
                        if ($total === 0) return '0%';

                        $engaged = $record->contacts()
                            ->where('engagement_score', '>=', 70)
                            ->count();

                        return number_format(($engaged / $total) * 100, 1) . '%';
                    })
                    ->badge()
                    ->color(fn (string $state): string =>
                        match (true) {
                            ((float) str_replace('%', '', $state)) >= 70 => 'success',
                            ((float) str_replace('%', '', $state)) >= 40 => 'warning',
                            default => 'danger',
                        }
                    ),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'static' => 'Static Lists',
                        'dynamic' => 'Dynamic Lists',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'archived' => 'Archived',
                    ]),
                Tables\Filters\SelectFilter::make('geographic_filter')
                    ->label('Geographic Filter')
                    ->options([
                        'india' => 'India',
                        'tamil_nadu' => 'Tamil Nadu',
                        'karnataka' => 'Karnataka',
                        'kerala' => 'Kerala',
                        'andhra_pradesh' => 'Andhra Pradesh',
                        'telangana' => 'Telangana',
                        'maharashtra' => 'Maharashtra',
                        'gujarat' => 'Gujarat',
                        'rajasthan' => 'Rajasthan',
                        'delhi' => 'Delhi',
                        'west_bengal' => 'West Bengal',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (!$data['value']) return $query;

                        return $query->whereHas('contacts', function ($q) use ($data) {
                            switch ($data['value']) {
                                case 'india':
                                    $q->where('country', 'India');
                                    break;
                                case 'tamil_nadu':
                                    $q->where('state', 'Tamil Nadu');
                                    break;
                                case 'karnataka':
                                    $q->where('state', 'Karnataka');
                                    break;
                                case 'kerala':
                                    $q->where('state', 'Kerala');
                                    break;
                                case 'andhra_pradesh':
                                    $q->where('state', 'Andhra Pradesh');
                                    break;
                                case 'telangana':
                                    $q->where('state', 'Telangana');
                                    break;
                                case 'maharashtra':
                                    $q->where('state', 'Maharashtra');
                                    break;
                                case 'gujarat':
                                    $q->where('state', 'Gujarat');
                                    break;
                                case 'rajasthan':
                                    $q->where('state', 'Rajasthan');
                                    break;
                                case 'delhi':
                                    $q->where('state', 'Delhi');
                                    break;
                                case 'west_bengal':
                                    $q->where('state', 'West Bengal');
                                    break;
                            }
                        });
                    }),
                Tables\Filters\Filter::make('large_lists')
                    ->query(fn (Builder $query): Builder =>
                        $query->whereIn('id', function ($subQuery) {
                            $subQuery->select('contact_list_id')
                                ->from('contact_list_members')
                                ->groupBy('contact_list_id')
                                ->havingRaw('COUNT(*) >= 1000');
                        })
                    )
                    ->label('Large Lists (1000+ contacts)'),
                Tables\Filters\Filter::make('massive_lists')
                    ->query(fn (Builder $query): Builder =>
                        $query->whereIn('id', function ($subQuery) {
                            $subQuery->select('contact_list_id')
                                ->from('contact_list_members')
                                ->groupBy('contact_list_id')
                                ->havingRaw('COUNT(*) >= 100000');
                        })
                    )
                    ->label('Massive Lists (1 Lakh+ contacts)'),
                Tables\Filters\Filter::make('high_engagement')
                    ->query(fn (Builder $query): Builder =>
                        $query->whereHas('contacts', function ($q) {
                            $q->where('engagement_score', '>=', 70);
                        })
                    )
                    ->label('High Engagement Lists'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('export')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('success')
                    ->action(function (ContactList $record) {
                        return static::exportContacts($record);
                    }),
                Tables\Actions\Action::make('duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->action(function (ContactList $record) {
                        $newList = $record->replicate();
                        $newList->name = $record->name . ' (Copy)';
                        $newList->save();

                        // Copy contacts for static lists
                        if ($record->type === 'static') {
                            $contactIds = $record->contacts()->pluck('contacts.id');
                            $newList->contacts()->attach($contactIds);
                        }

                        Notification::make()
                            ->title('List duplicated successfully')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('merge_lists')
                        ->label('Merge Lists')
                        ->icon('heroicon-o-arrows-pointing-in')
                        ->form([
                            Forms\Components\TextInput::make('new_list_name')
                                ->label('New List Name')
                                ->required(),
                            Forms\Components\Textarea::make('description')
                                ->label('Description'),
                        ])
                        ->action(function (Collection $records, array $data) {
                            return static::mergeLists($records, $data);
                        }),
                    BulkAction::make('export_all')
                        ->label('Export All')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->action(function (Collection $records) {
                            return static::exportMultipleLists($records);
                        }),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmailLists::route('/'),
            'create' => Pages\CreateEmailList::route('/create'),
            'view' => Pages\ViewEmailList::route('/{record}'),
            'edit' => Pages\EditEmailList::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', 'active')->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }

    // Custom methods for handling large email lists
    public static function exportContacts(ContactList $list)
    {
        $filename = 'email_list_' . $list->id . '_' . date('Y-m-d_H-i-s') . '.csv';
        $path = storage_path('app/exports/' . $filename);

        // Ensure directory exists
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        $csv = Writer::createFromPath($path, 'w+');

        // Add headers
        $csv->insertOne([
            'first_name', 'last_name', 'email', 'phone', 'company',
            'industry', 'city', 'state', 'country', 'engagement_score',
            'subscription_status', 'last_interaction', 'date_added'
        ]);

        // Export in chunks to handle large lists
        $list->contacts()->chunk(1000, function ($contacts) use ($csv) {
            foreach ($contacts as $contact) {
                $csv->insertOne([
                    $contact->first_name,
                    $contact->last_name,
                    $contact->email,
                    $contact->phone,
                    $contact->company,
                    $contact->industry,
                    $contact->city,
                    $contact->state,
                    $contact->country,
                    $contact->engagement_score,
                    $contact->pivot->status ?? 'subscribed',
                    $contact->last_interaction_at?->format('Y-m-d H:i:s'),
                    $contact->created_at->format('Y-m-d H:i:s'),
                ]);
            }
        });

        Notification::make()
            ->title('Export completed')
            ->body("Exported {$list->contacts()->count()} contacts to {$filename}")
            ->success()
            ->send();

        return response()->download($path)->deleteFileAfterSend();
    }

    public static function mergeLists(Collection $lists, array $data)
    {
        $newList = ContactList::create([
            'name' => $data['new_list_name'],
            'description' => $data['description'] ?? 'Merged from multiple lists',
            'type' => 'static',
            'status' => 'active',
            'created_by' => auth()->id(),
        ]);

        $allContactIds = collect();

        foreach ($lists as $list) {
            $contactIds = $list->contacts()->pluck('contacts.id');
            $allContactIds = $allContactIds->merge($contactIds);
        }

        // Remove duplicates and attach to new list
        $uniqueContactIds = $allContactIds->unique();
        $newList->contacts()->attach($uniqueContactIds);

        Notification::make()
            ->title('Lists merged successfully')
            ->body("Created new list with {$uniqueContactIds->count()} unique contacts")
            ->success()
            ->send();
    }

    public static function exportMultipleLists(Collection $lists)
    {
        $filename = 'multiple_lists_export_' . date('Y-m-d_H-i-s') . '.csv';
        $path = storage_path('app/exports/' . $filename);

        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        $csv = Writer::createFromPath($path, 'w+');

        // Add headers
        $csv->insertOne([
            'list_name', 'first_name', 'last_name', 'email', 'phone',
            'company', 'industry', 'city', 'state', 'country',
            'engagement_score', 'subscription_status'
        ]);

        foreach ($lists as $list) {
            $list->contacts()->chunk(1000, function ($contacts) use ($csv, $list) {
                foreach ($contacts as $contact) {
                    $csv->insertOne([
                        $list->name,
                        $contact->first_name,
                        $contact->last_name,
                        $contact->email,
                        $contact->phone,
                        $contact->company,
                        $contact->industry,
                        $contact->city,
                        $contact->state,
                        $contact->country,
                        $contact->engagement_score,
                        $contact->pivot->status ?? 'subscribed',
                    ]);
                }
            });
        }

        Notification::make()
            ->title('Multiple lists exported')
            ->success()
            ->send();

        return response()->download($path)->deleteFileAfterSend();
    }
}
