<?php

namespace App\Services;

use App\Models\AutomationWorkflow;
use App\Models\EmailTemplate;
use App\Models\Lead;
use App\Models\EmailCampaign;
use App\Jobs\SendAutomatedEmailJob;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class EmailAutomationService
{
    protected EmailMarketingService $emailService;

    public function __construct(EmailMarketingService $emailService)
    {
        $this->emailService = $emailService;
    }

    public function createWelcomeSequence(): AutomationWorkflow
    {
        return AutomationWorkflow::create([
            'name' => 'Welcome Email Sequence',
            'description' => 'Automated welcome sequence for new leads',
            'trigger_type' => 'form_submit',
            'trigger_conditions' => [
                [
                    'field' => 'form_type',
                    'operator' => 'equals',
                    'value' => 'contact_form',
                ]
            ],
            'workflow_steps' => [
                [
                    'action' => 'send_email',
                    'delay' => 0, // Immediate
                    'config' => [
                        'template_type' => 'welcome',
                        'subject' => 'Welcome to {{company_name}} - Let\'s Get Started!',
                    ]
                ],
                [
                    'action' => 'wait',
                    'delay' => 1440, // 24 hours in minutes
                ],
                [
                    'action' => 'send_email',
                    'delay' => 0,
                    'config' => [
                        'template_type' => 'nurture',
                        'subject' => 'Here\'s how we can help transform your business',
                    ]
                ],
                [
                    'action' => 'wait',
                    'delay' => 4320, // 3 days
                ],
                [
                    'action' => 'send_email',
                    'delay' => 0,
                    'config' => [
                        'template_type' => 'follow_up',
                        'subject' => 'Ready to discuss your project?',
                    ]
                ],
            ],
            'is_active' => true,
            'status' => 'active',
            'created_by' => auth()->id(),
        ]);
    }

    public function createNurtureSequence(string $serviceType): AutomationWorkflow
    {
        $sequences = [
            'web-development' => $this->getWebDevelopmentSequence(),
            'mobile-development' => $this->getMobileDevelopmentSequence(),
            'digital-marketing' => $this->getDigitalMarketingSequence(),
            'graphic-design' => $this->getGraphicDesignSequence(),
        ];

        $sequence = $sequences[$serviceType] ?? $this->getGenericNurtureSequence();

        return AutomationWorkflow::create([
            'name' => "Lead Nurture - " . ucwords(str_replace('-', ' ', $serviceType)),
            'description' => "Automated nurture sequence for {$serviceType} leads",
            'trigger_type' => 'lead_score',
            'trigger_conditions' => [
                [
                    'field' => 'service_interest',
                    'operator' => 'equals',
                    'value' => $serviceType,
                ],
                [
                    'field' => 'score',
                    'operator' => 'greater_than',
                    'value' => 40,
                ]
            ],
            'workflow_steps' => $sequence,
            'is_active' => true,
            'status' => 'active',
            'created_by' => auth()->id(),
        ]);
    }

    protected function getWebDevelopmentSequence(): array
    {
        return [
            [
                'action' => 'send_email',
                'delay' => 0,
                'config' => [
                    'template_type' => 'nurture',
                    'subject' => 'Transform Your Online Presence with Professional Web Development',
                    'content_focus' => 'web_development_benefits',
                ]
            ],
            [
                'action' => 'wait',
                'delay' => 2880, // 2 days
            ],
            [
                'action' => 'send_email',
                'delay' => 0,
                'config' => [
                    'template_type' => 'case_study',
                    'subject' => 'How We Increased Client Revenue by 300% with a New Website',
                    'content_focus' => 'web_development_case_study',
                ]
            ],
            [
                'action' => 'wait',
                'delay' => 4320, // 3 days
            ],
            [
                'action' => 'send_email',
                'delay' => 0,
                'config' => [
                    'template_type' => 'promotional',
                    'subject' => 'Limited Time: Free Website Audit Worth ₹5,000',
                    'content_focus' => 'free_audit_offer',
                ]
            ],
        ];
    }

    protected function getMobileDevelopmentSequence(): array
    {
        return [
            [
                'action' => 'send_email',
                'delay' => 0,
                'config' => [
                    'template_type' => 'nurture',
                    'subject' => 'Mobile Apps That Drive Business Growth',
                    'content_focus' => 'mobile_app_benefits',
                ]
            ],
            [
                'action' => 'wait',
                'delay' => 2880, // 2 days
            ],
            [
                'action' => 'send_email',
                'delay' => 0,
                'config' => [
                    'template_type' => 'educational',
                    'subject' => 'Native vs Hybrid Apps: Which is Right for Your Business?',
                    'content_focus' => 'app_development_guide',
                ]
            ],
        ];
    }

    protected function getDigitalMarketingSequence(): array
    {
        return [
            [
                'action' => 'send_email',
                'delay' => 0,
                'config' => [
                    'template_type' => 'nurture',
                    'subject' => 'Boost Your Online Visibility with Digital Marketing',
                    'content_focus' => 'digital_marketing_benefits',
                ]
            ],
            [
                'action' => 'wait',
                'delay' => 1440, // 1 day
            ],
            [
                'action' => 'send_email',
                'delay' => 0,
                'config' => [
                    'template_type' => 'educational',
                    'subject' => 'SEO Checklist: 10 Ways to Improve Your Rankings',
                    'content_focus' => 'seo_tips',
                ]
            ],
        ];
    }

    protected function getGraphicDesignSequence(): array
    {
        return [
            [
                'action' => 'send_email',
                'delay' => 0,
                'config' => [
                    'template_type' => 'nurture',
                    'subject' => 'Professional Design That Converts',
                    'content_focus' => 'design_importance',
                ]
            ],
            [
                'action' => 'wait',
                'delay' => 2880, // 2 days
            ],
            [
                'action' => 'send_email',
                'delay' => 0,
                'config' => [
                    'template_type' => 'portfolio',
                    'subject' => 'See Our Latest Design Work',
                    'content_focus' => 'design_portfolio',
                ]
            ],
        ];
    }

    protected function getGenericNurtureSequence(): array
    {
        return [
            [
                'action' => 'send_email',
                'delay' => 0,
                'config' => [
                    'template_type' => 'nurture',
                    'subject' => 'How {{company_name}} Can Help Your Business Grow',
                ]
            ],
            [
                'action' => 'wait',
                'delay' => 2880, // 2 days
            ],
            [
                'action' => 'send_email',
                'delay' => 0,
                'config' => [
                    'template_type' => 'follow_up',
                    'subject' => 'Ready to take the next step?',
                ]
            ],
        ];
    }

    public function triggerWorkflow(AutomationWorkflow $workflow, Lead $lead): void
    {
        if (!$workflow->canExecute()) {
            return;
        }

        Log::info('Triggering automation workflow', [
            'workflow_id' => $workflow->id,
            'lead_id' => $lead->id,
        ]);

        foreach ($workflow->workflow_steps as $index => $step) {
            $delay = $step['delay'] ?? 0;
            
            if ($delay > 0) {
                // Schedule the step for later execution
                SendAutomatedEmailJob::dispatch($workflow, $lead, $index)
                    ->delay(now()->addMinutes($delay));
            } else {
                // Execute immediately
                $this->executeWorkflowStep($workflow, $lead, $step);
            }
        }

        $workflow->increment('execution_count');
        $workflow->update(['last_executed_at' => now()]);
    }

    public function executeWorkflowStep(AutomationWorkflow $workflow, Lead $lead, array $step): void
    {
        $action = $step['action'] ?? null;
        $config = $step['config'] ?? [];

        switch ($action) {
            case 'send_email':
                $this->sendAutomatedEmail($lead, $config);
                break;
            case 'update_lead_score':
                $this->updateLeadScore($lead, $config);
                break;
            case 'assign_lead':
                $this->assignLead($lead, $config);
                break;
            case 'add_tag':
                $this->addTag($lead, $config);
                break;
            case 'wait':
                // Handled by job scheduling
                break;
        }
    }

    protected function sendAutomatedEmail(Lead $lead, array $config): void
    {
        $templateType = $config['template_type'] ?? 'nurture';
        $template = EmailTemplate::active()
            ->byType($templateType)
            ->first();

        if (!$template) {
            Log::warning('Email template not found for automation', [
                'template_type' => $templateType,
                'lead_id' => $lead->id,
            ]);
            return;
        }

        // Create automated campaign
        $campaign = $this->emailService->createCampaign([
            'name' => "Automated: {$template->name} - {$lead->email}",
            'subject' => $config['subject'] ?? $template->subject,
            'content' => $template->content,
            'template_id' => $template->id,
            'recipients' => [
                [
                    'email' => $lead->email,
                    'name' => $lead->name,
                    'data' => [
                        'lead_id' => $lead->id,
                        'company' => $lead->company,
                        'service_interest' => $lead->service_interest,
                    ],
                ]
            ],
        ]);

        // Send immediately
        $this->emailService->sendCampaign($campaign);

        // Create interaction record
        $lead->interactions()->create([
            'type' => 'email_sent',
            'channel' => 'email',
            'description' => "Automated email sent: {$template->name}",
            'metadata' => [
                'campaign_id' => $campaign->id,
                'template_id' => $template->id,
                'automation_type' => 'workflow',
            ],
            'interaction_date' => now(),
            'is_automated' => true,
        ]);
    }

    protected function updateLeadScore(Lead $lead, array $config): void
    {
        $points = $config['points'] ?? 0;
        $lead->increment('score', $points);
    }

    protected function assignLead(Lead $lead, array $config): void
    {
        if (isset($config['user_id'])) {
            $lead->update(['assigned_to' => $config['user_id']]);
        }
    }

    protected function addTag(Lead $lead, array $config): void
    {
        // Implementation for adding tags to leads
        // This would depend on your tagging system
    }

    public function getActiveWorkflows(): \Illuminate\Database\Eloquent\Collection
    {
        return AutomationWorkflow::active()->get();
    }

    public function checkTriggerConditions(Lead $lead): void
    {
        $workflows = $this->getActiveWorkflows();

        foreach ($workflows as $workflow) {
            if ($workflow->checkTriggerConditions($lead->toArray())) {
                $this->triggerWorkflow($workflow, $lead);
            }
        }
    }
}
