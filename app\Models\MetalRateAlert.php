<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MetalRateAlert extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'metal',
        'condition',
        'threshold',
        'notification_channels',
        'is_active',
        'last_triggered_at',
    ];

    protected $casts = [
        'threshold' => 'decimal:2',
        'notification_channels' => 'array',
        'is_active' => 'boolean',
        'last_triggered_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByMetal($query, string $metal)
    {
        return $query->where('metal', $metal);
    }

    public function scopeByCondition($query, string $condition)
    {
        return $query->where('condition', $condition);
    }
}
