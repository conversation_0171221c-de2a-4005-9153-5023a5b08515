<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Contact;
use App\Models\ContactList;
use App\Models\EmailAccount;
use App\Models\EmailCampaign;
use App\Models\EmailTemplate;
use App\Models\WhatsAppNumber;
use App\Models\WhatsAppCampaign;
use App\Models\Lead;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminPanelTestSeeder extends Seeder
{
    /**
     * Run the database seeds for comprehensive admin panel testing.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting comprehensive admin panel test data seeding...');

        // Create additional admin users for testing
        $this->createTestUsers();
        
        // Create email accounts for testing
        $this->createEmailAccounts();
        
        // Create large contact database
        $this->createContacts();
        
        // Create contact lists with different types
        $this->createContactLists();
        
        // Create email templates
        $this->createEmailTemplates();
        
        // Create email campaigns
        $this->createEmailCampaigns();
        
        // Create WhatsApp numbers and campaigns
        $this->createWhatsAppData();
        
        // Create additional leads for testing
        $this->createAdditionalLeads();

        $this->command->info('✅ Admin panel test data seeding completed successfully!');
    }

    private function createTestUsers(): void
    {
        $this->command->info('Creating test users...');
        
        // Marketing Manager
        User::firstOrCreate(['email' => '<EMAIL>'], [
            'name' => 'Marketing Manager',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'phone' => '+91 7010860892',
            'company' => 'Bhavitech',
            'city' => 'Salem',
            'state' => 'Tamil Nadu',
            'country' => 'India',
            'email_verified_at' => now(),
        ]);

        // Sales Manager
        User::firstOrCreate(['email' => '<EMAIL>'], [
            'name' => 'Sales Manager',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'phone' => '+91 **********',
            'company' => 'Bhavitech',
            'city' => 'Salem',
            'state' => 'Tamil Nadu',
            'country' => 'India',
            'email_verified_at' => now(),
        ]);
    }

    private function createEmailAccounts(): void
    {
        $this->command->info('Creating email accounts...');
        
        $emailAccounts = [
            [
                'name' => 'Primary Marketing Account',
                'email' => '<EMAIL>',
                'provider' => 'gmail',
                'smtp_host' => 'smtp.gmail.com',
                'smtp_port' => 587,
                'username' => '<EMAIL>',
                'password' => encrypt('app_password_here'),
                'daily_send_limit' => 500,
                'emails_sent_today' => 245,
                'is_active' => true,
                'is_primary' => true,
            ],
            [
                'name' => 'Sales Communication',
                'email' => '<EMAIL>',
                'provider' => 'outlook',
                'smtp_host' => 'smtp-mail.outlook.com',
                'smtp_port' => 587,
                'username' => '<EMAIL>',
                'password' => encrypt('app_password_here'),
                'daily_send_limit' => 300,
                'emails_sent_today' => 89,
                'is_active' => true,
                'is_primary' => false,
            ],
            [
                'name' => 'Newsletter Account',
                'email' => '<EMAIL>',
                'provider' => 'sendgrid',
                'smtp_host' => 'smtp.sendgrid.net',
                'smtp_port' => 587,
                'username' => 'apikey',
                'password' => encrypt('sendgrid_api_key_here'),
                'daily_send_limit' => 1000,
                'emails_sent_today' => 756,
                'is_active' => true,
                'is_primary' => false,
            ],
            [
                'name' => 'Support Account',
                'email' => '<EMAIL>',
                'provider' => 'gmail',
                'smtp_host' => 'smtp.gmail.com',
                'smtp_port' => 587,
                'username' => '<EMAIL>',
                'password' => encrypt('app_password_here'),
                'daily_send_limit' => 200,
                'emails_sent_today' => 45,
                'is_active' => true,
                'is_primary' => false,
            ],
        ];

        foreach ($emailAccounts as $account) {
            EmailAccount::firstOrCreate(['email' => $account['email']], $account);
        }
    }

    private function createContacts(): void
    {
        $this->command->info('Creating contact database (100 contacts for testing)...');

        // Create 100 contacts with realistic data for testing
        for ($i = 0; $i < 100; $i++) {
            $firstName = fake('en_IN')->firstName();
            $lastName = fake('en_IN')->lastName();
            
            Contact::create([
                'first_name' => $firstName,
                'last_name' => $lastName,
                'email' => strtolower($firstName . '.' . $lastName . $i) . '@' . fake()->safeEmailDomain(),
                'phone' => '+91' . fake('en_IN')->numerify('##########'),
                'company' => fake('en_IN')->company(),
                'job_title' => fake()->jobTitle(),
                'industry' => fake()->randomElement([
                    'Technology', 'Healthcare', 'Finance', 'Education', 'Manufacturing',
                    'Retail', 'Real Estate', 'Automotive', 'Food & Beverage', 'Consulting'
                ]),
                'city' => fake('en_IN')->city(),
                'state' => fake()->randomElement([
                    'Tamil Nadu', 'Karnataka', 'Kerala', 'Andhra Pradesh', 'Telangana',
                    'Maharashtra', 'Gujarat', 'Rajasthan', 'Delhi', 'Punjab'
                ]),
                'country' => 'India',
                'source' => fake()->randomElement(['website', 'social_media', 'referral', 'advertisement', 'event', 'cold_outreach']),
                'tags' => json_encode(fake()->randomElements(['hot_lead', 'qualified', 'interested', 'follow_up', 'demo_requested'], rand(1, 3))),
                'engagement_score' => rand(0, 100),
                'last_interaction_at' => fake()->optional(0.7)->dateTimeBetween('-30 days', 'now'),
                'is_subscribed' => fake()->boolean(85), // 85% subscribed
                'is_active' => fake()->boolean(95), // 95% active
            ]);
        }
    }

    private function createContactLists(): void
    {
        $this->command->info('Creating contact lists with different types...');

        $admin = User::where('role', 'admin')->first();

        // Static contact lists
        $staticLists = [
            [
                'name' => 'Newsletter Subscribers',
                'description' => 'All users who subscribed to our newsletter',
                'type' => 'static',
                'status' => 'active',
                'created_by' => $admin->id,
            ],
            [
                'name' => 'High-Value Prospects',
                'description' => 'Prospects with high engagement scores and large company size',
                'type' => 'static',
                'status' => 'active',
                'created_by' => $admin->id,
            ],
            [
                'name' => 'Event Attendees - Tech Summit 2024',
                'description' => 'Contacts from our recent tech summit event',
                'type' => 'static',
                'status' => 'active',
                'created_by' => $admin->id,
            ],
        ];

        // Dynamic contact lists
        $dynamicLists = [
            [
                'name' => 'Highly Engaged Contacts',
                'description' => 'Contacts with engagement score > 70',
                'type' => 'dynamic',
                'status' => 'active',
                'segment_rules' => json_encode([
                    ['field' => 'engagement_score', 'operator' => '>', 'value' => '70']
                ]),
                'created_by' => $admin->id,
            ],
            [
                'name' => 'Technology Industry Leads',
                'description' => 'All contacts from technology industry',
                'type' => 'dynamic',
                'status' => 'active',
                'segment_rules' => json_encode([
                    ['field' => 'industry', 'operator' => '=', 'value' => 'Technology']
                ]),
                'created_by' => $admin->id,
            ],
        ];

        foreach (array_merge($staticLists, $dynamicLists) as $listData) {
            $contactList = ContactList::create($listData);

            // Add random contacts to static lists
            if ($listData['type'] === 'static') {
                $contacts = Contact::inRandomOrder()->limit(rand(50, 200))->get();
                foreach ($contacts as $contact) {
                    $contactList->addContact($contact);
                }
            }
        }
    }

    private function createEmailTemplates(): void
    {
        $this->command->info('Creating email templates...');

        $admin = User::where('role', 'admin')->first();

        $templates = [
            [
                'name' => 'Welcome Email',
                'subject' => 'Welcome to Bhavitech - Let\'s Transform Your Business!',
                'content' => '<h1>Welcome {{first_name}}!</h1><p>Thank you for joining Bhavitech. We\'re excited to help transform your business with our cutting-edge digital solutions.</p><p>Best regards,<br>The Bhavitech Team</p>',
                'type' => 'welcome',
                'category' => 'customer_onboarding',
                'variables' => json_encode(['first_name', 'last_name', 'company']),
                'created_by' => $admin->id,
            ],
            [
                'name' => 'Newsletter Template',
                'subject' => 'Bhavitech Monthly Newsletter - {{month}} {{year}}',
                'content' => '<h1>Monthly Newsletter</h1><p>Hi {{first_name}},</p><p>Here are the latest updates from Bhavitech...</p>',
                'type' => 'newsletter',
                'category' => 'marketing',
                'variables' => json_encode(['first_name', 'month', 'year']),
                'created_by' => $admin->id,
            ],
            [
                'name' => 'Lead Follow-up',
                'subject' => 'Following up on your inquiry about {{service}}',
                'content' => '<p>Hi {{first_name}},</p><p>Thank you for your interest in our {{service}} services. I wanted to follow up...</p>',
                'type' => 'follow_up',
                'category' => 'lead_nurture',
                'variables' => json_encode(['first_name', 'service', 'company']),
                'created_by' => $admin->id,
            ],
            [
                'name' => 'Promotional Offer',
                'subject' => '🎉 Special Offer: 20% Off Web Development Services',
                'content' => '<h1>Limited Time Offer!</h1><p>Hi {{first_name}},</p><p>Get 20% off our web development services this month...</p>',
                'type' => 'promotional',
                'category' => 'promotional',
                'variables' => json_encode(['first_name', 'company']),
                'created_by' => $admin->id,
            ],
        ];

        foreach ($templates as $template) {
            EmailTemplate::create($template);
        }
    }

    private function createEmailCampaigns(): void
    {
        $this->command->info('Creating email campaigns...');

        $admin = User::where('role', 'admin')->first();
        $contactLists = ContactList::all();

        $campaigns = [
            [
                'name' => 'Monthly Newsletter - December 2024',
                'subject' => 'Bhavitech December Newsletter - Year End Special',
                'content' => '<h1>December Newsletter</h1><p>As we wrap up 2024, here are our biggest achievements...</p>',
                'type' => 'newsletter',
                'status' => 'sent',
                'sender_name' => 'Bhavitech Team',
                'sender_email' => '<EMAIL>',
                'scheduled_at' => now()->subDays(5),
                'sent_at' => now()->subDays(5),
                'created_by' => $admin->id,
            ],
            [
                'name' => 'New Year Promotion Campaign',
                'subject' => '🎊 New Year Special: 30% Off All Services',
                'content' => '<h1>New Year Special Offer!</h1><p>Start 2025 with amazing savings...</p>',
                'type' => 'promotional',
                'status' => 'scheduled',
                'sender_name' => 'Bhavitech Sales',
                'sender_email' => '<EMAIL>',
                'scheduled_at' => now()->addDays(3),
                'created_by' => $admin->id,
            ],
        ];

        foreach ($campaigns as $campaignData) {
            $campaign = EmailCampaign::create($campaignData);

            // Assign random contact lists to campaigns
            if ($contactLists->count() > 0) {
                $randomLists = $contactLists->random(rand(1, 2));
                $recipients = [];
                foreach ($randomLists as $list) {
                    $recipients[] = ['type' => 'list', 'id' => $list->id];
                }
                $campaign->update(['recipients' => json_encode($recipients)]);
            }
        }
    }

    private function createWhatsAppData(): void
    {
        $this->command->info('Creating WhatsApp numbers and campaigns...');

        // Create WhatsApp numbers
        $whatsappNumbers = [
            [
                'name' => 'Primary Business Number',
                'phone_number' => '+************',
                'phone_number_id' => 'wa_' . fake()->uuid(),
                'access_token' => 'test_access_token_' . fake()->uuid(),
                'is_active' => true,
                'is_primary' => true,
                'daily_message_limit' => 1000,
                'messages_sent_today' => 245,
                'business_profile' => json_encode([
                    'name' => 'Bhavitech',
                    'description' => 'Leading Digital Marketing & Web Development Company',
                    'website' => 'https://bhavitech.com',
                ]),
                'status' => 'active',
            ],
            [
                'name' => 'Support Number',
                'phone_number' => '+************',
                'phone_number_id' => 'wa_' . fake()->uuid(),
                'access_token' => 'test_access_token_' . fake()->uuid(),
                'is_active' => true,
                'is_primary' => false,
                'daily_message_limit' => 500,
                'messages_sent_today' => 89,
                'business_profile' => json_encode([
                    'name' => 'Bhavitech Support',
                    'description' => 'Customer Support',
                ]),
                'status' => 'active',
            ],
        ];

        foreach ($whatsappNumbers as $number) {
            WhatsAppNumber::create($number);
        }

        // Create WhatsApp campaigns
        $admin = User::where('role', 'admin')->first();
        $campaigns = [
            [
                'name' => 'Welcome Message Campaign',
                'message' => 'Hi {{name}}! Welcome to Bhavitech. We\'re excited to help transform your business with our digital solutions. Reply STOP to unsubscribe.',
                'type' => 'promotional',
                'status' => 'active',
                'created_by' => $admin->id,
            ],
            [
                'name' => 'Follow-up Campaign',
                'message' => 'Hi {{name}}, thank you for your interest in our services. Our team will contact you shortly to discuss your requirements.',
                'type' => 'follow_up',
                'status' => 'active',
                'created_by' => $admin->id,
            ],
        ];

        foreach ($campaigns as $campaign) {
            WhatsAppCampaign::create($campaign);
        }
    }

    private function createAdditionalLeads(): void
    {
        $this->command->info('Creating additional leads for testing...');

        $admin = User::where('role', 'admin')->first();

        // Create 50 additional leads with various statuses
        for ($i = 0; $i < 50; $i++) {
            Lead::create([
                'name' => fake('en_IN')->name(),
                'email' => fake()->unique()->safeEmail(),
                'phone' => '+91' . fake('en_IN')->numerify('##########'),
                'company' => fake('en_IN')->company(),
                'source' => fake()->randomElement(['website', 'social_media', 'referral', 'advertisement', 'cold_call']),
                'status' => fake()->randomElement(['new', 'contacted', 'qualified', 'converted', 'lost']),
                'score' => rand(0, 100),
                'service_interest' => fake()->randomElement(['web_development', 'mobile_development', 'digital_marketing', 'graphic_design', 'consultation']),
                'message' => fake()->paragraph(),
                'assigned_to' => $admin->id,
                'metadata' => [
                    'budget_range' => fake()->randomElement(['under_50k', '50k_1l', '1l_3l', '3l_5l', 'above_5l']),
                    'timeline' => fake()->randomElement(['asap', '1_month', '3_months', '6_months', 'flexible']),
                    'city' => fake('en_IN')->city(),
                    'state' => fake()->randomElement(['Tamil Nadu', 'Karnataka', 'Kerala', 'Andhra Pradesh', 'Telangana']),
                    'country' => 'India',
                ],
                'last_contacted_at' => fake()->optional(0.6)->dateTimeBetween('-30 days', 'now'),
                'qualified_at' => fake()->optional(0.3)->dateTimeBetween('-20 days', 'now'),
                'converted_at' => fake()->optional(0.1)->dateTimeBetween('-10 days', 'now'),
            ]);
        }
    }
}
