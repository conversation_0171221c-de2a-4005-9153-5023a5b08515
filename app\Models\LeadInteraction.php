<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LeadInteraction extends Model
{
    use HasFactory;

    protected $fillable = [
        'lead_id',
        'type',
        'channel',
        'description',
        'metadata',
        'interaction_date',
        'user_id',
        'is_automated',
    ];

    protected $casts = [
        'metadata' => 'array',
        'interaction_date' => 'datetime',
        'is_automated' => 'boolean',
    ];

    // Relationships
    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeAutomated($query)
    {
        return $query->where('is_automated', true);
    }

    public function scopeManual($query)
    {
        return $query->where('is_automated', false);
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('interaction_date', '>=', now()->subDays($days));
    }
}
