<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AutomationWorkflow extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'trigger_type',
        'trigger_conditions',
        'workflow_steps',
        'is_active',
        'execution_count',
        'last_executed_at',
        'status',
        'settings',
        'created_by',
    ];

    protected $casts = [
        'trigger_conditions' => 'array',
        'workflow_steps' => 'array',
        'settings' => 'array',
        'is_active' => 'boolean',
        'last_executed_at' => 'datetime',
    ];

    // Relationships
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    public function scopeByTrigger($query, $triggerType)
    {
        return $query->where('trigger_type', $triggerType);
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopePaused($query)
    {
        return $query->where('status', 'paused');
    }

    // Methods
    public function canExecute(): bool
    {
        return $this->is_active && $this->status === 'active';
    }

    public function execute(array $data = []): bool
    {
        if (!$this->canExecute()) {
            return false;
        }

        try {
            foreach ($this->workflow_steps as $step) {
                $this->executeStep($step, $data);
            }

            $this->increment('execution_count');
            $this->update(['last_executed_at' => now()]);

            return true;
        } catch (\Exception $e) {
            \Log::error('Workflow execution failed', [
                'workflow_id' => $this->id,
                'error' => $e->getMessage(),
                'data' => $data
            ]);

            return false;
        }
    }

    protected function executeStep(array $step, array $data): void
    {
        $action = $step['action'] ?? null;
        $config = $step['config'] ?? [];

        switch ($action) {
            case 'send_email':
                $this->sendEmail($config, $data);
                break;
            case 'send_whatsapp':
                $this->sendWhatsApp($config, $data);
                break;
            case 'update_lead_score':
                $this->updateLeadScore($config, $data);
                break;
            case 'assign_lead':
                $this->assignLead($config, $data);
                break;
            case 'add_tag':
                $this->addTag($config, $data);
                break;
            case 'wait':
                // For time-based delays (handled by queue system)
                break;
        }
    }

    protected function sendEmail(array $config, array $data): void
    {
        // Implementation would use email service
    }

    protected function sendWhatsApp(array $config, array $data): void
    {
        // Implementation would use WhatsApp service
    }

    protected function updateLeadScore(array $config, array $data): void
    {
        if (isset($data['lead_id'])) {
            $lead = Lead::find($data['lead_id']);
            if ($lead) {
                $lead->increment('score', $config['points'] ?? 0);
            }
        }
    }

    protected function assignLead(array $config, array $data): void
    {
        if (isset($data['lead_id']) && isset($config['user_id'])) {
            $lead = Lead::find($data['lead_id']);
            if ($lead) {
                $lead->update(['assigned_to' => $config['user_id']]);
            }
        }
    }

    protected function addTag(array $config, array $data): void
    {
        // Implementation would add tags to lead/contact
    }

    public function checkTriggerConditions(array $data): bool
    {
        foreach ($this->trigger_conditions as $condition) {
            if (!$this->evaluateCondition($condition, $data)) {
                return false;
            }
        }

        return true;
    }

    protected function evaluateCondition(array $condition, array $data): bool
    {
        $field = $condition['field'] ?? null;
        $operator = $condition['operator'] ?? 'equals';
        $value = $condition['value'] ?? null;
        $dataValue = data_get($data, $field);

        return match ($operator) {
            'equals' => $dataValue == $value,
            'not_equals' => $dataValue != $value,
            'greater_than' => $dataValue > $value,
            'less_than' => $dataValue < $value,
            'contains' => str_contains($dataValue, $value),
            'starts_with' => str_starts_with($dataValue, $value),
            'ends_with' => str_ends_with($dataValue, $value),
            default => false,
        };
    }
}
