<?php

namespace App\Services;

use App\Models\Contact;
use App\Models\EmailValidation;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class AIEmailListCleaner
{
    private const VALIDATION_CACHE_TTL = 86400 * 7; // 7 days
    private const ENGAGEMENT_THRESHOLD_DAYS = 90;
    private const MIN_ENGAGEMENT_SCORE = 30;
    
    /**
     * Clean and validate email list using AI
     */
    public function cleanEmailList(array $emails): array
    {
        $results = [
            'valid' => [],
            'invalid' => [],
            'risky' => [],
            'unknown' => [],
            'statistics' => []
        ];
        
        foreach ($emails as $email) {
            $validation = $this->validateEmail($email);
            $category = $this->categorizeEmail($validation);
            
            $results[$category][] = [
                'email' => $email,
                'validation' => $validation
            ];
        }
        
        $results['statistics'] = $this->generateStatistics($results);
        
        return $results;
    }
    
    /**
     * Validate individual email address
     */
    public function validateEmail(string $email): array
    {
        $cacheKey = "email_validation_" . md5($email);
        
        return Cache::remember($cacheKey, self::VALIDATION_CACHE_TTL, function () use ($email) {
            $validation = [
                'email' => $email,
                'is_valid_format' => $this->validateEmailFormat($email),
                'domain_exists' => $this->checkDomainExists($email),
                'is_disposable' => $this->isDisposableEmail($email),
                'is_role_based' => $this->isRoleBasedEmail($email),
                'risk_score' => 0,
                'engagement_prediction' => 0,
                'deliverability_score' => 0,
                'validation_date' => now()
            ];
            
            // Calculate risk score
            $validation['risk_score'] = $this->calculateRiskScore($validation);
            
            // Predict engagement using AI
            $validation['engagement_prediction'] = $this->predictEngagement($email);
            
            // Calculate deliverability score
            $validation['deliverability_score'] = $this->calculateDeliverabilityScore($validation);
            
            // Store validation result
            $this->storeValidationResult($validation);
            
            return $validation;
        });
    }
    
    /**
     * Validate email format using advanced regex
     */
    private function validateEmailFormat(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Check if domain exists and has MX record
     */
    private function checkDomainExists(string $email): bool
    {
        $domain = substr(strrchr($email, "@"), 1);
        
        if (!$domain) {
            return false;
        }
        
        // Check MX record
        return checkdnsrr($domain, 'MX') || checkdnsrr($domain, 'A');
    }
    
    /**
     * Check if email is from disposable email provider
     */
    private function isDisposableEmail(string $email): bool
    {
        $domain = substr(strrchr($email, "@"), 1);
        
        $disposableDomains = [
            '10minutemail.com', 'guerrillamail.com', 'mailinator.com',
            'tempmail.org', 'yopmail.com', 'throwaway.email',
            'temp-mail.org', 'getnada.com', 'maildrop.cc'
        ];
        
        return in_array(strtolower($domain), $disposableDomains);
    }
    
    /**
     * Check if email is role-based (info@, admin@, etc.)
     */
    private function isRoleBasedEmail(string $email): bool
    {
        $localPart = substr($email, 0, strpos($email, '@'));
        
        $rolePrefixes = [
            'admin', 'administrator', 'info', 'support', 'help',
            'sales', 'marketing', 'contact', 'service', 'noreply',
            'no-reply', 'webmaster', 'postmaster', 'hostmaster'
        ];
        
        return in_array(strtolower($localPart), $rolePrefixes);
    }
    
    /**
     * Calculate risk score based on various factors
     */
    private function calculateRiskScore(array $validation): int
    {
        $score = 0;
        
        if (!$validation['is_valid_format']) $score += 50;
        if (!$validation['domain_exists']) $score += 40;
        if ($validation['is_disposable']) $score += 30;
        if ($validation['is_role_based']) $score += 20;
        
        // Check domain reputation
        $domain = substr(strrchr($validation['email'], "@"), 1);
        if ($this->isDomainBlacklisted($domain)) {
            $score += 35;
        }
        
        return min($score, 100);
    }
    
    /**
     * Predict engagement using AI/ML model
     */
    private function predictEngagement(string $email): int
    {
        // Get historical engagement data
        $contact = Contact::where('email', $email)->first();
        
        if (!$contact) {
            // New contact - use domain-based prediction
            return $this->predictEngagementByDomain($email);
        }
        
        // Calculate engagement score based on historical data
        $engagementScore = 0;
        
        // Recent email opens
        $recentOpens = $contact->emailInteractions()
            ->where('type', 'open')
            ->where('created_at', '>=', now()->subDays(self::ENGAGEMENT_THRESHOLD_DAYS))
            ->count();
            
        // Recent email clicks
        $recentClicks = $contact->emailInteractions()
            ->where('type', 'click')
            ->where('created_at', '>=', now()->subDays(self::ENGAGEMENT_THRESHOLD_DAYS))
            ->count();
            
        // Recent form submissions
        $recentSubmissions = $contact->leads()
            ->where('created_at', '>=', now()->subDays(self::ENGAGEMENT_THRESHOLD_DAYS))
            ->count();
        
        // Calculate weighted score
        $engagementScore = ($recentOpens * 2) + ($recentClicks * 5) + ($recentSubmissions * 10);
        
        // Normalize to 0-100 scale
        return min($engagementScore, 100);
    }
    
    /**
     * Predict engagement by domain analysis
     */
    private function predictEngagementByDomain(string $email): int
    {
        $domain = substr(strrchr($email, "@"), 1);
        
        // Get average engagement for this domain
        $domainEngagement = Cache::remember("domain_engagement_{$domain}", 3600, function () use ($domain) {
            $contacts = Contact::where('email', 'LIKE', "%@{$domain}")
                ->with(['emailInteractions', 'leads'])
                ->get();
                
            if ($contacts->isEmpty()) {
                return 50; // Default score for unknown domains
            }
            
            $totalScore = 0;
            foreach ($contacts as $contact) {
                $score = $this->calculateContactEngagementScore($contact);
                $totalScore += $score;
            }
            
            return $totalScore / $contacts->count();
        });
        
        return (int) $domainEngagement;
    }
    
    /**
     * Calculate deliverability score
     */
    private function calculateDeliverabilityScore(array $validation): int
    {
        $score = 100;
        
        if (!$validation['is_valid_format']) $score -= 50;
        if (!$validation['domain_exists']) $score -= 40;
        if ($validation['is_disposable']) $score -= 30;
        if ($validation['is_role_based']) $score -= 15;
        
        // Reduce score based on risk
        $score -= ($validation['risk_score'] * 0.3);
        
        return max(0, (int) $score);
    }
    
    /**
     * Categorize email based on validation results
     */
    private function categorizeEmail(array $validation): string
    {
        if ($validation['risk_score'] >= 70) {
            return 'invalid';
        }
        
        if ($validation['risk_score'] >= 40) {
            return 'risky';
        }
        
        if ($validation['deliverability_score'] >= 70 && $validation['engagement_prediction'] >= 30) {
            return 'valid';
        }
        
        return 'unknown';
    }
    
    /**
     * Check if domain is blacklisted
     */
    private function isDomainBlacklisted(string $domain): bool
    {
        $blacklistedDomains = Cache::remember('blacklisted_domains', 86400, function () {
            // This could be loaded from a database or external service
            return [
                'spam.com', 'fake.com', 'test.com', 'example.com'
            ];
        });
        
        return in_array(strtolower($domain), $blacklistedDomains);
    }
    
    /**
     * Store validation result in database
     */
    private function storeValidationResult(array $validation): void
    {
        EmailValidation::updateOrCreate(
            ['email' => $validation['email']],
            [
                'is_valid_format' => $validation['is_valid_format'],
                'domain_exists' => $validation['domain_exists'],
                'is_disposable' => $validation['is_disposable'],
                'is_role_based' => $validation['is_role_based'],
                'risk_score' => $validation['risk_score'],
                'engagement_prediction' => $validation['engagement_prediction'],
                'deliverability_score' => $validation['deliverability_score'],
                'validated_at' => now()
            ]
        );
    }
    
    /**
     * Generate cleaning statistics
     */
    private function generateStatistics(array $results): array
    {
        $total = count($results['valid']) + count($results['invalid']) + 
                count($results['risky']) + count($results['unknown']);
                
        return [
            'total_processed' => $total,
            'valid_count' => count($results['valid']),
            'invalid_count' => count($results['invalid']),
            'risky_count' => count($results['risky']),
            'unknown_count' => count($results['unknown']),
            'valid_percentage' => $total > 0 ? round((count($results['valid']) / $total) * 100, 2) : 0,
            'invalid_percentage' => $total > 0 ? round((count($results['invalid']) / $total) * 100, 2) : 0,
            'cleaning_efficiency' => $total > 0 ? round(((count($results['valid']) + count($results['risky'])) / $total) * 100, 2) : 0
        ];
    }
    
    /**
     * Calculate contact engagement score
     */
    private function calculateContactEngagementScore($contact): int
    {
        $opens = $contact->emailInteractions()->where('type', 'open')->count();
        $clicks = $contact->emailInteractions()->where('type', 'click')->count();
        $leads = $contact->leads()->count();
        
        return min(($opens * 2) + ($clicks * 5) + ($leads * 10), 100);
    }
    
    /**
     * Bulk clean contact list
     */
    public function bulkCleanContacts(int $batchSize = 100): array
    {
        $contacts = Contact::whereNull('email_validated_at')
            ->orWhere('email_validated_at', '<', now()->subDays(30))
            ->limit($batchSize)
            ->get();
            
        $results = ['processed' => 0, 'cleaned' => 0, 'errors' => []];
        
        foreach ($contacts as $contact) {
            try {
                $validation = $this->validateEmail($contact->email);
                
                $contact->update([
                    'email_validation_score' => $validation['deliverability_score'],
                    'email_risk_score' => $validation['risk_score'],
                    'email_validated_at' => now()
                ]);
                
                // Mark as cleaned if low quality
                if ($validation['risk_score'] >= 70) {
                    $contact->update(['is_email_valid' => false]);
                    $results['cleaned']++;
                }
                
                $results['processed']++;
                
            } catch (\Exception $e) {
                $results['errors'][] = [
                    'contact_id' => $contact->id,
                    'email' => $contact->email,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
}
