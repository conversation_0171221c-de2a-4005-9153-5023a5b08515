<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

class Setting extends Model
{
    use HasFactory;

    protected $fillable = [
        'key',
        'value',
        'category',
        'type',
        'description',
        'validation_rules',
        'options',
        'is_encrypted',
        'is_public',
        'requires_restart',
        'sort_order',
        'is_active',
        'group',
        'metadata',
        'last_modified_at',
        'modified_by',
    ];

    protected $casts = [
        'validation_rules' => 'array',
        'options' => 'array',
        'metadata' => 'array',
        'is_encrypted' => 'boolean',
        'is_public' => 'boolean',
        'requires_restart' => 'boolean',
        'is_active' => 'boolean',
        'last_modified_at' => 'datetime',
    ];

    // Relationships
    public function modifier(): BelongsTo
    {
        return $this->belongsTo(User::class, 'modified_by');
    }

    // Accessors & Mutators
    public function getValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            try {
                return Crypt::decryptString($value);
            } catch (\Exception $e) {
                return $value; // Return original if decryption fails
            }
        }

        return $this->castValue($value);
    }

    public function setValueAttribute($value)
    {
        if ($this->is_encrypted && $value) {
            $this->attributes['value'] = Crypt::encryptString($value);
        } else {
            $this->attributes['value'] = $this->prepareValueForStorage($value);
        }
    }

    // Helper Methods
    protected function castValue($value)
    {
        if (is_null($value)) {
            return null;
        }

        switch ($this->type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'array':
            case 'json':
                return is_string($value) ? json_decode($value, true) : $value;
            default:
                return $value;
        }
    }

    protected function prepareValueForStorage($value)
    {
        if (in_array($this->type, ['array', 'json']) && is_array($value)) {
            return json_encode($value);
        }

        return $value;
    }

    // Static Methods for Settings Management
    public static function get(string $key, $default = null)
    {
        $cacheKey = "setting.{$key}";

        return Cache::remember($cacheKey, 3600, function () use ($key, $default) {
            $setting = static::where('key', $key)
                ->where('is_active', true)
                ->first();

            return $setting ? $setting->value : $default;
        });
    }

    public static function set(string $key, $value, array $attributes = []): self
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            array_merge([
                'value' => $value,
                'last_modified_at' => now(),
                'modified_by' => auth()->id(),
            ], $attributes)
        );

        // Clear cache
        Cache::forget("setting.{$key}");
        Cache::forget('settings.all');

        return $setting;
    }

    public static function getByCategory(string $category): array
    {
        $cacheKey = "settings.category.{$category}";

        return Cache::remember($cacheKey, 3600, function () use ($category) {
            return static::where('category', $category)
                ->where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('key')
                ->get()
                ->pluck('value', 'key')
                ->toArray();
        });
    }

    public static function getAllSettings(): array
    {
        return Cache::remember('settings.all', 3600, function () {
            return static::where('is_active', true)
                ->get()
                ->groupBy('category')
                ->map(function ($settings) {
                    return $settings->pluck('value', 'key')->toArray();
                })
                ->toArray();
        });
    }

    public static function clearCache(): void
    {
        Cache::forget('settings.all');

        // Clear individual setting caches
        static::all()->each(function ($setting) {
            Cache::forget("setting.{$setting->key}");
            Cache::forget("settings.category.{$setting->category}");
        });
    }

    // Scopes
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByGroup($query, string $group)
    {
        return $query->where('group', $group);
    }

    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('key');
    }
}
