@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Fonts - Optimized for performance */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Base Styles */
@layer base {
    html {
        font-family: 'Inter', system-ui, sans-serif;
        scroll-behavior: smooth;
    }

    body {
        @apply antialiased;
    }
}

/* Component Styles */
@layer components {
    /* Gradient Backgrounds */
    .gradient-bg {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .gradient-text {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Glass Effect */
    .glass-effect {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Button Styles */
    .btn-primary {
        @apply bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold py-3 px-6 rounded-full hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg;
    }

    .btn-secondary {
        @apply bg-transparent border-2 border-white text-white font-semibold py-3 px-6 rounded-full hover:bg-white hover:text-gray-900 transition-all duration-300 transform hover:scale-105;
    }

    /* Card Styles */
    .card {
        @apply bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2;
    }

    .card-gradient {
        @apply bg-gradient-to-br from-white to-gray-50 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2;
    }

    /* Container */
    .container-custom {
        @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
    }

    /* Form Styles */
    .form-input {
        @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
    }

    .form-textarea {
        @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none;
    }

    .form-select {
        @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white;
    }
}

/* Utility Styles */
@layer utilities {
    /* Animations */
    .animate-float {
        animation: float 6s ease-in-out infinite;
    }

    .animate-pulse-slow {
        animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    .animate-fade-in {
        animation: fadeIn 1s ease-out;
    }

    .animate-fade-in-up {
        animation: fadeInUp 0.8s ease-out;
    }

    .animate-slide-up {
        animation: slideUp 0.8s ease-out;
    }

    .animate-scale-in {
        animation: scaleIn 0.5s ease-out;
    }

    .animate-bounce-in {
        animation: bounceIn 0.8s ease-out;
    }

    /* Animation Delays */
    .animation-delay-200 {
        animation-delay: 0.2s;
    }

    .animation-delay-400 {
        animation-delay: 0.4s;
    }

    .animation-delay-600 {
        animation-delay: 0.6s;
    }

    .animation-delay-800 {
        animation-delay: 0.8s;
    }

    /* Hover Effects */
    .hover-lift {
        @apply transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl;
    }

    .hover-scale {
        @apply transition-transform duration-300 hover:scale-105;
    }

    .hover-glow {
        @apply transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/25;
    }

    /* Text Effects */
    .text-shadow {
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .text-shadow-lg {
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    /* Background Patterns */
    .bg-pattern-dots {
        background-image: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
        background-size: 20px 20px;
    }

    .bg-pattern-grid {
        background-image: linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                          linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
        background-size: 20px 20px;
    }

    /* Scrollbar Styles */
    .scrollbar-thin {
        scrollbar-width: thin;
        scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
    }

    .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
    }

    .scrollbar-thin::-webkit-scrollbar-track {
        background: transparent;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: rgba(156, 163, 175, 0.5);
        border-radius: 3px;
    }

    .scrollbar-thin::-webkit-scrollbar-thumb:hover {
        background-color: rgba(156, 163, 175, 0.8);
    }
}

/* Keyframe Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Responsive Design Helpers */
@media (max-width: 640px) {
    .mobile-hidden {
        display: none;
    }
}

@media (min-width: 641px) {
    .mobile-only {
        display: none;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .dark-mode-auto {
        @apply bg-gray-900 text-white;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .high-contrast {
        @apply border-2 border-black;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-pulse-slow,
    .animate-fade-in,
    .animate-fade-in-up,
    .animate-slide-up,
    .animate-scale-in,
    .animate-bounce-in {
        animation: none;
    }

    .hover-lift,
    .hover-scale {
        transform: none !important;
    }
}

/* Focus Styles for Accessibility */
.focus-visible:focus {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Custom Scrollbar for Webkit */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-secondary-100 hover:bg-secondary-200 text-secondary-800 font-medium py-3 px-6 rounded-lg transition-all duration-300 border border-secondary-300;
  }

  .card {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6;
  }

  .section-padding {
    @apply py-16 lg:py-24;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-800 bg-clip-text text-transparent;
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 200ms;
  }

  .animation-delay-400 {
    animation-delay: 400ms;
  }

  .animation-delay-600 {
    animation-delay: 600ms;
  }
}