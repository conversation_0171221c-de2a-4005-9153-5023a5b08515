<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Contact extends Model
{
    use HasFactory;

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'company',
        'job_title',
        'city',
        'state',
        'country',
        'postal_code',
        'timezone',
        'language',
        'status',
        'source',
        'tags',
        'engagement_score',
        'last_opened_at',
        'last_clicked_at',
        'subscribed_at',
        'unsubscribed_at',
        'bounced_at',
        'complained_at',
        'industry',
        'is_subscribed',
        'is_active',
        'last_interaction_at',
        'custom_fields',
        'notes',
    ];

    protected $casts = [
        'tags' => 'array',
        'custom_fields' => 'array',
        'is_subscribed' => 'boolean',
        'is_active' => 'boolean',
        'engagement_score' => 'integer',
        'last_interaction_at' => 'datetime',
        'last_opened_at' => 'datetime',
        'last_clicked_at' => 'datetime',
        'subscribed_at' => 'datetime',
        'unsubscribed_at' => 'datetime',
        'bounced_at' => 'datetime',
        'complained_at' => 'datetime',
    ];

    // Relationships
    public function contactLists(): BelongsToMany
    {
        return $this->belongsToMany(ContactList::class, 'contact_list_members');
    }

    public function emailValidation(): HasOne
    {
        return $this->hasOne(EmailValidation::class, 'email', 'email');
    }

    public function campaignRecipients(): HasMany
    {
        return $this->hasMany(EmailCampaignRecipient::class, 'email', 'email');
    }

    public function emailInteractions(): HasMany
    {
        return $this->hasMany(EmailInteraction::class, 'email', 'email');
    }

    // Scopes
    public function scopeSubscribed($query)
    {
        return $query->where('status', 'subscribed');
    }

    public function scopeUnsubscribed($query)
    {
        return $query->where('status', 'unsubscribed');
    }

    public function scopeActive($query)
    {
        return $query->whereIn('status', ['subscribed', 'pending']);
    }

    public function scopeHighEngagement($query, $threshold = 70)
    {
        return $query->where('engagement_score', '>=', $threshold);
    }

    public function scopeRecentlyActive($query, $days = 30)
    {
        return $query->where('last_opened_at', '>=', now()->subDays($days));
    }

    // Accessors
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    public function getInitialsAttribute(): string
    {
        $initials = '';
        if ($this->first_name) {
            $initials .= strtoupper(substr($this->first_name, 0, 1));
        }
        if ($this->last_name) {
            $initials .= strtoupper(substr($this->last_name, 0, 1));
        }
        return $initials ?: strtoupper(substr($this->email, 0, 2));
    }

    // Methods
    public function updateEngagementScore(): void
    {
        $score = 0;

        // Base score for being subscribed
        if ($this->status === 'subscribed') {
            $score += 20;
        }

        // Recent activity bonus
        if ($this->last_opened_at && $this->last_opened_at->isAfter(now()->subDays(30))) {
            $score += 30;
        }

        if ($this->last_clicked_at && $this->last_clicked_at->isAfter(now()->subDays(30))) {
            $score += 40;
        }

        // Frequency bonus
        $openCount = $this->campaignRecipients()->whereNotNull('opened_at')->count();
        $clickCount = $this->campaignRecipients()->whereNotNull('clicked_at')->count();
        $totalSent = $this->campaignRecipients()->count();

        if ($totalSent > 0) {
            $openRate = ($openCount / $totalSent) * 100;
            $clickRate = ($clickCount / $totalSent) * 100;

            $score += min($openRate * 0.3, 20); // Max 20 points for open rate
            $score += min($clickRate * 0.5, 30); // Max 30 points for click rate
        }

        $this->update(['engagement_score' => min($score, 100)]);
    }

    public function subscribe(): void
    {
        $this->update([
            'status' => 'subscribed',
            'subscribed_at' => now(),
            'unsubscribed_at' => null,
        ]);
    }

    public function unsubscribe(): void
    {
        $this->update([
            'status' => 'unsubscribed',
            'unsubscribed_at' => now(),
        ]);
    }

    public function markAsBounced(): void
    {
        $this->update([
            'status' => 'bounced',
            'bounced_at' => now(),
        ]);
    }

    public function markAsComplained(): void
    {
        $this->update([
            'status' => 'complained',
            'complained_at' => now(),
        ]);
    }

    public function addToList(ContactList $list): void
    {
        $this->contactLists()->syncWithoutDetaching([$list->id]);
    }

    public function removeFromList(ContactList $list): void
    {
        $this->contactLists()->detach($list->id);
    }
}
