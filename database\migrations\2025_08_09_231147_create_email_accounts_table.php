<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Display name for the account
            $table->string('email')->unique(); // Email address
            $table->enum('provider', ['gmail', 'outlook', 'sendgrid', 'mailgun', 'ses', 'smtp']);
            $table->string('smtp_host')->nullable();
            $table->integer('smtp_port')->nullable();
            $table->enum('encryption', ['tls', 'ssl', 'none'])->nullable();
            $table->string('username')->nullable(); // SMTP username
            $table->string('password')->nullable(); // Encrypted password
            $table->string('access_token')->nullable(); // OAuth access token
            $table->string('refresh_token')->nullable(); // OAuth refresh token
            $table->timestamp('token_expires_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_primary')->default(false); // Primary account for rotation
            $table->integer('daily_send_limit')->default(500); // Gmail limit
            $table->integer('emails_sent_today')->default(0);
            $table->timestamp('last_email_sent_at')->nullable();
            $table->timestamp('last_reset_at')->nullable(); // Daily counter reset
            $table->enum('status', ['active', 'suspended', 'rate_limited', 'error'])->default('active');
            $table->text('status_message')->nullable();
            $table->json('settings')->nullable(); // Additional provider settings
            $table->decimal('cost_per_email', 8, 4)->default(0.0001); // Cost tracking
            $table->timestamps();

            $table->index(['provider', 'is_active']);
            $table->index(['is_active', 'status']);
            $table->index('is_primary');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_accounts');
    }
};
