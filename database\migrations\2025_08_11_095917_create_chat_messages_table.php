<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_session_id')->constrained('chat_sessions')->onDelete('cascade');
            $table->enum('sender_type', ['visitor', 'agent', 'system', 'bot']); // Who sent the message
            $table->foreignId('sender_id')->nullable()->constrained('users')->onDelete('set null'); // Agent/User ID if applicable
            $table->string('sender_name')->nullable(); // Display name for the sender
            $table->enum('message_type', ['text', 'file', 'image', 'system', 'typing'])->default('text');
            $table->text('message'); // Message content
            $table->json('metadata')->nullable(); // File info, typing indicators, etc.
            $table->boolean('is_read')->default(false); // Read status
            $table->timestamp('read_at')->nullable();
            $table->boolean('is_internal')->default(false); // Internal agent notes
            $table->string('message_id')->unique(); // Unique message identifier for real-time
            $table->timestamp('sent_at')->useCurrent();
            $table->timestamps();

            // Indexes for performance
            $table->index(['chat_session_id', 'created_at']);
            $table->index(['sender_type', 'sender_id']);
            $table->index(['message_type', 'created_at']);
            $table->index(['is_read', 'chat_session_id']);
            $table->index('message_id');
            $table->index('sent_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chat_messages');
    }
};
