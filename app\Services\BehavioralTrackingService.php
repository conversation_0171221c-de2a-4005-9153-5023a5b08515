<?php

namespace App\Services;

use App\Models\AnalyticsEvent;
use App\Models\Lead;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class BehavioralTrackingService
{
    protected Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function trackEvent(array $eventData): AnalyticsEvent
    {
        $enrichedData = $this->enrichEventData($eventData);
        
        $event = AnalyticsEvent::create($enrichedData);

        // Update lead behavior if visitor is identified
        if (isset($enrichedData['user_id']) || isset($enrichedData['visitor_id'])) {
            $this->updateLeadBehavior($event);
        }

        return $event;
    }

    protected function enrichEventData(array $eventData): array
    {
        return array_merge($eventData, [
            'session_id' => $this->getSessionId(),
            'visitor_id' => $this->getVisitorId(),
            'page_url' => $this->request->url(),
            'referrer_url' => $this->request->header('referer'),
            'device_type' => $this->getDeviceType(),
            'browser' => $this->getBrowser(),
            'operating_system' => $this->getOperatingSystem(),
            'country' => $this->getCountry(),
            'city' => $this->getCity(),
            'ip_address' => $this->request->ip(),
            'utm_source' => $this->request->get('utm_source'),
            'utm_medium' => $this->request->get('utm_medium'),
            'utm_campaign' => $this->request->get('utm_campaign'),
            'utm_term' => $this->request->get('utm_term'),
            'utm_content' => $this->request->get('utm_content'),
            'event_timestamp' => now(),
        ]);
    }

    protected function getSessionId(): string
    {
        return session()->getId();
    }

    protected function getVisitorId(): string
    {
        // Try to get from cookie, otherwise generate new one
        $visitorId = $this->request->cookie('visitor_id');
        
        if (!$visitorId) {
            $visitorId = Str::uuid()->toString();
            cookie()->queue('visitor_id', $visitorId, 60 * 24 * 365); // 1 year
        }

        return $visitorId;
    }

    protected function getDeviceType(): string
    {
        $userAgent = $this->request->userAgent();
        
        if (preg_match('/mobile|android|iphone|ipad/i', $userAgent)) {
            return 'mobile';
        } elseif (preg_match('/tablet|ipad/i', $userAgent)) {
            return 'tablet';
        }
        
        return 'desktop';
    }

    protected function getBrowser(): string
    {
        $userAgent = $this->request->userAgent();
        
        if (strpos($userAgent, 'Chrome') !== false) {
            return 'Chrome';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            return 'Firefox';
        } elseif (strpos($userAgent, 'Safari') !== false) {
            return 'Safari';
        } elseif (strpos($userAgent, 'Edge') !== false) {
            return 'Edge';
        }
        
        return 'Unknown';
    }

    protected function getOperatingSystem(): string
    {
        $userAgent = $this->request->userAgent();
        
        if (strpos($userAgent, 'Windows') !== false) {
            return 'Windows';
        } elseif (strpos($userAgent, 'Mac') !== false) {
            return 'macOS';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            return 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            return 'Android';
        } elseif (strpos($userAgent, 'iOS') !== false) {
            return 'iOS';
        }
        
        return 'Unknown';
    }

    protected function getCountry(): ?string
    {
        // In production, you would use a GeoIP service
        return 'India'; // Default for Bhavitech
    }

    protected function getCity(): ?string
    {
        // In production, you would use a GeoIP service
        return 'Salem'; // Default for Bhavitech
    }

    protected function updateLeadBehavior(AnalyticsEvent $event): void
    {
        $lead = $this->findLeadByEvent($event);
        
        if ($lead) {
            $this->updateLeadEngagement($lead, $event);
        }
    }

    protected function findLeadByEvent(AnalyticsEvent $event): ?Lead
    {
        // Try to find lead by user_id first
        if ($event->user_id) {
            return Lead::find($event->user_id);
        }

        // Try to find by email in visitor_id (if email was used as visitor identifier)
        if ($event->visitor_id && filter_var($event->visitor_id, FILTER_VALIDATE_EMAIL)) {
            return Lead::where('email', $event->visitor_id)->first();
        }

        // Try to find by session data or other identifiers
        return null;
    }

    protected function updateLeadEngagement(Lead $lead, AnalyticsEvent $event): void
    {
        // Update lead's last activity
        $lead->touch();

        // Create interaction record
        $lead->interactions()->create([
            'type' => 'website_activity',
            'channel' => 'website',
            'description' => "Performed {$event->event_name} on {$event->page_url}",
            'metadata' => [
                'event_name' => $event->event_name,
                'page_url' => $event->page_url,
                'event_category' => $event->event_category,
                'event_action' => $event->event_action,
            ],
            'interaction_date' => $event->event_timestamp,
            'is_automated' => true,
        ]);

        // Trigger lead score recalculation
        app(LeadScoringService::class)->updateLeadScore($lead);
    }

    public function trackPageView(string $pageTitle = null): AnalyticsEvent
    {
        return $this->trackEvent([
            'event_name' => 'page_view',
            'event_category' => 'engagement',
            'event_action' => 'view',
            'page_title' => $pageTitle ?: $this->request->get('title', 'Unknown'),
        ]);
    }

    public function trackFormSubmission(string $formType, array $formData = []): AnalyticsEvent
    {
        return $this->trackEvent([
            'event_name' => 'form_submit',
            'event_category' => 'conversion',
            'event_action' => 'submit',
            'event_label' => $formType,
            'custom_properties' => [
                'form_type' => $formType,
                'form_data' => $formData,
            ],
        ]);
    }

    public function trackButtonClick(string $buttonName, string $buttonLocation = null): AnalyticsEvent
    {
        return $this->trackEvent([
            'event_name' => 'button_click',
            'event_category' => 'engagement',
            'event_action' => 'click',
            'event_label' => $buttonName,
            'custom_properties' => [
                'button_name' => $buttonName,
                'button_location' => $buttonLocation,
            ],
        ]);
    }

    public function trackDownload(string $fileName, string $fileType = null): AnalyticsEvent
    {
        return $this->trackEvent([
            'event_name' => 'file_download',
            'event_category' => 'engagement',
            'event_action' => 'download',
            'event_label' => $fileName,
            'custom_properties' => [
                'file_name' => $fileName,
                'file_type' => $fileType,
            ],
        ]);
    }

    public function trackEmailOpen(string $campaignId, string $emailId): AnalyticsEvent
    {
        return $this->trackEvent([
            'event_name' => 'email_open',
            'event_category' => 'email',
            'event_action' => 'open',
            'event_label' => $campaignId,
            'custom_properties' => [
                'campaign_id' => $campaignId,
                'email_id' => $emailId,
            ],
        ]);
    }

    public function trackEmailClick(string $campaignId, string $emailId, string $linkUrl): AnalyticsEvent
    {
        return $this->trackEvent([
            'event_name' => 'email_click',
            'event_category' => 'email',
            'event_action' => 'click',
            'event_label' => $campaignId,
            'custom_properties' => [
                'campaign_id' => $campaignId,
                'email_id' => $emailId,
                'link_url' => $linkUrl,
            ],
        ]);
    }

    public function getVisitorJourney(string $visitorId): \Illuminate\Support\Collection
    {
        return AnalyticsEvent::where('visitor_id', $visitorId)
            ->orderBy('event_timestamp')
            ->get();
    }

    public function getLeadJourney(Lead $lead): \Illuminate\Support\Collection
    {
        return AnalyticsEvent::where(function ($query) use ($lead) {
            $query->where('user_id', $lead->id)
                  ->orWhere('visitor_id', $lead->email);
        })
        ->orderBy('event_timestamp')
        ->get();
    }
}
