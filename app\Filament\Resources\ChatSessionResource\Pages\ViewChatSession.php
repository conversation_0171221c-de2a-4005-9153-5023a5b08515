<?php

namespace App\Filament\Resources\ChatSessionResource\Pages;

use App\Filament\Resources\ChatSessionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Grid;

class ViewChatSession extends ViewRecord
{
    protected static string $resource = ChatSessionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\Action::make('assign_agent')
                ->label('Assign Agent')
                ->icon('heroicon-o-user-plus')
                ->form([
                    \Filament\Forms\Components\Select::make('agent_id')
                        ->label('Select Agent')
                        ->options(\App\Models\User::all()->pluck('name', 'id'))
                        ->required(),
                ])
                ->action(function (array $data): void {
                    $agent = \App\Models\User::find($data['agent_id']);
                    $this->record->assignToAgent($agent);
                    $this->refreshFormData(['agent_id']);
                })
                ->visible(fn (): bool => !$this->record->agent_id && !$this->record->isClosed()),
            Actions\Action::make('close_session')
                ->label('Close Session')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->requiresConfirmation()
                ->form([
                    \Filament\Forms\Components\Textarea::make('closing_reason')
                        ->label('Closing Reason')
                        ->required(),
                ])
                ->action(function (array $data): void {
                    $this->record->closeSession($data['closing_reason']);
                    $this->refreshFormData(['status', 'closed_at', 'closing_reason']);
                })
                ->visible(fn (): bool => !$this->record->isClosed()),
        ];
    }
}
