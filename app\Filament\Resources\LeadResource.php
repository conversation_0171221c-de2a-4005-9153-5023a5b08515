<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LeadResource\Pages;
use App\Filament\Resources\LeadResource\RelationManagers;
use App\Models\Lead;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\DateTimePicker;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\BulkAction;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;

class LeadResource extends Resource
{
    protected static ?string $model = Lead::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationGroup = 'Lead Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Lead Information')
                    ->schema([
                        TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        TextInput::make('email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        TextInput::make('phone')
                            ->tel()
                            ->maxLength(255),
                        TextInput::make('company')
                            ->maxLength(255),
                    ])->columns(2),

                Forms\Components\Section::make('Lead Details')
                    ->schema([
                        Select::make('source')
                            ->options([
                                'website' => 'Website',
                                'social' => 'Social Media',
                                'referral' => 'Referral',
                                'email' => 'Email Campaign',
                                'phone' => 'Phone Call',
                                'other' => 'Other',
                            ])
                            ->default('website')
                            ->required(),
                        Select::make('status')
                            ->options([
                                'new' => 'New',
                                'contacted' => 'Contacted',
                                'qualified' => 'Qualified',
                                'converted' => 'Converted',
                                'lost' => 'Lost',
                            ])
                            ->default('new')
                            ->required(),
                        Select::make('service_interest')
                            ->options([
                                'web-development' => 'Web Development',
                                'mobile-development' => 'Mobile Development',
                                'digital-marketing' => 'Digital Marketing',
                                'graphic-design' => 'Graphic Design',
                                'other' => 'Other',
                            ]),
                        Select::make('assigned_to')
                            ->relationship('assignedTo', 'name')
                            ->searchable()
                            ->preload(),
                    ])->columns(2),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Textarea::make('message')
                            ->rows(3)
                            ->columnSpanFull(),
                        TextInput::make('score')
                            ->numeric()
                            ->default(0)
                            ->minValue(0)
                            ->maxValue(100),
                        DateTimePicker::make('last_contacted_at'),
                        DateTimePicker::make('qualified_at'),
                        DateTimePicker::make('converted_at'),
                    ])->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                TextColumn::make('phone')
                    ->searchable()
                    ->toggleable(),
                TextColumn::make('company')
                    ->searchable()
                    ->toggleable(),
                BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'new',
                        'warning' => 'contacted',
                        'primary' => 'qualified',
                        'success' => 'converted',
                        'danger' => 'lost',
                    ]),
                TextColumn::make('score')
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match (true) {
                        $state >= 80 => 'success',
                        $state >= 60 => 'warning',
                        default => 'secondary',
                    }),
                BadgeColumn::make('source')
                    ->colors([
                        'primary' => 'website',
                        'success' => 'referral',
                        'warning' => 'social',
                        'secondary' => 'email',
                    ]),
                TextColumn::make('service_interest')
                    ->label('Service')
                    ->toggleable(),
                TextColumn::make('assignedTo.name')
                    ->label('Assigned To')
                    ->toggleable(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
                TextColumn::make('last_contacted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'new' => 'New',
                        'contacted' => 'Contacted',
                        'qualified' => 'Qualified',
                        'converted' => 'Converted',
                        'lost' => 'Lost',
                    ]),
                SelectFilter::make('source')
                    ->options([
                        'website' => 'Website',
                        'social' => 'Social Media',
                        'referral' => 'Referral',
                        'email' => 'Email Campaign',
                        'phone' => 'Phone Call',
                        'other' => 'Other',
                    ]),
                SelectFilter::make('service_interest')
                    ->label('Service Interest')
                    ->options([
                        'web-development' => 'Web Development',
                        'mobile-development' => 'Mobile Development',
                        'digital-marketing' => 'Digital Marketing',
                        'graphic-design' => 'Graphic Design',
                        'other' => 'Other',
                    ]),
                Filter::make('high_score')
                    ->query(fn (Builder $query): Builder => $query->where('score', '>=', 70))
                    ->label('High Score (70+)'),
                Filter::make('recent_leads')
                    ->query(fn (Builder $query): Builder => $query->where('created_at', '>=', now()->subDays(7)))
                    ->label('Recent Leads (7 days)'),
                Filter::make('unassigned')
                    ->query(fn (Builder $query): Builder => $query->whereNull('assigned_to'))
                    ->label('Unassigned'),
                Filter::make('needs_follow_up')
                    ->query(fn (Builder $query): Builder =>
                        $query->where('status', 'contacted')
                              ->where('last_contacted_at', '<=', now()->subDays(3))
                    )
                    ->label('Needs Follow-up'),
                SelectFilter::make('assigned_to')
                    ->label('Assigned To')
                    ->relationship('assignedTo', 'name')
                    ->preload(),
                SelectFilter::make('grade')
                    ->options([
                        'A' => 'Grade A (Hot)',
                        'B' => 'Grade B (Warm)',
                        'C' => 'Grade C (Cold)',
                        'D' => 'Grade D (Unqualified)',
                    ]),
                SelectFilter::make('assigned_to')
                    ->relationship('assignedTo', 'name')
                    ->label('Assigned To'),
            ])
            ->actions([
                Action::make('updateScore')
                    ->label('Update Score')
                    ->icon('heroicon-o-calculator')
                    ->action(function (Lead $record) {
                        $record->updateScore();
                        \Filament\Notifications\Notification::make()
                            ->title('Score updated successfully')
                            ->success()
                            ->send();
                    }),
                Action::make('markContacted')
                    ->label('Mark Contacted')
                    ->icon('heroicon-o-phone')
                    ->action(function (Lead $record) {
                        $record->markAsContacted();
                        \Filament\Notifications\Notification::make()
                            ->title('Lead marked as contacted')
                            ->success()
                            ->send();
                    })
                    ->visible(fn (Lead $record) => $record->status === 'new'),
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('updateScores')
                        ->label('Update Scores')
                        ->icon('heroicon-o-calculator')
                        ->action(function (Collection $records): void {
                            foreach ($records as $record) {
                                $record->updateScore();
                            }
                            Notification::make()
                                ->title('Scores updated successfully')
                                ->success()
                                ->send();
                        }),
                    BulkAction::make('assignTo')
                        ->label('Assign To')
                        ->icon('heroicon-o-user')
                        ->form([
                            Select::make('assigned_to')
                                ->label('Assign to User')
                                ->options(User::where('role', 'admin')->pluck('name', 'id'))
                                ->required(),
                        ])
                        ->action(function (Collection $records, array $data): void {
                            $records->each->update(['assigned_to' => $data['assigned_to']]);
                            Notification::make()
                                ->title('Leads assigned successfully')
                                ->success()
                                ->send();
                        }),
                    BulkAction::make('updateStatus')
                        ->label('Update Status')
                        ->icon('heroicon-o-flag')
                        ->form([
                            Select::make('status')
                                ->options([
                                    'new' => 'New',
                                    'contacted' => 'Contacted',
                                    'qualified' => 'Qualified',
                                    'proposal_sent' => 'Proposal Sent',
                                    'converted' => 'Converted',
                                    'lost' => 'Lost',
                                ])
                                ->required(),
                        ])
                        ->action(function (Collection $records, array $data): void {
                            $records->each->update(['status' => $data['status']]);
                            Notification::make()
                                ->title('Lead status updated successfully')
                                ->success()
                                ->send();
                        }),
                    BulkAction::make('addToEmailCampaign')
                        ->label('Add to Email Campaign')
                        ->icon('heroicon-o-envelope')
                        ->color('primary')
                        ->action(function (Collection $records): void {
                            // This would redirect to create email campaign with selected leads
                            Notification::make()
                                ->title('Redirecting to email campaign creation...')
                                ->info()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Lead Information')
                    ->schema([
                        Infolists\Components\TextEntry::make('name')
                            ->weight('bold'),
                        Infolists\Components\TextEntry::make('email')
                            ->copyable()
                            ->icon('heroicon-m-envelope'),
                        Infolists\Components\TextEntry::make('phone')
                            ->copyable()
                            ->icon('heroicon-m-phone'),
                        Infolists\Components\TextEntry::make('company'),
                        Infolists\Components\TextEntry::make('status')
                            ->badge(),
                        Infolists\Components\TextEntry::make('score')
                            ->badge()
                            ->color(fn (string $state): string => match (true) {
                                $state >= 80 => 'success',
                                $state >= 60 => 'warning',
                                default => 'secondary',
                            }),
                    ])->columns(3),

                Infolists\Components\Section::make('Lead Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('source')
                            ->badge(),
                        Infolists\Components\TextEntry::make('service_interest')
                            ->label('Service Interest'),
                        Infolists\Components\TextEntry::make('grade')
                            ->badge(),
                        Infolists\Components\TextEntry::make('priority')
                            ->badge(),
                        Infolists\Components\TextEntry::make('assignedTo.name')
                            ->label('Assigned To'),
                        Infolists\Components\TextEntry::make('city'),
                    ])->columns(3),

                Infolists\Components\Section::make('Message & Notes')
                    ->schema([
                        Infolists\Components\TextEntry::make('message')
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Timeline')
                    ->schema([
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Lead Created')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('last_contacted_at')
                            ->label('Last Contacted')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('qualified_at')
                            ->label('Qualified At')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('converted_at')
                            ->label('Converted At')
                            ->dateTime(),
                    ])->columns(2),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLeads::route('/'),
            'create' => Pages\CreateLead::route('/create'),
            'view' => Pages\ViewLead::route('/{record}'),
            'edit' => Pages\EditLead::route('/{record}/edit'),
        ];
    }
}
