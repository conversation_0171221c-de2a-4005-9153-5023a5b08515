<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Contact;
use App\Models\ContactList;
use App\Models\EmailAccount;
use App\Models\EmailTemplate;
use App\Models\EmailCampaign;
use App\Models\User;

class QuickTestSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('Creating quick test data for admin panel...');

        $admin = User::where('role', 'admin')->first();

        // Create email accounts
        EmailAccount::firstOrCreate(['email' => '<EMAIL>'], [
            'name' => 'Primary Marketing',
            'email' => '<EMAIL>',
            'provider' => 'gmail',
            'smtp_host' => 'smtp.gmail.com',
            'smtp_port' => 587,
            'username' => '<EMAIL>',
            'password' => encrypt('test_password'),
            'daily_send_limit' => 500,
            'emails_sent_today' => 245,
            'is_active' => true,
            'is_primary' => true,
        ]);

        EmailAccount::firstOrCreate(['email' => '<EMAIL>'], [
            'name' => 'Sales Account',
            'email' => '<EMAIL>',
            'provider' => 'outlook',
            'smtp_host' => 'smtp-mail.outlook.com',
            'smtp_port' => 587,
            'username' => '<EMAIL>',
            'password' => encrypt('test_password'),
            'daily_send_limit' => 300,
            'emails_sent_today' => 89,
            'is_active' => true,
            'is_primary' => false,
        ]);

        // Create contacts
        $contacts = [
            ['first_name' => 'John', 'last_name' => 'Doe', 'email' => '<EMAIL>', 'company' => 'Tech Corp', 'industry' => 'Technology'],
            ['first_name' => 'Jane', 'last_name' => 'Smith', 'email' => '<EMAIL>', 'company' => 'Design Studio', 'industry' => 'Design'],
            ['first_name' => 'Mike', 'last_name' => 'Johnson', 'email' => '<EMAIL>', 'company' => 'Marketing Inc', 'industry' => 'Marketing'],
            ['first_name' => 'Sarah', 'last_name' => 'Wilson', 'email' => '<EMAIL>', 'company' => 'Finance Ltd', 'industry' => 'Finance'],
            ['first_name' => 'David', 'last_name' => 'Brown', 'email' => '<EMAIL>', 'company' => 'Healthcare Co', 'industry' => 'Healthcare'],
        ];

        foreach ($contacts as $contactData) {
            Contact::firstOrCreate(['email' => $contactData['email']], array_merge($contactData, [
                'phone' => '+91' . rand(**********, **********),
                'city' => 'Chennai',
                'state' => 'Tamil Nadu',
                'country' => 'India',
                'engagement_score' => rand(50, 100),
                'is_subscribed' => true,
                'is_active' => true,
            ]));
        }

        // Create contact lists
        $newsletterList = ContactList::create([
            'name' => 'Newsletter Subscribers',
            'description' => 'All newsletter subscribers',
            'type' => 'static',
            'status' => 'active',
            'created_by' => $admin->id,
        ]);

        $highValueList = ContactList::create([
            'name' => 'High Value Prospects',
            'description' => 'High engagement score contacts',
            'type' => 'dynamic',
            'status' => 'active',
            'segment_rules' => json_encode([
                ['field' => 'engagement_score', 'operator' => '>', 'value' => '80']
            ]),
            'created_by' => $admin->id,
        ]);

        // Add contacts to newsletter list
        $allContacts = Contact::all();
        foreach ($allContacts as $contact) {
            $newsletterList->addContact($contact);
        }

        // Create email templates
        EmailTemplate::create([
            'name' => 'Welcome Email',
            'subject' => 'Welcome to Bhavitech!',
            'content' => '<h1>Welcome {{first_name}}!</h1><p>Thank you for joining us.</p>',
            'type' => 'welcome',
            'category' => 'customer_onboarding',
            'variables' => json_encode(['first_name', 'last_name']),
            'created_by' => $admin->id,
        ]);

        EmailTemplate::create([
            'name' => 'Newsletter Template',
            'subject' => 'Monthly Newsletter',
            'content' => '<h1>Newsletter</h1><p>Hi {{first_name}}, here are our updates...</p>',
            'type' => 'newsletter',
            'category' => 'marketing',
            'variables' => json_encode(['first_name']),
            'created_by' => $admin->id,
        ]);

        // Create email campaigns
        EmailCampaign::create([
            'name' => 'Welcome Campaign',
            'subject' => 'Welcome to Bhavitech',
            'content' => '<h1>Welcome!</h1><p>Thank you for joining us.</p>',
            'type' => 'welcome',
            'status' => 'sent',
            'sender_name' => 'Bhavitech Team',
            'sender_email' => '<EMAIL>',
            'recipients' => json_encode([['type' => 'list', 'id' => $newsletterList->id]]),
            'sent_at' => now()->subDays(2),
            'created_by' => $admin->id,
        ]);

        EmailCampaign::create([
            'name' => 'Monthly Newsletter',
            'subject' => 'December Newsletter',
            'content' => '<h1>Newsletter</h1><p>Here are our updates...</p>',
            'type' => 'newsletter',
            'status' => 'scheduled',
            'sender_name' => 'Bhavitech Team',
            'sender_email' => '<EMAIL>',
            'recipients' => json_encode([['type' => 'list', 'id' => $newsletterList->id]]),
            'scheduled_at' => now()->addDays(1),
            'created_by' => $admin->id,
        ]);

        $this->command->info('✅ Quick test data created successfully!');
        $this->command->info('📧 Email Accounts: 2');
        $this->command->info('👥 Contacts: 5');
        $this->command->info('📋 Contact Lists: 2');
        $this->command->info('📝 Email Templates: 2');
        $this->command->info('📨 Email Campaigns: 2');
    }
}
