<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ChatSessionResource\Pages;
use App\Filament\Resources\ChatSessionResource\RelationManagers;
use App\Models\ChatSession;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Support\Enums\FontWeight;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Actions\Action;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChatSessionResource extends Resource
{
    protected static ?string $model = ChatSession::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?string $navigationLabel = 'Live Chat';

    protected static ?string $modelLabel = 'Chat Session';

    protected static ?string $pluralModelLabel = 'Chat Sessions';

    protected static ?string $navigationGroup = 'Customer Support';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Session Information')
                    ->schema([
                        Forms\Components\TextInput::make('session_id')
                            ->label('Session ID')
                            ->disabled(),
                        Forms\Components\TextInput::make('visitor_name')
                            ->label('Visitor Name'),
                        Forms\Components\TextInput::make('visitor_email')
                            ->label('Visitor Email')
                            ->email(),
                        Forms\Components\TextInput::make('visitor_phone')
                            ->label('Visitor Phone'),
                        Forms\Components\Select::make('status')
                            ->options([
                                'active' => 'Active',
                                'waiting' => 'Waiting',
                                'assigned' => 'Assigned',
                                'closed' => 'Closed',
                                'abandoned' => 'Abandoned',
                            ])
                            ->required(),
                        Forms\Components\Select::make('agent_id')
                            ->label('Assigned Agent')
                            ->options(User::all()->pluck('name', 'id'))
                            ->searchable(),
                    ])->columns(2),

                Forms\Components\Section::make('Session Details')
                    ->schema([
                        Forms\Components\Textarea::make('initial_message')
                            ->label('Initial Message')
                            ->rows(3),
                        Forms\Components\TextInput::make('source_page')
                            ->label('Source Page'),
                        Forms\Components\Textarea::make('closing_reason')
                            ->label('Closing Reason')
                            ->rows(2),
                        Forms\Components\Textarea::make('feedback')
                            ->label('Customer Feedback')
                            ->rows(3),
                        Forms\Components\TextInput::make('satisfaction_rating')
                            ->label('Satisfaction Rating')
                            ->numeric()
                            ->minValue(1)
                            ->maxValue(5),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('session_id')
                    ->label('Session ID')
                    ->searchable()
                    ->copyable()
                    ->limit(20),
                TextColumn::make('visitor_name')
                    ->label('Visitor')
                    ->searchable()
                    ->default('Anonymous'),
                TextColumn::make('visitor_email')
                    ->label('Email')
                    ->searchable()
                    ->toggleable(),
                BadgeColumn::make('status')
                    ->colors([
                        'success' => 'active',
                        'warning' => 'waiting',
                        'primary' => 'assigned',
                        'secondary' => 'closed',
                        'danger' => 'abandoned',
                    ]),
                TextColumn::make('agent.name')
                    ->label('Agent')
                    ->default('Unassigned')
                    ->toggleable(),
                TextColumn::make('message_count')
                    ->label('Messages')
                    ->alignCenter(),
                TextColumn::make('started_at')
                    ->label('Started')
                    ->dateTime()
                    ->sortable(),
                TextColumn::make('last_activity_at')
                    ->label('Last Activity')
                    ->since()
                    ->sortable(),
                TextColumn::make('satisfaction_rating')
                    ->label('Rating')
                    ->formatStateUsing(fn ($state) => $state ? "⭐ {$state}/5" : '-')
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'waiting' => 'Waiting',
                        'assigned' => 'Assigned',
                        'closed' => 'Closed',
                        'abandoned' => 'Abandoned',
                    ]),
                SelectFilter::make('agent_id')
                    ->label('Agent')
                    ->options(User::all()->pluck('name', 'id'))
                    ->searchable(),
            ])
            ->actions([
                Action::make('view_chat')
                    ->label('View Chat')
                    ->icon('heroicon-o-eye')
                    ->url(fn (ChatSession $record): string => route('filament.admin.resources.chat-sessions.view', $record)),
                Action::make('assign_agent')
                    ->label('Assign Agent')
                    ->icon('heroicon-o-user-plus')
                    ->form([
                        Forms\Components\Select::make('agent_id')
                            ->label('Select Agent')
                            ->options(User::all()->pluck('name', 'id'))
                            ->required(),
                    ])
                    ->action(function (ChatSession $record, array $data): void {
                        $agent = User::find($data['agent_id']);
                        $record->assignToAgent($agent);
                    })
                    ->visible(fn (ChatSession $record): bool => !$record->agent_id && !$record->isClosed()),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('last_activity_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\MessagesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListChatSessions::route('/'),
            'create' => Pages\CreateChatSession::route('/create'),
            'view' => Pages\ViewChatSession::route('/{record}'),
            'edit' => Pages\EditChatSession::route('/{record}/edit'),
        ];
    }
}
