@extends('layouts.app')

@section('title', 'Contact Bhavitech - Get In Touch for Digital Solutions')
@section('description', 'Contact Bhavitech for web development, mobile apps, digital marketing services. Located in Salem, Tamil Nadu. Call +91 7010860889 <NAME_EMAIL>')

@section('content')
<!-- Hero Section -->
<section class="relative py-20 lg:py-32 gradient-bg overflow-hidden">
    <div class="absolute inset-0">
        <div class="absolute inset-0 bg-black opacity-40"></div>
        <div class="absolute top-20 right-10 w-72 h-72 bg-green-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow"></div>
        <div class="absolute bottom-20 left-10 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow animation-delay-200"></div>
    </div>
    
    <div class="relative z-10 container-custom">
        <div class="max-w-4xl mx-auto text-center text-white">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">
                Get In <span class="text-gradient bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">Touch</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-gray-200 animate-slide-up animation-delay-200">
                Ready to transform your business? Let's discuss your project and create something amazing together
            </p>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="grid lg:grid-cols-2 gap-12 items-start">
            <!-- Contact Form -->
            <div>
                <h2 class="text-3xl md:text-4xl font-bold mb-6">Send Us a Message</h2>
                <p class="text-gray-600 mb-8">
                    Fill out the form below and we'll get back to you within 24 hours. Let's discuss how we can help grow your business.
                </p>

                <div x-data="enhancedContactForm" class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                    <form @submit.prevent="submitForm" class="space-y-8">
                        @csrf
                        <div class="grid md:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <label for="name" class="block text-sm font-semibold text-gray-800 mb-2">Full Name *</label>
                                <input type="text" id="name" x-model="formData.name" required
                                       :class="{'border-red-300 bg-red-50': errors.name, 'border-green-300 bg-green-50': formData.name && !errors.name}"
                                       class="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white">
                                <p x-show="errors.name" x-text="errors.name" class="mt-2 text-sm text-red-600 font-medium"></p>
                            </div>
                            <div class="space-y-2">
                                <label for="email" class="block text-sm font-semibold text-gray-800 mb-2">Email Address *</label>
                                <input type="email" id="email" x-model="formData.email" required
                                       :class="{'border-red-300 bg-red-50': errors.email, 'border-green-300 bg-green-50': formData.email && !errors.email && isValidEmail(formData.email)}"
                                       class="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white">
                                <p x-show="errors.email" x-text="errors.email" class="mt-2 text-sm text-red-600 font-medium"></p>
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <label for="phone" class="block text-sm font-semibold text-gray-800 mb-2">Phone Number</label>
                                <input type="tel" id="phone" x-model="formData.phone"
                                       placeholder="+91 98765 43210"
                                       :class="{'border-red-300 bg-red-50': errors.phone, 'border-green-300 bg-green-50': formData.phone && !errors.phone}"
                                       class="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white">
                                <p x-show="errors.phone" x-text="errors.phone" class="mt-2 text-sm text-red-600 font-medium"></p>
                            </div>
                            <div class="space-y-2">
                                <label for="company" class="block text-sm font-semibold text-gray-800 mb-2">Company Name</label>
                                <input type="text" id="company" x-model="formData.company"
                                       placeholder="Your Company Name"
                                       class="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white">
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <label for="service" class="block text-sm font-semibold text-gray-800 mb-2">Service Interested In *</label>
                                <select id="service" x-model="formData.service_interest" required
                                        :class="{'border-red-300 bg-red-50': errors.service_interest}"
                                        class="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white">
                                    <option value="">Select a service</option>
                                <option value="web_development">Web Development</option>
                                <option value="mobile_development">Mobile App Development</option>
                                <option value="digital_marketing">Digital Marketing</option>
                                <option value="ai_crm">AI-Powered CRM</option>
                                <option value="business_intelligence">Business Intelligence</option>
                                <option value="graphic_design">Graphic Design</option>
                                <option value="consultation">General Consultation</option>
                                <option value="other">Other Services</option>
                            </select>
                            <p x-show="errors.service_interest" x-text="errors.service_interest" class="mt-1 text-sm text-red-600"></p>
                        </div>
                            <div class="space-y-2">
                                <label for="budget" class="block text-sm font-semibold text-gray-800 mb-2">Budget Range</label>
                                <select id="budget" x-model="formData.budget"
                                        class="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white">
                                    <option value="">Select budget range</option>
                                    <option value="under_50k">Under ₹50,000</option>
                                    <option value="50k_1l">₹50,000 - ₹1,00,000</option>
                                    <option value="1l_3l">₹1,00,000 - ₹3,00,000</option>
                                    <option value="3l_5l">₹3,00,000 - ₹5,00,000</option>
                                    <option value="above_5l">Above ₹5,00,000</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid md:grid-cols-2 gap-6">
                            <div class="space-y-2">
                                <label for="timeline" class="block text-sm font-semibold text-gray-800 mb-2">Project Timeline</label>
                                <select id="timeline" x-model="formData.timeline"
                                        class="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white">
                                    <option value="">Select timeline</option>
                                    <option value="asap">ASAP</option>
                                    <option value="1_month">Within 1 month</option>
                                    <option value="3_months">Within 3 months</option>
                                    <option value="6_months">Within 6 months</option>
                                    <option value="flexible">Flexible</option>
                                </select>
                            </div>
                            <div class="space-y-2">
                                <!-- Empty div for grid alignment -->
                            </div>
                        </div>

                        <div class="space-y-2">
                            <label for="message" class="block text-sm font-semibold text-gray-800 mb-2">Project Details *</label>
                            <textarea id="message" x-model="formData.message" rows="6" required
                                      :class="{'border-red-300 bg-red-50': errors.message, 'border-green-300 bg-green-50': formData.message && !errors.message}"
                                      class="w-full px-4 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 bg-gray-50 hover:bg-white resize-none"
                                      placeholder="Tell us about your project requirements, goals, and any specific features you need. The more details you provide, the better we can help you!"></textarea>
                            <p x-show="errors.message" x-text="errors.message" class="mt-2 text-sm text-red-600 font-medium"></p>
                            <p class="mt-2 text-sm text-gray-500 text-right" x-text="`${formData.message.length}/2000 characters`"></p>
                        </div>

                        <!-- Form Actions -->
                        <div class="flex flex-col sm:flex-row gap-4 pt-4">
                            <button type="submit"
                                    :disabled="loading || !isFormValid"
                                    :class="loading || !isFormValid ? 'bg-gray-400 cursor-not-allowed' : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 shadow-lg hover:shadow-xl'"
                                    class="flex-1 inline-flex items-center justify-center px-8 py-4 border border-transparent text-base font-semibold rounded-xl text-white transition-all duration-300">
                                <span x-show="!loading" class="flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                    Send Message
                                </span>
                                <span x-show="loading" class="flex items-center">
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Sending Message...
                                </span>
                            </button>

                            <button type="button" @click="resetForm"
                                    class="inline-flex items-center justify-center px-6 py-4 border border-gray-300 text-base font-medium rounded-xl text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 hover:shadow-md">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Reset Form
                            </button>
                        </div>
                    </form>

                    <!-- Success Message -->
                    <div x-show="success" x-transition:enter="transition ease-out duration-300"
                         x-transition:enter-start="opacity-0 transform scale-90"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         x-transition:leave="transition ease-in duration-200"
                         x-transition:leave-start="opacity-100 transform scale-100"
                         x-transition:leave-end="opacity-0 transform scale-90"
                         style="display: none;"
                         class="mt-6 p-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl shadow-sm">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="h-5 w-5 text-green-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-green-800">Message Sent Successfully!</h3>
                                <p class="mt-1 text-sm text-green-700">
                                    Thank you for reaching out to us. Our team will review your inquiry and get back to you within 24 hours.
                                    We're excited to discuss your project!
                                </p>
                                <div class="mt-3 text-sm text-green-600">
                                    <p>📧 You'll receive a confirmation email shortly</p>
                                    <p>⏱️ Expected response time: 2-24 hours</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div x-show="error" x-transition:enter="transition ease-out duration-300"
                         x-transition:enter-start="opacity-0 transform scale-90"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         x-transition:leave="transition ease-in duration-200"
                         x-transition:leave-start="opacity-100 transform scale-100"
                         x-transition:leave-end="opacity-0 transform scale-90"
                         style="display: none;"
                         class="mt-6 p-6 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl shadow-sm">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                    <svg class="h-5 w-5 text-red-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-lg font-semibold text-red-800">Oops! Something went wrong</h3>
                                <p class="mt-1 text-sm text-red-700" x-text="errorMessage || 'Sorry, there was an error sending your message. Please try again or contact us directly.'"></p>
                                <div class="mt-3">
                                    <button @click="error = false; resetForm()"
                                            class="text-sm text-red-600 hover:text-red-800 font-medium underline">
                                        Try Again
                                    </button>
                                    <span class="mx-2 text-red-400">|</span>
                                    <a href="mailto:<EMAIL>" class="text-sm text-red-600 hover:text-red-800 font-medium underline">
                                        Email Us Directly
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="space-y-8 lg:sticky lg:top-8">
                <div>
                    <h2 class="text-3xl md:text-4xl font-bold mb-6 text-gray-900">Contact Information</h2>
                    <p class="text-gray-600 mb-8 text-lg leading-relaxed">
                        Get in touch with us through any of the following methods. We're here to help you succeed.
                    </p>
                </div>

                <div class="space-y-6">
                    <!-- Office Address -->
                    <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                <svg class="w-7 h-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold mb-2 text-gray-900">Office Address</h3>
                                <p class="text-gray-600 leading-relaxed">
                                    Convent Road, Fairlands<br>
                                    Salem - 636016<br>
                                    Tamil Nadu, India
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Phone Numbers -->
                    <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-green-100 to-emerald-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                <svg class="w-7 h-7 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold mb-2 text-gray-900">Phone Numbers</h3>
                                <div class="space-y-1">
                                    <p><a href="tel:+917010860889" class="text-gray-600 hover:text-blue-600 transition-colors font-medium">+91 7010860889</a></p>
                                    <p><a href="tel:+919629037527" class="text-gray-600 hover:text-blue-600 transition-colors font-medium">+91 9629037527</a></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email -->
                    <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-purple-100 to-pink-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                <svg class="w-7 h-7 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold mb-2 text-gray-900">Email Address</h3>
                                <p>
                                    <a href="mailto:<EMAIL>" class="text-gray-600 hover:text-blue-600 transition-colors font-medium"><EMAIL></a>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Business Hours -->
                    <div class="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
                        <div class="flex items-start space-x-4">
                            <div class="w-14 h-14 bg-gradient-to-br from-orange-100 to-red-100 rounded-xl flex items-center justify-center flex-shrink-0">
                                <svg class="w-7 h-7 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold mb-2 text-gray-900">Business Hours</h3>
                                <div class="space-y-1 text-gray-600">
                                    <p><span class="font-medium">Monday - Friday:</span> 9:00 AM - 6:00 PM</p>
                                    <p><span class="font-medium">Saturday:</span> 9:00 AM - 2:00 PM</p>
                                    <p><span class="font-medium">Sunday:</span> Closed</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Contact Buttons -->
                <div class="mt-10 space-y-4">
                    <a href="tel:+917010860889"
                       class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center space-x-3">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <span class="text-lg">📞 Call Now</span>
                    </a>

                    <a href="https://wa.me/917010860889?text=Hi%20Bhavitech,%20I'm%20interested%20in%20your%20services"
                       target="_blank"
                       class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center space-x-3">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        <span class="text-lg">💬 WhatsApp</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-16 bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">Find Us</h2>
            <p class="text-xl text-gray-600">
                Visit our office in Salem, Tamil Nadu
            </p>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="aspect-w-16 aspect-h-9">
                <iframe 
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3908.8234567890123!2d78.1234567!3d11.6543210!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTHCsDM5JzE1LjYiTiA3OMKwMDcnMjQuNCJF!5e0!3m2!1sen!2sin!4v1234567890123!5m2!1sen!2sin"
                    width="100%" 
                    height="400" 
                    style="border:0;" 
                    allowfullscreen="" 
                    loading="lazy" 
                    referrerpolicy="no-referrer-when-downgrade"
                    class="w-full h-96">
                </iframe>
            </div>
        </div>
    </div>
</section>

<!-- Why Choose Us Section -->
<section class="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">Why Choose Bhavitech?</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                We're committed to delivering exceptional results that drive your business forward
            </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center group">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">Fast Delivery</h3>
                <p class="text-gray-600">Quick turnaround times without compromising quality</p>
            </div>

            <div class="text-center group">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">Quality Assured</h3>
                <p class="text-gray-600">Rigorous testing and quality control processes</p>
            </div>

            <div class="text-center group">
                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">24/7 Support</h3>
                <p class="text-gray-600">Round-the-clock technical support and maintenance</p>
            </div>

            <div class="text-center group">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-2">Affordable Pricing</h3>
                <p class="text-gray-600">Competitive rates with transparent pricing</p>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">Frequently Asked Questions</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Quick answers to common questions about our services
            </p>
        </div>

        <div class="max-w-3xl mx-auto space-y-4">
            <div x-data="{ open: false }" class="border border-gray-200 rounded-lg">
                <button @click="open = !open" class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                    <span class="font-semibold">How long does it take to complete a website project?</span>
                    <svg :class="open ? 'rotate-180' : ''" class="w-5 h-5 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="open" x-collapse class="px-6 pb-4">
                    <p class="text-gray-600">Project timelines vary based on complexity and requirements. A simple website typically takes 2-4 weeks, while complex web applications can take 8-12 weeks. We'll provide a detailed timeline during our initial consultation.</p>
                </div>
            </div>

            <div x-data="{ open: false }" class="border border-gray-200 rounded-lg">
                <button @click="open = !open" class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                    <span class="font-semibold">Do you provide ongoing support and maintenance?</span>
                    <svg :class="open ? 'rotate-180' : ''" class="w-5 h-5 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="open" x-collapse class="px-6 pb-4">
                    <p class="text-gray-600">Yes, we offer comprehensive support and maintenance packages to keep your website secure, updated, and performing optimally. Our support includes regular updates, security monitoring, and technical assistance.</p>
                </div>
            </div>

            <div x-data="{ open: false }" class="border border-gray-200 rounded-lg">
                <button @click="open = !open" class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                    <span class="font-semibold">What is your pricing structure?</span>
                    <svg :class="open ? 'rotate-180' : ''" class="w-5 h-5 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="open" x-collapse class="px-6 pb-4">
                    <p class="text-gray-600">Our pricing is project-based and depends on the scope, complexity, and specific requirements. We provide detailed quotes after understanding your needs. Contact us for a free consultation and custom quote.</p>
                </div>
            </div>

            <div x-data="{ open: false }" class="border border-gray-200 rounded-lg">
                <button @click="open = !open" class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors">
                    <span class="font-semibold">Do you work with clients outside Salem?</span>
                    <svg :class="open ? 'rotate-180' : ''" class="w-5 h-5 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="open" x-collapse class="px-6 pb-4">
                    <p class="text-gray-600">Absolutely! While we're based in Salem, we work with clients across India and internationally. We use modern communication tools and project management systems to ensure seamless collaboration regardless of location.</p>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
