<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AuthenticationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    public function test_login_page_loads()
    {
        $response = $this->get('/login');
        $response->assertStatus(200);
        $response->assertViewIs('auth.login');
    }

    public function test_register_page_loads()
    {
        $response = $this->get('/register');
        $response->assertStatus(200);
        $response->assertViewIs('auth.register');
    }

    public function test_user_can_register()
    {
        $userData = [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'phone' => '+91 9876543210',
            'company' => 'Test Company',
            'role' => 'customer'
        ];

        $response = $this->post('/register', $userData);

        $response->assertRedirect('/customer/dashboard');
        $this->assertDatabaseHas('users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'customer'
        ]);
    }

    public function test_user_can_login()
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'customer'
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $response->assertRedirect('/customer/dashboard');
        $this->assertAuthenticatedAs($user);
    }

    public function test_admin_redirects_to_admin_panel()
    {
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin'
        ]);

        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $response->assertRedirect('/admin');
    }

    public function test_invalid_login_credentials()
    {
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);

        $response->assertSessionHasErrors(['email']);
        $this->assertGuest();
    }

    public function test_user_can_logout()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->post('/logout');

        $response->assertRedirect('/');
        $this->assertGuest();
    }

    public function test_customer_portal_requires_authentication()
    {
        $response = $this->get('/customer/dashboard');
        $response->assertRedirect('/login');
    }

    public function test_customer_portal_requires_customer_role()
    {
        $user = User::factory()->create(['role' => 'user']);

        $response = $this->actingAs($user)->get('/customer/dashboard');
        $response->assertStatus(403);
    }

    public function test_registration_validation()
    {
        $response = $this->post('/register', []);

        $response->assertSessionHasErrors(['name', 'email', 'password', 'role']);
    }

    public function test_password_confirmation_required()
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'different_password',
            'role' => 'customer'
        ]);

        $response->assertSessionHasErrors(['password']);
    }
}
