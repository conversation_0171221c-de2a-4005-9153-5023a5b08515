<?php

namespace App\Services;

use App\Models\EmailAccount;
use App\Models\EmailCampaign;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class GmailAccountManager
{
    private const DAILY_SEND_LIMIT = 500;
    private const HOURLY_SEND_LIMIT = 50;
    private const RATE_LIMIT_BUFFER = 0.9; // Use 90% of limits for safety
    
    /**
     * Get the best available Gmail account for sending
     */
    public function getBestAccountForSending(): ?EmailAccount
    {
        $accounts = EmailAccount::where('provider', 'gmail')
            ->where('is_active', true)
            ->where('is_verified', true)
            ->orderBy('last_used_at', 'asc')
            ->get();
            
        foreach ($accounts as $account) {
            if ($this->canAccountSendEmail($account)) {
                return $account;
            }
        }
        
        return null;
    }
    
    /**
     * Check if an account can send email based on rate limits
     */
    public function canAccountSendEmail(EmailAccount $account): bool
    {
        $dailyCount = $this->getDailySentCount($account);
        $hourlyCount = $this->getHourlySentCount($account);
        
        $dailyLimit = self::DAILY_SEND_LIMIT * self::RATE_LIMIT_BUFFER;
        $hourlyLimit = self::HOURLY_SEND_LIMIT * self::RATE_LIMIT_BUFFER;
        
        return $dailyCount < $dailyLimit && $hourlyCount < $hourlyLimit;
    }
    
    /**
     * Get daily sent count for an account
     */
    public function getDailySentCount(EmailAccount $account): int
    {
        $cacheKey = "email_count_daily_{$account->id}_" . now()->format('Y-m-d');
        
        return Cache::remember($cacheKey, 3600, function () use ($account) {
            return $account->sentEmails()
                ->whereDate('sent_at', today())
                ->count();
        });
    }
    
    /**
     * Get hourly sent count for an account
     */
    public function getHourlySentCount(EmailAccount $account): int
    {
        $cacheKey = "email_count_hourly_{$account->id}_" . now()->format('Y-m-d-H');
        
        return Cache::remember($cacheKey, 300, function () use ($account) {
            return $account->sentEmails()
                ->where('sent_at', '>=', now()->subHour())
                ->count();
        });
    }
    
    /**
     * Record email sent for rate limiting
     */
    public function recordEmailSent(EmailAccount $account, array $emailData = []): void
    {
        // Update last used timestamp
        $account->update(['last_used_at' => now()]);
        
        // Increment counters
        $this->incrementSentCount($account, 'daily');
        $this->incrementSentCount($account, 'hourly');
        
        // Log for monitoring
        Log::info('Email sent via account', [
            'account_id' => $account->id,
            'email' => $account->email,
            'daily_count' => $this->getDailySentCount($account),
            'hourly_count' => $this->getHourlySentCount($account)
        ]);
    }
    
    /**
     * Increment sent count in cache
     */
    private function incrementSentCount(EmailAccount $account, string $period): void
    {
        $suffix = $period === 'daily' ? now()->format('Y-m-d') : now()->format('Y-m-d-H');
        $cacheKey = "email_count_{$period}_{$account->id}_{$suffix}";
        
        Cache::increment($cacheKey);
        
        // Set expiration if key is new
        $ttl = $period === 'daily' ? 86400 : 3600;
        Cache::expire($cacheKey, $ttl);
    }
    
    /**
     * Get account health status
     */
    public function getAccountHealth(EmailAccount $account): array
    {
        $dailyCount = $this->getDailySentCount($account);
        $hourlyCount = $this->getHourlySentCount($account);
        
        $dailyUsage = ($dailyCount / self::DAILY_SEND_LIMIT) * 100;
        $hourlyUsage = ($hourlyCount / self::HOURLY_SEND_LIMIT) * 100;
        
        $status = 'healthy';
        if ($dailyUsage > 90 || $hourlyUsage > 90) {
            $status = 'critical';
        } elseif ($dailyUsage > 70 || $hourlyUsage > 70) {
            $status = 'warning';
        }
        
        return [
            'status' => $status,
            'daily_usage' => round($dailyUsage, 2),
            'hourly_usage' => round($hourlyUsage, 2),
            'daily_count' => $dailyCount,
            'hourly_count' => $hourlyCount,
            'daily_limit' => self::DAILY_SEND_LIMIT,
            'hourly_limit' => self::HOURLY_SEND_LIMIT,
            'can_send' => $this->canAccountSendEmail($account)
        ];
    }
    
    /**
     * Get all accounts health status
     */
    public function getAllAccountsHealth(): array
    {
        $accounts = EmailAccount::where('provider', 'gmail')
            ->where('is_active', true)
            ->get();
            
        $health = [];
        foreach ($accounts as $account) {
            $health[$account->id] = array_merge(
                ['email' => $account->email],
                $this->getAccountHealth($account)
            );
        }
        
        return $health;
    }
    
    /**
     * Rotate to next available account
     */
    public function rotateToNextAccount(EmailAccount $currentAccount): ?EmailAccount
    {
        $accounts = EmailAccount::where('provider', 'gmail')
            ->where('is_active', true)
            ->where('is_verified', true)
            ->where('id', '!=', $currentAccount->id)
            ->orderBy('last_used_at', 'asc')
            ->get();
            
        foreach ($accounts as $account) {
            if ($this->canAccountSendEmail($account)) {
                return $account;
            }
        }
        
        return null;
    }
    
    /**
     * Distribute emails across accounts
     */
    public function distributeEmails(array $emails, int $campaignId): array
    {
        $distribution = [];
        $availableAccounts = EmailAccount::where('provider', 'gmail')
            ->where('is_active', true)
            ->where('is_verified', true)
            ->orderBy('last_used_at', 'asc')
            ->get();
            
        if ($availableAccounts->isEmpty()) {
            throw new \Exception('No Gmail accounts available for sending');
        }
        
        $accountIndex = 0;
        foreach ($emails as $email) {
            $account = null;
            $attempts = 0;
            
            // Try to find an available account
            while ($attempts < $availableAccounts->count()) {
                $candidateAccount = $availableAccounts[$accountIndex % $availableAccounts->count()];
                
                if ($this->canAccountSendEmail($candidateAccount)) {
                    $account = $candidateAccount;
                    break;
                }
                
                $accountIndex++;
                $attempts++;
            }
            
            if (!$account) {
                // No accounts available, schedule for later
                $distribution['scheduled'][] = $email;
            } else {
                $distribution['immediate'][$account->id][] = $email;
                $accountIndex++;
            }
        }
        
        return $distribution;
    }
    
    /**
     * Check account reputation and deliverability
     */
    public function checkAccountReputation(EmailAccount $account): array
    {
        // Get recent bounce and complaint rates
        $recentEmails = $account->sentEmails()
            ->where('sent_at', '>=', now()->subDays(7))
            ->get();
            
        $totalSent = $recentEmails->count();
        $bounces = $recentEmails->where('status', 'bounced')->count();
        $complaints = $recentEmails->where('status', 'complained')->count();
        
        $bounceRate = $totalSent > 0 ? ($bounces / $totalSent) * 100 : 0;
        $complaintRate = $totalSent > 0 ? ($complaints / $totalSent) * 100 : 0;
        
        $reputation = 'good';
        if ($bounceRate > 5 || $complaintRate > 0.5) {
            $reputation = 'poor';
        } elseif ($bounceRate > 2 || $complaintRate > 0.1) {
            $reputation = 'warning';
        }
        
        return [
            'reputation' => $reputation,
            'bounce_rate' => round($bounceRate, 2),
            'complaint_rate' => round($complaintRate, 2),
            'total_sent_7_days' => $totalSent,
            'bounces_7_days' => $bounces,
            'complaints_7_days' => $complaints
        ];
    }
    
    /**
     * Auto-pause accounts with poor reputation
     */
    public function autoPauseProblematicAccounts(): array
    {
        $pausedAccounts = [];
        
        $accounts = EmailAccount::where('provider', 'gmail')
            ->where('is_active', true)
            ->get();
            
        foreach ($accounts as $account) {
            $reputation = $this->checkAccountReputation($account);
            
            if ($reputation['reputation'] === 'poor') {
                $account->update([
                    'is_active' => false,
                    'pause_reason' => 'Auto-paused due to poor reputation',
                    'paused_at' => now()
                ]);
                
                $pausedAccounts[] = $account->email;
                
                Log::warning('Account auto-paused due to poor reputation', [
                    'account' => $account->email,
                    'reputation_data' => $reputation
                ]);
            }
        }
        
        return $pausedAccounts;
    }
}
