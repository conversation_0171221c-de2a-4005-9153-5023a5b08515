@echo off
REM Branch Management Script for 1 Crore Email Management System
REM This script provides comprehensive branch management functionality

setlocal enabledelayedexpansion

echo.
echo 🌿 Branch Management System for 1 Crore Email Management
echo.

REM Check if Git is available
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Git is not installed or not in PATH
    echo Please install Git from: https://git-scm.com/download/windows
    pause
    exit /b 1
)

REM Check if we're in a Git repository
if not exist .git (
    echo ❌ Not in a Git repository
    echo Run 'scripts\init-git.bat' first to initialize the repository
    pause
    exit /b 1
)

:MENU
echo.
echo 📋 Branch Management Options:
echo.
echo 1. Show current branch status
echo 2. Create new feature branch
echo 3. Switch to existing branch
echo 4. Merge feature branch to development
echo 5. Create release branch
echo 6. Create hotfix branch
echo 7. List all branches
echo 8. Delete branch
echo 9. Push branch to remote
echo 10. Pull latest changes
echo 11. Show branch workflow guide
echo 0. Exit
echo.
set /p choice="Select an option (0-11): "

if "%choice%"=="1" goto STATUS
if "%choice%"=="2" goto CREATE_FEATURE
if "%choice%"=="3" goto SWITCH_BRANCH
if "%choice%"=="4" goto MERGE_FEATURE
if "%choice%"=="5" goto CREATE_RELEASE
if "%choice%"=="6" goto CREATE_HOTFIX
if "%choice%"=="7" goto LIST_BRANCHES
if "%choice%"=="8" goto DELETE_BRANCH
if "%choice%"=="9" goto PUSH_BRANCH
if "%choice%"=="10" goto PULL_CHANGES
if "%choice%"=="11" goto WORKFLOW_GUIDE
if "%choice%"=="0" goto EXIT
goto MENU

:STATUS
echo.
echo 📊 Current Branch Status:
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo Current branch: 
git branch --show-current 2>nul || echo "Unable to determine"
echo.
echo Recent commits:
git log --oneline -5 2>nul || echo "No commits found"
echo.
echo Branch status:
git status --short 2>nul || echo "Unable to get status"
echo.
echo Uncommitted changes:
git diff --stat 2>nul || echo "No changes"
goto MENU

:CREATE_FEATURE
echo.
echo 🔧 Creating New Feature Branch
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo Feature branch types:
echo 1. email-campaigns (Email campaign management)
echo 2. analytics (Analytics and reporting)
echo 3. whatsapp-integration (WhatsApp features)
echo 4. performance-optimization (Performance improvements)
echo 5. api-development (API development)
echo 6. mobile-app (Mobile application)
echo 7. advanced-segmentation (Advanced contact segmentation)
echo 8. automation-workflows (Email automation)
echo 9. custom (Custom feature name)
echo.
set /p feature_type="Select feature type (1-9): "

if "%feature_type%"=="1" set feature_name=email-campaigns
if "%feature_type%"=="2" set feature_name=analytics
if "%feature_type%"=="3" set feature_name=whatsapp-integration
if "%feature_type%"=="4" set feature_name=performance-optimization
if "%feature_type%"=="5" set feature_name=api-development
if "%feature_type%"=="6" set feature_name=mobile-app
if "%feature_type%"=="7" set feature_name=advanced-segmentation
if "%feature_type%"=="8" set feature_name=automation-workflows
if "%feature_type%"=="9" (
    set /p feature_name="Enter custom feature name: "
)

if "%feature_name%"=="" (
    echo ❌ Invalid selection
    goto MENU
)

set /p branch_suffix="Enter branch suffix (optional): "
if not "%branch_suffix%"=="" (
    set branch_name=feature/%feature_name%-%branch_suffix%
) else (
    set branch_name=feature/%feature_name%
)

echo.
echo Creating branch: %branch_name%
git checkout -b %branch_name% development
if %errorlevel% equ 0 (
    echo ✅ Branch '%branch_name%' created and checked out
    echo 📝 Don't forget to push: git push -u origin %branch_name%
) else (
    echo ❌ Failed to create branch
)
goto MENU

:SWITCH_BRANCH
echo.
echo 🔄 Switch to Existing Branch
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo Available branches:
git branch -a
echo.
set /p branch_name="Enter branch name to switch to: "
if "%branch_name%"=="" goto MENU

git checkout %branch_name%
if %errorlevel% equ 0 (
    echo ✅ Switched to branch '%branch_name%'
) else (
    echo ❌ Failed to switch to branch
)
goto MENU

:MERGE_FEATURE
echo.
echo 🔀 Merge Feature Branch to Development
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo Current branch: 
git branch --show-current
echo.
echo ⚠️ This will merge the current branch into development
set /p confirm="Continue? (y/N): "
if /i not "%confirm%"=="y" goto MENU

git checkout development
git pull origin development
git merge --no-ff -
if %errorlevel% equ 0 (
    echo ✅ Feature branch merged successfully
    echo 📝 Don't forget to push: git push origin development
) else (
    echo ❌ Merge failed - resolve conflicts manually
)
goto MENU

:CREATE_RELEASE
echo.
echo 🚀 Create Release Branch
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
set /p version="Enter version number (e.g., 1.0.0): "
if "%version%"=="" goto MENU

set release_branch=release/%version%
git checkout -b %release_branch% development
if %errorlevel% equ 0 (
    echo ✅ Release branch '%release_branch%' created
    echo 📝 Update version numbers and prepare for release
) else (
    echo ❌ Failed to create release branch
)
goto MENU

:CREATE_HOTFIX
echo.
echo 🔥 Create Hotfix Branch
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
set /p hotfix_name="Enter hotfix name: "
if "%hotfix_name%"=="" goto MENU

set hotfix_branch=hotfix/%hotfix_name%
git checkout -b %hotfix_branch% main
if %errorlevel% equ 0 (
    echo ✅ Hotfix branch '%hotfix_branch%' created
    echo 📝 Fix the issue and merge to both main and development
) else (
    echo ❌ Failed to create hotfix branch
)
goto MENU

:LIST_BRANCHES
echo.
echo 📋 All Branches
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo Local branches:
git branch
echo.
echo Remote branches:
git branch -r
echo.
echo All branches:
git branch -a
goto MENU

:DELETE_BRANCH
echo.
echo 🗑️ Delete Branch
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo Available branches:
git branch
echo.
set /p branch_name="Enter branch name to delete: "
if "%branch_name%"=="" goto MENU

echo ⚠️ This will permanently delete the branch '%branch_name%'
set /p confirm="Are you sure? (y/N): "
if /i not "%confirm%"=="y" goto MENU

git branch -d %branch_name%
if %errorlevel% equ 0 (
    echo ✅ Branch '%branch_name%' deleted locally
    echo 📝 To delete from remote: git push origin --delete %branch_name%
) else (
    echo ❌ Failed to delete branch (use -D for force delete)
)
goto MENU

:PUSH_BRANCH
echo.
echo 📤 Push Branch to Remote
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo Current branch: 
git branch --show-current
echo.
set /p confirm="Push current branch to remote? (y/N): "
if /i not "%confirm%"=="y" goto MENU

git push -u origin HEAD
if %errorlevel% equ 0 (
    echo ✅ Branch pushed to remote successfully
) else (
    echo ❌ Failed to push branch
)
goto MENU

:PULL_CHANGES
echo.
echo 📥 Pull Latest Changes
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
git pull
if %errorlevel% equ 0 (
    echo ✅ Latest changes pulled successfully
) else (
    echo ❌ Failed to pull changes
)
goto MENU

:WORKFLOW_GUIDE
echo.
echo 📖 Branch Workflow Guide
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo 🌿 Branch Structure:
echo   main/master     - Production-ready code
echo   development     - Integration branch for features
echo   feature/*       - New features and enhancements
echo   release/*       - Release preparation
echo   hotfix/*        - Critical bug fixes
echo.
echo 🔄 Workflow:
echo   1. Create feature branch from development
echo   2. Develop and test feature
echo   3. Merge feature to development
echo   4. Create release branch from development
echo   5. Test and prepare release
echo   6. Merge release to main and development
echo   7. Tag release version
echo.
echo 🚀 Feature Development:
echo   git checkout development
echo   git pull origin development
echo   git checkout -b feature/new-feature
echo   [develop feature]
echo   git add . && git commit -m "feat: add new feature"
echo   git push -u origin feature/new-feature
echo   [create pull request]
echo.
pause
goto MENU

:EXIT
echo.
echo 👋 Goodbye!
echo.
exit /b 0
