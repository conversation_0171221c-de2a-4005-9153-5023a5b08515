<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SocialMediaPostResource\Pages;
use App\Models\SocialMediaPost;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;

class SocialMediaPostResource extends Resource
{
    protected static ?string $model = SocialMediaPost::class;
    protected static ?string $navigationIcon = 'heroicon-o-megaphone';
    protected static ?string $navigationGroup = 'Social Media';
    protected static ?string $navigationLabel = 'Posts';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Post Details')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Select::make('status')
                            ->options([
                                'draft' => 'Draft',
                                'scheduled' => 'Scheduled',
                                'published' => 'Published',
                                'failed' => 'Failed',
                            ])
                            ->required(),
                        Forms\Components\Select::make('post_type')
                            ->options([
                                'text' => 'Text Only',
                                'image' => 'Image Post',
                                'video' => 'Video Post',
                                'carousel' => 'Carousel',
                                'story' => 'Story',
                            ])
                            ->required()
                            ->reactive(),
                    ])->columns(3),

                Forms\Components\Section::make('Content')
                    ->schema([
                        Forms\Components\Textarea::make('content')
                            ->required()
                            ->rows(5)
                            ->maxLength(2200)
                            ->helperText('Character limits vary by platform')
                            ->columnSpanFull(),
                        Forms\Components\TagsInput::make('hashtags')
                            ->placeholder('Add hashtags without #')
                            ->columnSpanFull(),
                        Forms\Components\FileUpload::make('media_urls')
                            ->label('Media Files')
                            ->multiple()
                            ->acceptedFileTypes(['image/*', 'video/*'])
                            ->maxSize(50000) // 50MB
                            ->directory('social-media')
                            ->columnSpanFull()
                            ->visible(fn ($get) => in_array($get('post_type'), ['image', 'video', 'carousel'])),
                    ]),

                Forms\Components\Section::make('Platform Targeting')
                    ->schema([
                        Forms\Components\CheckboxList::make('platforms')
                            ->options([
                                'facebook' => 'Facebook',
                                'instagram' => 'Instagram',
                                'twitter' => 'Twitter/X',
                                'linkedin' => 'LinkedIn',
                                'youtube' => 'YouTube',
                            ])
                            ->required()
                            ->columns(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Platform-Specific Content')
                    ->schema([
                        Forms\Components\Textarea::make('facebook_content')
                            ->label('Facebook Content')
                            ->rows(3)
                            ->maxLength(63206)
                            ->helperText('Facebook allows up to 63,206 characters'),
                        Forms\Components\Textarea::make('instagram_content')
                            ->label('Instagram Content')
                            ->rows(3)
                            ->maxLength(2200)
                            ->helperText('Instagram allows up to 2,200 characters'),
                        Forms\Components\Textarea::make('twitter_content')
                            ->label('Twitter/X Content')
                            ->rows(3)
                            ->maxLength(280)
                            ->helperText('Twitter allows up to 280 characters'),
                        Forms\Components\Textarea::make('linkedin_content')
                            ->label('LinkedIn Content')
                            ->rows(3)
                            ->maxLength(3000)
                            ->helperText('LinkedIn allows up to 3,000 characters'),
                    ])->columns(2),

                Forms\Components\Section::make('Scheduling')
                    ->schema([
                        Forms\Components\DateTimePicker::make('scheduled_at')
                            ->label('Schedule Post Time'),
                        Forms\Components\Select::make('timezone')
                            ->options([
                                'Asia/Kolkata' => 'India (IST)',
                                'UTC' => 'UTC',
                                'America/New_York' => 'Eastern Time',
                                'Europe/London' => 'London Time',
                            ])
                            ->default('Asia/Kolkata'),
                        Forms\Components\Toggle::make('auto_publish')
                            ->label('Auto Publish')
                            ->helperText('Automatically publish when scheduled time arrives'),
                    ])->columns(3),

                Forms\Components\Section::make('Advanced Options')
                    ->schema([
                        Forms\Components\TagsInput::make('target_audience')
                            ->label('Target Audience Tags')
                            ->placeholder('Add audience tags'),
                        Forms\Components\TextInput::make('campaign_id')
                            ->label('Campaign ID')
                            ->numeric()
                            ->helperText('Optional: Link to a specific campaign'),
                        Forms\Components\Textarea::make('notes')
                            ->label('Internal Notes')
                            ->rows(3),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->limit(30),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'draft',
                        'warning' => 'scheduled',
                        'success' => 'published',
                        'danger' => 'failed',
                    ]),
                Tables\Columns\BadgeColumn::make('post_type')
                    ->colors([
                        'primary' => 'text',
                        'success' => 'image',
                        'info' => 'video',
                        'warning' => 'carousel',
                        'secondary' => 'story',
                    ]),
                Tables\Columns\TextColumn::make('platforms')
                    ->badge()
                    ->separator(',')
                    ->colors([
                        'primary' => 'facebook',
                        'danger' => 'instagram',
                        'info' => 'twitter',
                        'success' => 'linkedin',
                        'warning' => 'youtube',
                    ]),
                Tables\Columns\TextColumn::make('total_engagement')
                    ->label('Engagement')
                    ->numeric()
                    ->sortable(false),
                Tables\Columns\TextColumn::make('total_reach')
                    ->label('Reach')
                    ->numeric()
                    ->sortable(false),
                Tables\Columns\TextColumn::make('scheduled_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('published_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'scheduled' => 'Scheduled',
                        'published' => 'Published',
                        'failed' => 'Failed',
                    ]),
                Tables\Filters\SelectFilter::make('post_type')
                    ->options([
                        'text' => 'Text Only',
                        'image' => 'Image Post',
                        'video' => 'Video Post',
                        'carousel' => 'Carousel',
                        'story' => 'Story',
                    ]),
                Tables\Filters\Filter::make('platforms')
                    ->form([
                        Forms\Components\CheckboxList::make('platforms')
                            ->options([
                                'facebook' => 'Facebook',
                                'instagram' => 'Instagram',
                                'twitter' => 'Twitter/X',
                                'linkedin' => 'LinkedIn',
                                'youtube' => 'YouTube',
                            ]),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (!empty($data['platforms'])) {
                            return $query->where(function ($query) use ($data) {
                                foreach ($data['platforms'] as $platform) {
                                    $query->orWhereJsonContains('platforms', $platform);
                                }
                            });
                        }
                        return $query;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->action(function (SocialMediaPost $record) {
                        $newPost = $record->replicate();
                        $newPost->title = $record->title . ' (Copy)';
                        $newPost->status = 'draft';
                        $newPost->scheduled_at = null;
                        $newPost->published_at = null;
                        $newPost->save();
                        
                        return redirect()->route('filament.admin.resources.social-media-posts.edit', $newPost);
                    }),
                Tables\Actions\Action::make('publish_now')
                    ->icon('heroicon-o-paper-airplane')
                    ->action(function (SocialMediaPost $record) {
                        // Publish post logic here
                        $record->update([
                            'status' => 'published',
                            'published_at' => now(),
                        ]);
                        
                        \Filament\Notifications\Notification::make()
                            ->title('Post published successfully')
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation()
                    ->visible(fn (SocialMediaPost $record) => $record->status === 'draft'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('schedule')
                        ->label('Schedule Posts')
                        ->icon('heroicon-o-clock')
                        ->form([
                            Forms\Components\DateTimePicker::make('scheduled_at')
                                ->required()
                                ->label('Schedule Time'),
                        ])
                        ->action(function ($records, array $data) {
                            $records->each(function ($record) use ($data) {
                                $record->update([
                                    'status' => 'scheduled',
                                    'scheduled_at' => $data['scheduled_at'],
                                ]);
                            });
                        }),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Post Overview')
                    ->schema([
                        Infolists\Components\TextEntry::make('title'),
                        Infolists\Components\TextEntry::make('status')
                            ->badge(),
                        Infolists\Components\TextEntry::make('post_type')
                            ->badge(),
                        Infolists\Components\TextEntry::make('platforms')
                            ->badge()
                            ->separator(','),
                        Infolists\Components\TextEntry::make('scheduled_at')
                            ->dateTime(),
                        Infolists\Components\TextEntry::make('published_at')
                            ->dateTime(),
                    ])->columns(3),

                Infolists\Components\Section::make('Performance Metrics')
                    ->schema([
                        Infolists\Components\TextEntry::make('total_reach')
                            ->label('Total Reach'),
                        Infolists\Components\TextEntry::make('total_engagement')
                            ->label('Total Engagement'),
                        Infolists\Components\TextEntry::make('total_likes')
                            ->label('Total Likes'),
                        Infolists\Components\TextEntry::make('total_comments')
                            ->label('Total Comments'),
                        Infolists\Components\TextEntry::make('total_shares')
                            ->label('Total Shares'),
                        Infolists\Components\TextEntry::make('engagement_rate')
                            ->label('Engagement Rate')
                            ->formatStateUsing(fn ($state) => $state ? number_format($state, 2) . '%' : 'N/A'),
                    ])->columns(3),

                Infolists\Components\Section::make('Content')
                    ->schema([
                        Infolists\Components\TextEntry::make('content')
                            ->columnSpanFull(),
                        Infolists\Components\TextEntry::make('hashtags')
                            ->badge()
                            ->separator(',')
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSocialMediaPosts::route('/'),
            'create' => Pages\CreateSocialMediaPost::route('/create'),
            'view' => Pages\ViewSocialMediaPost::route('/{record}'),
            'edit' => Pages\EditSocialMediaPost::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', 'scheduled')->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }
}
