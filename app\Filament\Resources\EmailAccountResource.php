<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EmailAccountResource\Pages;
use App\Models\EmailAccount;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;

class EmailAccountResource extends Resource
{
    protected static ?string $model = EmailAccount::class;
    protected static ?string $navigationIcon = 'heroicon-o-at-symbol';
    protected static ?string $navigationGroup = 'Email Marketing';
    protected static ?int $navigationSort = 5;
    protected static ?string $recordTitleAttribute = 'email';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Account Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Friendly name for this email account'),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Forms\Components\Select::make('provider')
                            ->options([
                                'gmail' => 'Gmail',
                                'outlook' => 'Outlook/Hotmail',
                                'yahoo' => 'Yahoo Mail',
                                'sendgrid' => 'SendGrid',
                                'mailgun' => 'Mailgun',
                                'ses' => 'Amazon SES',
                                'smtp' => 'Custom SMTP',
                            ])
                            ->required()
                            ->reactive(),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                        Forms\Components\Toggle::make('is_primary')
                            ->label('Primary Account')
                            ->helperText('Primary account will be used as default for sending'),
                    ])->columns(2),

                Forms\Components\Section::make('SMTP Configuration')
                    ->schema([
                        Forms\Components\TextInput::make('smtp_host')
                            ->label('SMTP Host')
                            ->maxLength(255)
                            ->visible(fn (Forms\Get $get): bool => in_array($get('provider'), ['smtp', 'custom'])),
                        Forms\Components\TextInput::make('smtp_port')
                            ->label('SMTP Port')
                            ->numeric()
                            ->default(587)
                            ->visible(fn (Forms\Get $get): bool => in_array($get('provider'), ['smtp', 'custom'])),
                        Forms\Components\Select::make('encryption')
                            ->options([
                                'tls' => 'TLS',
                                'ssl' => 'SSL',
                                'none' => 'None',
                            ])
                            ->default('tls')
                            ->visible(fn (Forms\Get $get): bool => in_array($get('provider'), ['smtp', 'custom'])),
                        Forms\Components\TextInput::make('username')
                            ->maxLength(255)
                            ->visible(fn (Forms\Get $get): bool => in_array($get('provider'), ['smtp', 'custom'])),
                        Forms\Components\TextInput::make('password')
                            ->password()
                            ->maxLength(255)
                            ->visible(fn (Forms\Get $get): bool => in_array($get('provider'), ['smtp', 'custom'])),
                    ])->columns(2)
                    ->visible(fn (Forms\Get $get): bool => in_array($get('provider'), ['smtp', 'custom'])),

                Forms\Components\Section::make('OAuth Configuration')
                    ->schema([
                        Forms\Components\Textarea::make('access_token')
                            ->label('Access Token')
                            ->rows(3)
                            ->columnSpanFull(),
                        Forms\Components\Textarea::make('refresh_token')
                            ->label('Refresh Token')
                            ->rows(3)
                            ->columnSpanFull(),
                        Forms\Components\DateTimePicker::make('token_expires_at')
                            ->label('Token Expires At'),
                    ])
                    ->visible(fn (Forms\Get $get): bool => in_array($get('provider'), ['gmail', 'outlook'])),

                Forms\Components\Section::make('Sending Limits')
                    ->schema([
                        Forms\Components\TextInput::make('daily_send_limit')
                            ->label('Daily Send Limit')
                            ->numeric()
                            ->default(500)
                            ->helperText('Maximum emails per day (Gmail: 500, Outlook: 300)'),
                        Forms\Components\TextInput::make('emails_sent_today')
                            ->label('Emails Sent Today')
                            ->numeric()
                            ->default(0)
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('last_email_sent_at')
                            ->label('Last Email Sent')
                            ->disabled(),
                        Forms\Components\DateTimePicker::make('last_reset_at')
                            ->label('Last Reset')
                            ->disabled(),
                    ])->columns(2),

                Forms\Components\Section::make('Status & Settings')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->options([
                                'active' => 'Active',
                                'inactive' => 'Inactive',
                                'suspended' => 'Suspended',
                                'error' => 'Error',
                            ])
                            ->default('active')
                            ->required(),
                        Forms\Components\Textarea::make('status_message')
                            ->label('Status Message')
                            ->maxLength(500)
                            ->rows(2),
                        Forms\Components\TextInput::make('cost_per_email')
                            ->label('Cost Per Email')
                            ->numeric()
                            ->step(0.0001)
                            ->prefix('₹')
                            ->helperText('Cost in rupees per email sent'),
                        Forms\Components\KeyValue::make('settings')
                            ->keyLabel('Setting Name')
                            ->valueLabel('Value')
                            ->addActionLabel('Add Setting'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->icon('heroicon-m-envelope'),
                Tables\Columns\BadgeColumn::make('provider')
                    ->colors([
                        'primary' => 'gmail',
                        'info' => 'outlook',
                        'warning' => 'yahoo',
                        'success' => 'sendgrid',
                        'secondary' => 'smtp',
                    ]),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'success' => 'active',
                        'secondary' => 'inactive',
                        'warning' => 'suspended',
                        'danger' => 'error',
                    ]),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean()
                    ->label('Active'),
                Tables\Columns\IconColumn::make('is_primary')
                    ->boolean()
                    ->label('Primary'),
                Tables\Columns\TextColumn::make('daily_send_limit')
                    ->label('Daily Limit')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('emails_sent_today')
                    ->label('Sent Today')
                    ->numeric()
                    ->sortable()
                    ->color(fn (EmailAccount $record): string => 
                        $record->emails_sent_today >= $record->daily_send_limit ? 'danger' : 'success'
                    ),
                Tables\Columns\TextColumn::make('usage_percentage')
                    ->label('Usage %')
                    ->getStateUsing(fn (EmailAccount $record): string =>
                        $record->daily_send_limit > 0
                            ? number_format(($record->emails_sent_today / $record->daily_send_limit) * 100, 1) . '%'
                            : '0%'
                    )
                    ->badge()
                    ->color(fn (EmailAccount $record): string =>
                        match (true) {
                            ($record->daily_send_limit > 0 ? ($record->emails_sent_today / $record->daily_send_limit) * 100 : 0) >= 90 => 'danger',
                            ($record->daily_send_limit > 0 ? ($record->emails_sent_today / $record->daily_send_limit) * 100 : 0) >= 70 => 'warning',
                            default => 'success',
                        }
                    ),
                Tables\Columns\TextColumn::make('last_email_sent_at')
                    ->label('Last Used')
                    ->dateTime('M j, g:i A')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('provider')
                    ->options([
                        'gmail' => 'Gmail',
                        'outlook' => 'Outlook',
                        'yahoo' => 'Yahoo',
                        'sendgrid' => 'SendGrid',
                        'mailgun' => 'Mailgun',
                        'ses' => 'Amazon SES',
                        'smtp' => 'SMTP',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'suspended' => 'Suspended',
                        'error' => 'Error',
                    ]),
                Tables\Filters\Filter::make('is_active')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true))
                    ->label('Active Only'),
                Tables\Filters\Filter::make('near_limit')
                    ->query(fn (Builder $query): Builder => 
                        $query->whereRaw('emails_sent_today >= daily_send_limit * 0.8')
                    )
                    ->label('Near Daily Limit (80%+)'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('test_connection')
                    ->label('Test')
                    ->icon('heroicon-o-wifi')
                    ->color('primary')
                    ->action(function (EmailAccount $record): void {
                        // Test connection logic would go here
                        Notification::make()
                            ->title('Connection test completed')
                            ->body('Email account is working properly')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('reset_daily_count')
                    ->label('Reset Count')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->action(function (EmailAccount $record): void {
                        $record->update([
                            'emails_sent_today' => 0,
                            'last_reset_at' => now(),
                        ]);
                        Notification::make()
                            ->title('Daily count reset successfully')
                            ->success()
                            ->send();
                    })
                    ->requiresConfirmation(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('activate')
                        ->label('Activate')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function (Collection $records): void {
                            $records->each->update(['is_active' => true, 'status' => 'active']);
                            Notification::make()
                                ->title('Email accounts activated successfully')
                                ->success()
                                ->send();
                        }),
                    BulkAction::make('deactivate')
                        ->label('Deactivate')
                        ->icon('heroicon-o-x-mark')
                        ->color('warning')
                        ->action(function (Collection $records): void {
                            $records->each->update(['is_active' => false, 'status' => 'inactive']);
                            Notification::make()
                                ->title('Email accounts deactivated successfully')
                                ->success()
                                ->send();
                        }),
                    BulkAction::make('reset_counts')
                        ->label('Reset Daily Counts')
                        ->icon('heroicon-o-arrow-path')
                        ->color('secondary')
                        ->action(function (Collection $records): void {
                            $records->each->update([
                                'emails_sent_today' => 0,
                                'last_reset_at' => now(),
                            ]);
                            Notification::make()
                                ->title('Daily counts reset successfully')
                                ->success()
                                ->send();
                        })
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmailAccounts::route('/'),
            'create' => Pages\CreateEmailAccount::route('/create'),
            'view' => Pages\ViewEmailAccount::route('/{record}'),
            'edit' => Pages\EditEmailAccount::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }
}
