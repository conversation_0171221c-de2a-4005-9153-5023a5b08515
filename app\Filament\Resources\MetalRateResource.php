<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MetalRateResource\Pages;
use App\Filament\Resources\MetalRateResource\RelationManagers;
use App\Models\MetalRate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MetalRateResource extends Resource
{
    protected static ?string $model = MetalRate::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationGroup = 'Business Management';

    protected static ?string $navigationLabel = 'Metal Rates';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('metal_type')
                    ->required(),
                Forms\Components\TextInput::make('purity'),
                Forms\Components\TextInput::make('unit')
                    ->required(),
                Forms\Components\TextInput::make('currency')
                    ->required()
                    ->maxLength(3)
                    ->default('INR'),
                Forms\Components\TextInput::make('buy_price')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('sell_price')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('spot_price')
                    ->numeric(),
                Forms\Components\TextInput::make('source')
                    ->maxLength(255),
                Forms\Components\TextInput::make('market')
                    ->required()
                    ->maxLength(255)
                    ->default('domestic'),
                Forms\Components\TextInput::make('location')
                    ->maxLength(255),
                Forms\Components\TextInput::make('change_amount')
                    ->numeric(),
                Forms\Components\TextInput::make('change_percentage')
                    ->numeric(),
                Forms\Components\DateTimePicker::make('rate_date')
                    ->required(),
                Forms\Components\Toggle::make('is_active')
                    ->required(),
                Forms\Components\TextInput::make('additional_data'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('metal_type'),
                Tables\Columns\TextColumn::make('purity'),
                Tables\Columns\TextColumn::make('unit'),
                Tables\Columns\TextColumn::make('currency')
                    ->searchable(),
                Tables\Columns\TextColumn::make('buy_price')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sell_price')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('spot_price')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('source')
                    ->searchable(),
                Tables\Columns\TextColumn::make('market')
                    ->searchable(),
                Tables\Columns\TextColumn::make('location')
                    ->searchable(),
                Tables\Columns\TextColumn::make('change_amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('change_percentage')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('rate_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMetalRates::route('/'),
            'create' => Pages\CreateMetalRate::route('/create'),
            'edit' => Pages\EditMetalRate::route('/{record}/edit'),
        ];
    }
}
