# 🚀 Production Deployment Guide for 1 Crore Email Management System

## 📋 Overview
This guide covers production deployment, server requirements, scaling strategies, and best practices for handling 1 crore (10 million) emails efficiently and reliably.

## 🖥️ Server Requirements

### Minimum Production Requirements
```
CPU: 8 cores (Intel Xeon or AMD EPYC)
RAM: 32 GB DDR4
Storage: 1 TB NVMe SSD
Network: 1 Gbps dedicated bandwidth
OS: Ubuntu 22.04 LTS or CentOS 8+
```

### Recommended Production Setup
```
CPU: 16 cores (Intel Xeon or AMD EPYC)
RAM: 64 GB DDR4
Storage: 2 TB NVMe SSD (RAID 1)
Network: 10 Gbps dedicated bandwidth
OS: Ubuntu 22.04 LTS
Load Balancer: Nginx or HAProxy
```

### Database Server (Separate)
```
CPU: 12 cores
RAM: 64 GB DDR4
Storage: 4 TB NVMe SSD (RAID 10)
Database: MySQL 8.0 or PostgreSQL 14+
Backup: Daily automated backups
```

## 🐳 Docker Production Setup

### 1. Create Production Docker Compose
```yaml
version: '3.8'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
      - "443:443"
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_HOST=database
      - REDIS_HOST=redis
      - QUEUE_CONNECTION=redis
    volumes:
      - ./storage:/var/www/html/storage
      - ./bootstrap/cache:/var/www/html/bootstrap/cache
    depends_on:
      - database
      - redis
    restart: unless-stopped

  database:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d
    ports:
      - "3306:3306"
    restart: unless-stopped
    command: --innodb-buffer-pool-size=16G --max-connections=1000

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --maxmemory 8gb --maxmemory-policy allkeys-lru

  queue-worker:
    build:
      context: .
      dockerfile: Dockerfile.prod
    command: php artisan queue:work --sleep=3 --tries=3 --max-time=3600
    environment:
      - APP_ENV=production
      - DB_HOST=database
      - REDIS_HOST=redis
    volumes:
      - ./storage:/var/www/html/storage
    depends_on:
      - database
      - redis
    restart: unless-stopped
    deploy:
      replicas: 4

  scheduler:
    build:
      context: .
      dockerfile: Dockerfile.prod
    command: php artisan schedule:work
    environment:
      - APP_ENV=production
      - DB_HOST=database
      - REDIS_HOST=redis
    volumes:
      - ./storage:/var/www/html/storage
    depends_on:
      - database
      - redis
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

### 2. Production Dockerfile
```dockerfile
FROM php:8.2-fpm-alpine

# Install system dependencies
RUN apk add --no-cache \
    nginx \
    supervisor \
    mysql-client \
    zip \
    unzip \
    git \
    curl \
    libpng-dev \
    libjpeg-turbo-dev \
    freetype-dev \
    libzip-dev \
    icu-dev

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        gd \
        zip \
        intl \
        opcache \
        pcntl \
        bcmath

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www/html

# Copy application files
COPY . .

# Install dependencies
RUN composer install --no-dev --optimize-autoloader

# Set permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html/storage \
    && chmod -R 755 /var/www/html/bootstrap/cache

# Copy configuration files
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/php.ini /usr/local/etc/php/php.ini
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

EXPOSE 80 443

CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

## 🗄️ Database Optimization

### MySQL Configuration (my.cnf)
```ini
[mysqld]
# Basic Settings
default-storage-engine = innodb
sql_mode = NO_ENGINE_SUBSTITUTION,STRICT_TRANS_TABLES

# Connection Settings
max_connections = 1000
max_connect_errors = 1000000

# Buffer Pool Settings (for 64GB RAM)
innodb_buffer_pool_size = 48G
innodb_buffer_pool_instances = 16
innodb_log_file_size = 2G
innodb_log_buffer_size = 256M

# Performance Settings
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_io_capacity = 2000
innodb_io_capacity_max = 4000

# Query Cache (for read-heavy workloads)
query_cache_type = 1
query_cache_size = 2G
query_cache_limit = 256M

# Temporary Tables
tmp_table_size = 1G
max_heap_table_size = 1G

# Slow Query Log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

### Database Indexing Strategy
```sql
-- Optimize contacts table for massive datasets
ALTER TABLE contacts 
ADD INDEX idx_email_subscription (email, is_subscribed),
ADD INDEX idx_geographic_engagement (country, state, city, engagement_score),
ADD INDEX idx_industry_active (industry, is_active),
ADD INDEX idx_source_date (source, created_at),
ADD INDEX idx_engagement_subscription (engagement_score, is_subscribed, is_active);

-- Optimize contact_list_members for fast queries
ALTER TABLE contact_list_members
ADD INDEX idx_list_status_date (contact_list_id, status, subscribed_at),
ADD INDEX idx_contact_lists (contact_id, contact_list_id, status);

-- Optimize email campaigns for performance
ALTER TABLE email_campaigns
ADD INDEX idx_status_schedule_type (status, scheduled_at, type),
ADD INDEX idx_creator_date (created_by, created_at);

-- Partition large tables by date (for tables with 10M+ records)
ALTER TABLE email_deliverability_logs 
PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## ⚡ Performance Optimization

### PHP Configuration (php.ini)
```ini
; Memory Settings
memory_limit = 2G
max_execution_time = 300
max_input_time = 300

; File Upload Settings
upload_max_filesize = 100M
post_max_size = 100M
max_file_uploads = 20

; OPcache Settings
opcache.enable = 1
opcache.memory_consumption = 512
opcache.interned_strings_buffer = 64
opcache.max_accelerated_files = 32531
opcache.validate_timestamps = 0
opcache.save_comments = 1
opcache.fast_shutdown = 1

; Session Settings
session.gc_maxlifetime = 7200
session.gc_probability = 1
session.gc_divisor = 100
```

### Laravel Optimization Commands
```bash
# Production optimization
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan event:cache
php artisan optimize

# Database optimization
php artisan migrate --force
php artisan db:seed --class=ProductionSeeder

# Queue optimization
php artisan queue:restart
```

## 🔄 Queue Management for Massive Emails

### Supervisor Configuration
```ini
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/html/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600 --memory=512
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/worker.log
stopwaitsecs=3600

[program:laravel-scheduler]
process_name=%(program_name)s
command=php /var/www/html/artisan schedule:work
autostart=true
autorestart=true
user=www-data
redirect_stderr=true
stdout_logfile=/var/www/html/storage/logs/scheduler.log
```

### Redis Configuration
```conf
# Memory optimization for massive email queues
maxmemory 16gb
maxmemory-policy allkeys-lru

# Persistence settings
save 900 1
save 300 10
save 60 10000

# Network settings
tcp-keepalive 300
timeout 0

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log
```

## 📧 Email Sending Optimization

### Multiple Email Provider Setup
```env
# Primary Email Provider (SendGrid)
MAIL_MAILER=sendgrid
SENDGRID_API_KEY=your_sendgrid_api_key

# Backup Email Provider (Mailgun)
MAILGUN_DOMAIN=your_domain.com
MAILGUN_SECRET=your_mailgun_secret

# SMTP Backup
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
```

### Email Account Distribution Strategy
```
Account Distribution for 1 Crore Emails:

Tier 1 (High Volume): 20 accounts × 500 emails/day = 10,000/day
Tier 2 (Medium Volume): 30 accounts × 300 emails/day = 9,000/day
Tier 3 (Backup): 10 accounts × 200 emails/day = 2,000/day

Total Daily Capacity: 21,000 emails/day
Monthly Capacity: 630,000 emails
Annual Capacity: 7.67 million emails

For 1 Crore (10 million) emails: ~1.3 years
```

## 🔒 Security & Compliance

### SSL/TLS Configuration
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/ssl/certs/yourdomain.crt;
    ssl_certificate_key /etc/ssl/private/yourdomain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://app:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### Environment Security
```env
# Production Environment
APP_ENV=production
APP_DEBUG=false
APP_KEY=base64:your_32_character_secret_key

# Database Security
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=production_db
DB_USERNAME=secure_user
DB_PASSWORD=very_secure_password

# Session Security
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=strict

# Cache Security
CACHE_DRIVER=redis
REDIS_PASSWORD=redis_secure_password
```

## 📊 Monitoring & Logging

### Application Monitoring
```bash
# Install monitoring tools
composer require laravel/telescope
composer require spatie/laravel-health

# Setup log monitoring
tail -f storage/logs/laravel.log | grep ERROR
tail -f storage/logs/worker.log
tail -f /var/log/mysql/slow.log
```

### Performance Monitoring
```bash
# Database performance
SHOW PROCESSLIST;
SHOW ENGINE INNODB STATUS;
SELECT * FROM information_schema.INNODB_METRICS;

# System monitoring
htop
iotop
nethogs
df -h
free -h
```

## 🚀 Deployment Process

### 1. Initial Deployment
```bash
# Clone repository
git clone https://github.com/your-repo/bhavitech-email-system.git
cd bhavitech-email-system

# Setup environment
cp .env.production .env
php artisan key:generate

# Install dependencies
composer install --no-dev --optimize-autoloader
npm install && npm run production

# Database setup
php artisan migrate --force
php artisan db:seed --class=ProductionSeeder

# Optimize application
php artisan optimize
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Start services
docker-compose up -d
```

### 2. Zero-Downtime Deployment
```bash
#!/bin/bash
# deploy.sh

# Pull latest code
git pull origin main

# Install dependencies
composer install --no-dev --optimize-autoloader

# Run migrations
php artisan migrate --force

# Clear and rebuild cache
php artisan optimize:clear
php artisan optimize

# Restart queue workers
php artisan queue:restart

# Reload PHP-FPM
sudo systemctl reload php8.2-fpm

echo "Deployment completed successfully!"
```

## 📈 Scaling Strategies

### Horizontal Scaling
```yaml
# Load Balancer Configuration
upstream app_servers {
    server app1:80 weight=3;
    server app2:80 weight=3;
    server app3:80 weight=2;
}

# Database Read Replicas
DB_READ_HOST_1=read-replica-1.mysql.com
DB_READ_HOST_2=read-replica-2.mysql.com
DB_WRITE_HOST=master.mysql.com
```

### Auto-Scaling with Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: email-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: email-system
  template:
    metadata:
      labels:
        app: email-system
    spec:
      containers:
      - name: app
        image: your-registry/email-system:latest
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: email-system-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: email-system
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

## 🔧 Maintenance & Backup

### Automated Backup Script
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

# Database backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/db_$DATE.sql.gz

# File backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz storage/ public/uploads/

# Upload to cloud storage
aws s3 cp $BACKUP_DIR/db_$DATE.sql.gz s3://your-backup-bucket/
aws s3 cp $BACKUP_DIR/files_$DATE.tar.gz s3://your-backup-bucket/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

### Health Check Endpoints
```php
// routes/web.php
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'database' => DB::connection()->getPdo() ? 'connected' : 'disconnected',
        'redis' => Redis::ping() ? 'connected' : 'disconnected',
        'queue' => Queue::size() < 10000 ? 'normal' : 'high',
        'timestamp' => now()->toISOString(),
    ]);
});
```

## 📞 Production Support

### Emergency Contacts
- **System Administrator**: <EMAIL>
- **Database Administrator**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **24/7 Support**: +91 **********

### Troubleshooting Commands
```bash
# Check application status
php artisan about
php artisan health:check

# Check queue status
php artisan queue:monitor
php artisan queue:failed

# Check database connections
php artisan tinker --execute="DB::connection()->getPdo();"

# Check disk space
df -h
du -sh storage/

# Check memory usage
free -h
ps aux --sort=-%mem | head
```

---

**🎉 Your production system is now ready to handle 1 crore emails efficiently and reliably!**
