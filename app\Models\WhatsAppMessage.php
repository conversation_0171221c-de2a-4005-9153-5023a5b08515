<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WhatsAppMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'whatsapp_number_id',
        'recipient_phone',
        'recipient_name',
        'message_type',
        'message_content',
        'template_data',
        'media_data',
        'interactive_data',
        'status',
        'whatsapp_message_id',
        'sent_at',
        'delivered_at',
        'read_at',
        'failure_reason',
        'campaign_id',
        'lead_id',
        'is_automated',
        'cost',
        'webhook_data',
    ];

    protected $casts = [
        'template_data' => 'array',
        'media_data' => 'array',
        'interactive_data' => 'array',
        'webhook_data' => 'array',
        'is_automated' => 'boolean',
        'cost' => 'decimal:4',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'read_at' => 'datetime',
    ];

    // Relationships
    public function whatsappNumber(): BelongsTo
    {
        return $this->belongsTo(WhatsAppNumber::class, 'whatsapp_number_id');
    }

    public function lead(): BelongsTo
    {
        return $this->belongsTo(Lead::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeByCampaign($query, $campaignId)
    {
        return $query->where('campaign_id', $campaignId);
    }

    public function scopeAutomated($query)
    {
        return $query->where('is_automated', true);
    }

    // Methods
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    public function isSent(): bool
    {
        return in_array($this->status, ['sent', 'delivered', 'read']);
    }

    public function isDelivered(): bool
    {
        return in_array($this->status, ['delivered', 'read']);
    }

    public function isRead(): bool
    {
        return $this->status === 'read';
    }

    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    public function markAsSent(string $whatsappMessageId): void
    {
        $this->update([
            'status' => 'sent',
            'whatsapp_message_id' => $whatsappMessageId,
            'sent_at' => now(),
        ]);
    }

    public function markAsDelivered(): void
    {
        $this->update([
            'status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    public function markAsRead(): void
    {
        $this->update([
            'status' => 'read',
            'read_at' => now(),
        ]);
    }

    public function markAsFailed(string $reason): void
    {
        $this->update([
            'status' => 'failed',
            'failure_reason' => $reason,
        ]);
    }

    public function getFormattedPhone(): string
    {
        // Ensure phone number is in international format
        $phone = preg_replace('/[^0-9]/', '', $this->recipient_phone);

        if (!str_starts_with($phone, '91') && strlen($phone) === 10) {
            $phone = '91' . $phone; // Add India country code
        }

        return $phone;
    }
}
