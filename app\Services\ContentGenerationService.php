<?php

namespace App\Services;

use App\Models\SocialMediaPost;
use App\Models\BusinessSetting;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ContentGenerationService
{
    protected array $contentTemplates = [
        'promotional' => [
            'templates' => [
                '🚀 Transform your business with our {service}! Get a free consultation today. #BhavitechSolutions #DigitalTransformation',
                '✨ Ready to take your {business_type} to the next level? Our {service} experts are here to help! Contact us now. #WebDevelopment #Growth',
                '💡 Did you know? {statistic} That\'s why you need professional {service}. Let\'s discuss your project! #TechFacts #Innovation',
            ],
            'variables' => ['service', 'business_type', 'statistic'],
        ],
        'educational' => [
            'templates' => [
                '📚 Tech Tip Tuesday: {tip_title}\n\n{tip_content}\n\nNeed help implementing this? We\'re here to assist! #TechTips #Learning',
                '🎯 Best Practice Alert: {practice_title}\n\n{practice_description}\n\nWant to know more? Drop us a message! #BestPractices #WebDev',
                '💭 Industry Insight: {insight_title}\n\n{insight_content}\n\nStay ahead with Bhavitech! #IndustryNews #TechTrends',
            ],
            'variables' => ['tip_title', 'tip_content', 'practice_title', 'practice_description', 'insight_title', 'insight_content'],
        ],
        'behind_the_scenes' => [
            'templates' => [
                '👨‍💻 Behind the scenes at Bhavitech: {activity_description}\n\nOur team is passionate about delivering excellence! #TeamWork #BehindTheScenes',
                '🏢 Office life at Bhavitech: {office_activity}\n\nWhere innovation meets dedication! #OfficeLife #TeamCulture',
                '⚡ Development in action: {development_activity}\n\nCrafting digital solutions with precision! #Development #Innovation',
            ],
            'variables' => ['activity_description', 'office_activity', 'development_activity'],
        ],
        'client_success' => [
            'templates' => [
                '🎉 Success Story: We helped {client_type} achieve {achievement}!\n\n{success_details}\n\nReady for your success story? #ClientSuccess #Results',
                '⭐ Client Spotlight: {client_name} saw {improvement} after working with us!\n\n{testimonial}\n\n#ClientTestimonial #Success',
                '📈 Case Study: How we {solution_provided} for {industry_type}\n\n{case_study_summary}\n\n#CaseStudy #Results',
            ],
            'variables' => ['client_type', 'achievement', 'success_details', 'client_name', 'improvement', 'testimonial', 'solution_provided', 'industry_type', 'case_study_summary'],
        ],
        'seasonal' => [
            'templates' => [
                '🎊 {season} Special: {offer_description}\n\nLimited time offer! Contact us before {deadline}. #SpecialOffer #{season}',
                '🌟 {holiday} Greetings from Team Bhavitech!\n\n{holiday_message}\n\nWishing you success and prosperity! #{holiday}Wishes',
                '📅 {month} Update: {monthly_highlight}\n\n{update_details}\n\nStay tuned for more updates! #MonthlyUpdate',
            ],
            'variables' => ['season', 'offer_description', 'deadline', 'holiday', 'holiday_message', 'month', 'monthly_highlight', 'update_details'],
        ],
    ];

    protected array $hashtagSets = [
        'web_development' => ['#WebDevelopment', '#WebDesign', '#ResponsiveDesign', '#UserExperience', '#WebDeveloper'],
        'mobile_development' => ['#MobileApp', '#AppDevelopment', '#iOS', '#Android', '#MobileDeveloper'],
        'digital_marketing' => ['#DigitalMarketing', '#SEO', '#SocialMediaMarketing', '#OnlineMarketing', '#MarketingStrategy'],
        'graphic_design' => ['#GraphicDesign', '#LogoDesign', '#BrandIdentity', '#CreativeDesign', '#VisualDesign'],
        'general' => ['#Bhavitech', '#TechSolutions', '#Innovation', '#DigitalTransformation', '#TechExperts'],
    ];

    public function generateContent(array $parameters): array
    {
        $contentType = $parameters['type'] ?? 'promotional';
        $service = $parameters['service'] ?? 'web development';
        $platform = $parameters['platform'] ?? 'facebook';
        
        $template = $this->selectTemplate($contentType);
        $content = $this->populateTemplate($template, $parameters);
        $hashtags = $this->generateHashtags($service, $platform);
        
        return [
            'content' => $content,
            'hashtags' => $hashtags,
            'suggested_media' => $this->suggestMedia($contentType, $service),
            'optimal_time' => $this->getOptimalPostingTime($platform),
            'engagement_tips' => $this->getEngagementTips($contentType),
        ];
    }

    protected function selectTemplate(string $contentType): string
    {
        $templates = $this->contentTemplates[$contentType]['templates'] ?? $this->contentTemplates['promotional']['templates'];
        return $templates[array_rand($templates)];
    }

    protected function populateTemplate(string $template, array $parameters): string
    {
        $defaultValues = [
            'service' => $parameters['service'] ?? 'digital solutions',
            'business_type' => $parameters['business_type'] ?? 'business',
            'statistic' => $this->getRandomStatistic(),
            'tip_title' => $this->getRandomTipTitle(),
            'tip_content' => $this->getRandomTipContent(),
            'practice_title' => $this->getRandomPracticeTitle(),
            'practice_description' => $this->getRandomPracticeDescription(),
            'insight_title' => $this->getRandomInsightTitle(),
            'insight_content' => $this->getRandomInsightContent(),
            'activity_description' => $this->getRandomActivityDescription(),
            'office_activity' => $this->getRandomOfficeActivity(),
            'development_activity' => $this->getRandomDevelopmentActivity(),
            'client_type' => $parameters['client_type'] ?? 'local business',
            'achievement' => $parameters['achievement'] ?? 'increase online visibility',
            'success_details' => $parameters['success_details'] ?? 'Through strategic digital marketing and modern web design',
            'season' => $this->getCurrentSeason(),
            'offer_description' => $this->getSeasonalOffer(),
            'deadline' => now()->addDays(30)->format('M d'),
            'holiday' => $this->getUpcomingHoliday(),
            'holiday_message' => $this->getHolidayMessage(),
            'month' => now()->format('F'),
            'monthly_highlight' => $this->getMonthlyHighlight(),
            'update_details' => $this->getUpdateDetails(),
        ];

        // Merge with provided parameters
        $values = array_merge($defaultValues, $parameters);

        // Replace placeholders
        foreach ($values as $key => $value) {
            $template = str_replace("{{$key}}", $value, $template);
        }

        return $template;
    }

    protected function generateHashtags(string $service, string $platform): array
    {
        $serviceKey = str_replace([' ', '-'], '_', strtolower($service));
        $serviceHashtags = $this->hashtagSets[$serviceKey] ?? $this->hashtagSets['general'];
        $generalHashtags = $this->hashtagSets['general'];
        
        $hashtags = array_merge(
            array_slice($serviceHashtags, 0, 3),
            array_slice($generalHashtags, 0, 2)
        );

        // Platform-specific hashtag limits
        $limits = [
            'instagram' => 30,
            'twitter' => 2,
            'facebook' => 5,
            'linkedin' => 3,
        ];

        $limit = $limits[$platform] ?? 5;
        return array_slice($hashtags, 0, $limit);
    }

    protected function suggestMedia(string $contentType, string $service): array
    {
        $mediaSuggestions = [
            'promotional' => [
                'type' => 'image',
                'suggestions' => [
                    'Service showcase graphic',
                    'Before/after comparison',
                    'Call-to-action banner',
                    'Team working photo',
                ],
            ],
            'educational' => [
                'type' => 'carousel',
                'suggestions' => [
                    'Step-by-step infographic',
                    'Tips carousel',
                    'Process diagram',
                    'Screenshot tutorial',
                ],
            ],
            'behind_the_scenes' => [
                'type' => 'video',
                'suggestions' => [
                    'Office tour video',
                    'Development process timelapse',
                    'Team introduction',
                    'Workspace photos',
                ],
            ],
            'client_success' => [
                'type' => 'image',
                'suggestions' => [
                    'Client testimonial graphic',
                    'Results infographic',
                    'Before/after screenshots',
                    'Success metrics chart',
                ],
            ],
        ];

        return $mediaSuggestions[$contentType] ?? $mediaSuggestions['promotional'];
    }

    protected function getOptimalPostingTime(string $platform): string
    {
        $optimalTimes = [
            'facebook' => '2:00 PM',
            'instagram' => '11:00 AM',
            'linkedin' => '12:00 PM',
            'twitter' => '9:00 AM',
        ];

        return $optimalTimes[$platform] ?? '12:00 PM';
    }

    protected function getEngagementTips(string $contentType): array
    {
        $tips = [
            'promotional' => [
                'Include a clear call-to-action',
                'Use eye-catching visuals',
                'Ask questions to encourage comments',
                'Share customer testimonials',
            ],
            'educational' => [
                'Break down complex topics',
                'Use numbered lists or bullet points',
                'Encourage sharing for others to learn',
                'Ask followers about their experiences',
            ],
            'behind_the_scenes' => [
                'Show authentic moments',
                'Tag team members',
                'Use Stories for real-time updates',
                'Encourage team interaction',
            ],
            'client_success' => [
                'Include specific metrics',
                'Tag the client (with permission)',
                'Use before/after visuals',
                'Encourage similar businesses to reach out',
            ],
        ];

        return $tips[$contentType] ?? $tips['promotional'];
    }

    // Helper methods for generating random content
    protected function getRandomStatistic(): string
    {
        $stats = [
            '75% of users judge a company\'s credibility based on website design',
            '88% of online consumers research before making a purchase',
            'Mobile traffic accounts for over 50% of web traffic',
            'Page load speed affects 40% of user bounce rates',
        ];

        return $stats[array_rand($stats)];
    }

    protected function getRandomTipTitle(): string
    {
        $titles = [
            'Optimize Your Website Speed',
            'Improve Mobile Responsiveness',
            'Boost Your SEO Rankings',
            'Enhance User Experience',
            'Secure Your Website',
        ];

        return $titles[array_rand($titles)];
    }

    protected function getRandomTipContent(): string
    {
        $content = [
            'Compress images and use CDN for faster loading times.',
            'Ensure your site works perfectly on all devices.',
            'Use relevant keywords and create quality content.',
            'Simplify navigation and improve page layout.',
            'Install SSL certificates and keep software updated.',
        ];

        return $content[array_rand($content)];
    }

    protected function getRandomPracticeTitle(): string
    {
        $practices = [
            'Regular Website Backups',
            'User-Centered Design',
            'Clean Code Standards',
            'Performance Monitoring',
            'Security Best Practices',
        ];

        return $practices[array_rand($practices)];
    }

    protected function getRandomPracticeDescription(): string
    {
        $descriptions = [
            'Always maintain regular backups to protect your data and ensure quick recovery.',
            'Design with your users in mind - prioritize usability and accessibility.',
            'Write clean, maintainable code that follows industry standards.',
            'Monitor your website performance and optimize continuously.',
            'Implement security measures to protect against threats.',
        ];

        return $descriptions[array_rand($descriptions)];
    }

    protected function getRandomInsightTitle(): string
    {
        $insights = [
            'The Rise of Progressive Web Apps',
            'AI Integration in Web Development',
            'Voice Search Optimization',
            'Sustainable Web Design',
            'Micro-Interactions in UX',
        ];

        return $insights[array_rand($insights)];
    }

    protected function getRandomInsightContent(): string
    {
        $content = [
            'PWAs are bridging the gap between web and mobile apps.',
            'AI is revolutionizing how we build and interact with websites.',
            'Voice search is changing SEO strategies significantly.',
            'Eco-friendly design practices are becoming essential.',
            'Small animations enhance user engagement dramatically.',
        ];

        return $content[array_rand($content)];
    }

    protected function getRandomActivityDescription(): string
    {
        $activities = [
            'Our developers collaborating on a complex e-commerce solution',
            'Design team brainstorming creative concepts for a client project',
            'Testing and debugging to ensure perfect functionality',
            'Client consultation meeting to understand requirements',
        ];

        return $activities[array_rand($activities)];
    }

    protected function getRandomOfficeActivity(): string
    {
        $activities = [
            'Team lunch and knowledge sharing session',
            'Friday evening team building activities',
            'Morning standup meeting with coffee',
            'Celebrating project completion with the team',
        ];

        return $activities[array_rand($activities)];
    }

    protected function getRandomDevelopmentActivity(): string
    {
        $activities = [
            'Building responsive layouts with modern CSS',
            'Implementing secure payment gateways',
            'Optimizing database queries for better performance',
            'Creating intuitive user interfaces',
        ];

        return $activities[array_rand($activities)];
    }

    protected function getCurrentSeason(): string
    {
        $month = now()->month;
        
        return match (true) {
            in_array($month, [12, 1, 2]) => 'Winter',
            in_array($month, [3, 4, 5]) => 'Spring',
            in_array($month, [6, 7, 8]) => 'Summer',
            in_array($month, [9, 10, 11]) => 'Autumn',
        };
    }

    protected function getSeasonalOffer(): string
    {
        $offers = [
            'Get 20% off on website development projects',
            'Free SEO audit with any digital marketing package',
            'Complimentary mobile app consultation',
            'Special discount on logo design services',
        ];

        return $offers[array_rand($offers)];
    }

    protected function getUpcomingHoliday(): string
    {
        // This would be more sophisticated in production
        return 'Diwali';
    }

    protected function getHolidayMessage(): string
    {
        return 'May this festival of lights illuminate your path to success and prosperity!';
    }

    protected function getMonthlyHighlight(): string
    {
        $highlights = [
            'Completed 15+ successful projects',
            'Launched our new mobile app development service',
            'Achieved 99.9% client satisfaction rate',
            'Expanded our team with talented developers',
        ];

        return $highlights[array_rand($highlights)];
    }

    protected function getUpdateDetails(): string
    {
        return 'We continue to innovate and deliver exceptional digital solutions for our clients.';
    }

    public function generateContentCalendar(int $days = 30): array
    {
        $calendar = [];
        $contentTypes = ['promotional', 'educational', 'behind_the_scenes', 'client_success'];
        
        for ($i = 0; $i < $days; $i++) {
            $date = now()->addDays($i);
            $contentType = $contentTypes[$i % count($contentTypes)];
            
            $calendar[] = [
                'date' => $date->format('Y-m-d'),
                'day_of_week' => $date->format('l'),
                'content_type' => $contentType,
                'suggested_content' => $this->generateContent([
                    'type' => $contentType,
                    'service' => 'web development',
                    'platform' => 'facebook',
                ]),
                'optimal_time' => $this->getOptimalPostingTime('facebook'),
            ];
        }

        return $calendar;
    }
}
