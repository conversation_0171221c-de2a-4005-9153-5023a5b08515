<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Page Header -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        System Configuration
                    </h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        Configure your 1 crore email management system settings
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    <x-filament::badge color="success">
                        {{ \App\Models\Setting::where('is_active', true)->count() }} Active Settings
                    </x-filament::badge>
                </div>
            </div>
        </div>

        <!-- Settings Form -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <form wire:submit.prevent="save" class="space-y-6">
                {{ $this->form }}

                <!-- Action Buttons -->
                <div class="px-6 py-4 bg-gray-50 dark:bg-gray-700 rounded-b-lg border-t border-gray-200 dark:border-gray-600">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <x-filament::button type="submit" color="primary" icon="heroicon-o-check">
                                Save Settings
                            </x-filament::button>

                            <x-filament::button
                                type="button"
                                color="danger"
                                icon="heroicon-o-arrow-path"
                                wire:click="resetToDefaults"
                                wire:confirm="Are you sure you want to reset all settings to their default values?"
                            >
                                Reset to Defaults
                            </x-filament::button>
                        </div>

                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            Last updated: {{ now()->format('M d, Y H:i') }}
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-envelope class="h-8 w-8 text-blue-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Daily Email Limit</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">
                            {{ \App\Models\Setting::get('email.daily_send_limit', '500') }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-users class="h-8 w-8 text-green-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Import Chunk Size</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">
                            {{ \App\Models\Setting::get('performance.import_chunk_size', '1000') }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-globe-alt class="h-8 w-8 text-purple-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Default Country</p>
                        <p class="text-lg font-bold text-gray-900 dark:text-white">
                            {{ \App\Models\Setting::get('geographic.default_country', 'India') }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-shield-check class="h-8 w-8 text-red-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Security Level</p>
                        <p class="text-lg font-bold text-gray-900 dark:text-white">
                            {{ \App\Models\Setting::get('security.enable_2fa', 'false') === 'true' ? 'High' : 'Standard' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                System Information
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Application Version</p>
                    <p class="text-lg text-gray-900 dark:text-white">1.0.0</p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">PHP Version</p>
                    <p class="text-lg text-gray-900 dark:text-white">{{ PHP_VERSION }}</p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Laravel Version</p>
                    <p class="text-lg text-gray-900 dark:text-white">{{ app()->version() }}</p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Database</p>
                    <p class="text-lg text-gray-900 dark:text-white">MySQL</p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Contacts</p>
                    <p class="text-lg text-gray-900 dark:text-white">{{ number_format(\App\Models\Contact::count()) }}</p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Email Accounts</p>
                    <p class="text-lg text-gray-900 dark:text-white">{{ \App\Models\EmailAccount::where('is_active', true)->count() }}</p>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
