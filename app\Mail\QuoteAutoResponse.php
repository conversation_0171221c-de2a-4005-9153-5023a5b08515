<?php

namespace App\Mail;

use App\Models\Lead;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class QuoteAutoResponse extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct(
        public Lead $lead
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Quote Request Received - Bhavitech will respond within 24 hours',
            to: [$this->lead->email],
            from: [config('mail.from.address', '<EMAIL>'), config('mail.from.name', 'Bhavitech')],
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.customer.quote-auto-response',
            with: [
                'lead' => $this->lead,
                'companyName' => 'Bhavitech',
                'supportEmail' => config('mail.support_email', '<EMAIL>'),
                'supportPhone' => '+91 70108 60889',
                'estimatedResponseTime' => '24 hours',
            ],
        );
    }

    public function attachments(): array
    {
        return [];
    }
}
