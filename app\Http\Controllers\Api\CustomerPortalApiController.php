<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\CustomerPortalService;
use App\Models\Project;
use App\Models\Invoice;
use App\Models\SupportTicket;
use App\Models\CustomerDocument;
use App\Models\CustomerNotification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CustomerPortalApiController extends Controller
{
    public function __construct(
        protected CustomerPortalService $portalService
    ) {
        $this->middleware('auth:sanctum');
    }

    public function dashboard(): JsonResponse
    {
        $customer = Auth::user();
        $dashboardData = $this->portalService->getDashboardData($customer);

        return response()->json([
            'success' => true,
            'data' => $dashboardData,
        ]);
    }

    public function projects(): JsonResponse
    {
        $customer = Auth::user();
        $projects = $this->portalService->getCustomerProjects($customer);

        return response()->json([
            'success' => true,
            'data' => $projects,
        ]);
    }

    public function getProject(Project $project): JsonResponse
    {
        $customer = Auth::user();

        if ($project->customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to project',
            ], 403);
        }

        $projectDetails = $this->portalService->getProjectDetails($customer, $project->id);

        return response()->json([
            'success' => true,
            'data' => $projectDetails,
        ]);
    }

    public function invoices(): JsonResponse
    {
        $customer = Auth::user();
        $invoices = $this->portalService->getRecentInvoices($customer, 50);

        return response()->json([
            'success' => true,
            'data' => $invoices,
        ]);
    }

    public function getInvoice(Invoice $invoice): JsonResponse
    {
        $customer = Auth::user();

        if ($invoice->customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to invoice',
            ], 403);
        }

        $invoiceDetails = $this->portalService->getInvoiceDetails($customer, $invoice->id);

        return response()->json([
            'success' => true,
            'data' => $invoiceDetails,
        ]);
    }

    public function supportTickets(Request $request): JsonResponse
    {
        $customer = Auth::user();

        $filters = $request->only(['status', 'priority', 'category']);
        $tickets = $this->portalService->getSupportTickets($customer, $filters);

        return response()->json([
            'success' => true,
            'data' => $tickets,
        ]);
    }

    public function getSupportTicket(SupportTicket $ticket): JsonResponse
    {
        $customer = Auth::user();

        if ($ticket->customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to ticket',
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $ticket->load(['responses', 'attachments']),
        ]);
    }

    public function createSupportTicket(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'priority' => 'required|in:low,medium,high,urgent',
            'category' => 'required|in:general,technical,billing,feature_request',
        ]);

        $customer = Auth::user();

        try {
            $ticket = $this->portalService->createSupportTicket($customer, $validated);

            return response()->json([
                'success' => true,
                'message' => 'Support ticket created successfully',
                'data' => $ticket,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create support ticket: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function addTicketResponse(Request $request, SupportTicket $ticket): JsonResponse
    {
        $validated = $request->validate([
            'message' => 'required|string',
        ]);

        $customer = Auth::user();

        if ($ticket->customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to ticket',
            ], 403);
        }

        try {
            $this->portalService->addTicketResponse($customer, $ticket, $validated);

            return response()->json([
                'success' => true,
                'message' => 'Response added successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to add response: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function documents(Request $request): JsonResponse
    {
        $customer = Auth::user();

        $filters = $request->only(['category', 'project_id']);
        $documents = $this->portalService->getCustomerDocuments($customer, $filters);

        return response()->json([
            'success' => true,
            'data' => $documents,
        ]);
    }

    public function notifications(): JsonResponse
    {
        $customer = Auth::user();
        $notifications = $this->portalService->getRecentNotifications($customer, 50);

        return response()->json([
            'success' => true,
            'data' => $notifications,
        ]);
    }

    public function markNotificationAsRead(CustomerNotification $notification): JsonResponse
    {
        $customer = Auth::user();

        if ($notification->customer_id !== $customer->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to notification',
            ], 403);
        }

        $this->portalService->markNotificationAsRead($customer, $notification->id);

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read',
        ]);
    }

    public function markAllNotificationsAsRead(): JsonResponse
    {
        $customer = Auth::user();
        $count = $this->portalService->markAllNotificationsAsRead($customer);

        return response()->json([
            'success' => true,
            'message' => "{$count} notifications marked as read",
            'data' => ['count' => $count],
        ]);
    }

    public function getProfile(): JsonResponse
    {
        $customer = Auth::user();

        return response()->json([
            'success' => true,
            'data' => $customer,
        ]);
    }

    public function updateProfile(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'timezone' => 'nullable|string|max:50',
        ]);

        $customer = Auth::user();

        try {
            $updatedCustomer = $this->portalService->updateCustomerProfile($customer, $validated);

            return response()->json([
                'success' => true,
                'message' => 'Profile updated successfully',
                'data' => $updatedCustomer,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update profile: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function changePassword(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8',
        ]);

        $customer = Auth::user();

        try {
            $this->portalService->changePassword(
                $customer,
                $validated['current_password'],
                $validated['new_password']
            );

            return response()->json([
                'success' => true,
                'message' => 'Password changed successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function getActivity(): JsonResponse
    {
        $customer = Auth::user();
        $activity = $this->portalService->getCustomerActivity($customer, 30);

        return response()->json([
            'success' => true,
            'data' => $activity,
        ]);
    }

    public function getQuickStats(): JsonResponse
    {
        $customer = Auth::user();
        $dashboardData = $this->portalService->getDashboardData($customer);

        return response()->json([
            'success' => true,
            'data' => [
                'summary' => $dashboardData['summary'],
                'quick_actions' => $dashboardData['quick_actions'],
                'unread_notifications' => CustomerNotification::where('customer_id', $customer->id)
                    ->where('is_read', false)
                    ->count(),
            ],
        ]);
    }

    public function searchContent(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'query' => 'required|string|min:3',
            'type' => 'nullable|in:projects,invoices,tickets,documents',
        ]);

        $customer = Auth::user();
        $query = $validated['query'];
        $type = $validated['type'] ?? 'all';

        $results = [];

        if ($type === 'all' || $type === 'projects') {
            $projects = Project::where('customer_id', $customer->id)
                ->where(function ($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%");
                })
                ->limit(5)
                ->get();

            $results['projects'] = $projects;
        }

        if ($type === 'all' || $type === 'invoices') {
            $invoices = Invoice::where('customer_id', $customer->id)
                ->where(function ($q) use ($query) {
                    $q->where('invoice_number', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%");
                })
                ->limit(5)
                ->get();

            $results['invoices'] = $invoices;
        }

        if ($type === 'all' || $type === 'tickets') {
            $tickets = SupportTicket::where('customer_id', $customer->id)
                ->where(function ($q) use ($query) {
                    $q->where('title', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%")
                      ->orWhere('ticket_number', 'like', "%{$query}%");
                })
                ->limit(5)
                ->get();

            $results['tickets'] = $tickets;
        }

        if ($type === 'all' || $type === 'documents') {
            $documents = CustomerDocument::where('customer_id', $customer->id)
                ->where(function ($q) use ($query) {
                    $q->where('title', 'like', "%{$query}%")
                      ->orWhere('description', 'like', "%{$query}%")
                      ->orWhere('filename', 'like', "%{$query}%");
                })
                ->limit(5)
                ->get();

            $results['documents'] = $documents;
        }

        return response()->json([
            'success' => true,
            'data' => $results,
            'query' => $query,
        ]);
    }
}
