<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SettingResource\Pages;
use App\Models\Setting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\Tabs;

class SettingResource extends Resource
{
    protected static ?string $model = Setting::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationLabel = 'Settings Database';

    protected static ?string $modelLabel = 'Setting';

    protected static ?string $pluralModelLabel = 'Settings Database';

    protected static ?string $navigationGroup = 'Administration';

    protected static ?int $navigationSort = 99;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Tabs::make('Settings Configuration')
                    ->tabs([
                        Tabs\Tab::make('Basic Information')
                            ->schema([
                                Forms\Components\Section::make('Setting Details')
                                    ->schema([
                                        Forms\Components\TextInput::make('key')
                                            ->required()
                                            ->unique(ignoreRecord: true)
                                            ->maxLength(255)
                                            ->helperText('Unique identifier for this setting (e.g., app.name, email.smtp.host)'),

                                        Forms\Components\Select::make('category')
                                            ->options([
                                                'general' => 'General',
                                                'email' => 'Email Configuration',
                                                'smtp' => 'SMTP Settings',
                                                'performance' => 'Performance',
                                                'security' => 'Security',
                                                'geographic' => 'Geographic',
                                                'localization' => 'Localization',
                                                'api' => 'API Configuration',
                                                'backup' => 'Backup & Recovery',
                                                'monitoring' => 'Monitoring',
                                                'whatsapp' => 'WhatsApp Integration',
                                                'social' => 'Social Media',
                                                'analytics' => 'Analytics',
                                                'maintenance' => 'Maintenance',
                                            ])
                                            ->required()
                                            ->searchable(),

                                        Forms\Components\TextInput::make('group')
                                            ->maxLength(255)
                                            ->helperText('Optional sub-category grouping'),

                                        Forms\Components\Textarea::make('description')
                                            ->maxLength(1000)
                                            ->rows(3)
                                            ->helperText('Description of what this setting controls'),
                                    ])->columns(2),
                            ]),

                        Tabs\Tab::make('Value & Type')
                            ->schema([
                                Forms\Components\Section::make('Setting Value')
                                    ->schema([
                                        Forms\Components\Select::make('type')
                                            ->options([
                                                'string' => 'Text',
                                                'integer' => 'Number (Integer)',
                                                'float' => 'Number (Decimal)',
                                                'boolean' => 'True/False',
                                                'array' => 'Array',
                                                'json' => 'JSON Object',
                                                'encrypted' => 'Encrypted Text',
                                                'email' => 'Email Address',
                                                'url' => 'URL',
                                                'password' => 'Password',
                                            ])
                                            ->required()
                                            ->reactive()
                                            ->afterStateUpdated(fn ($state, callable $set) =>
                                                $set('is_encrypted', $state === 'encrypted' || $state === 'password')
                                            ),

                                        Forms\Components\Textarea::make('value')
                                            ->label('Setting Value')
                                            ->rows(4)
                                            ->helperText('The actual value for this setting'),

                                        Forms\Components\KeyValue::make('options')
                                            ->label('Available Options')
                                            ->helperText('For select/radio type settings, define available options')
                                            ->visible(fn ($get) => in_array($get('type'), ['select', 'radio'])),
                                    ])->columns(1),
                            ]),

                        Tabs\Tab::make('Configuration')
                            ->schema([
                                Forms\Components\Section::make('Setting Configuration')
                                    ->schema([
                                        Forms\Components\Toggle::make('is_active')
                                            ->label('Active')
                                            ->default(true)
                                            ->helperText('Whether this setting is active and should be used'),

                                        Forms\Components\Toggle::make('is_public')
                                            ->label('Public Access')
                                            ->helperText('Can this setting be accessed by frontend/API'),

                                        Forms\Components\Toggle::make('is_encrypted')
                                            ->label('Encrypted Storage')
                                            ->helperText('Store this value encrypted in the database'),

                                        Forms\Components\Toggle::make('requires_restart')
                                            ->label('Requires Restart')
                                            ->helperText('Does changing this setting require application restart'),

                                        Forms\Components\TextInput::make('sort_order')
                                            ->label('Sort Order')
                                            ->numeric()
                                            ->default(0)
                                            ->helperText('Order for displaying settings (lower numbers first)'),
                                    ])->columns(2),

                                Forms\Components\Section::make('Validation & Metadata')
                                    ->schema([
                                        Forms\Components\KeyValue::make('validation_rules')
                                            ->label('Validation Rules')
                                            ->helperText('Laravel validation rules for this setting'),

                                        Forms\Components\KeyValue::make('metadata')
                                            ->label('Additional Metadata')
                                            ->helperText('Any additional configuration or metadata'),
                                    ])->columns(1),
                            ]),
                    ])->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('key')
                    ->label('Setting Key')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                Tables\Columns\BadgeColumn::make('category')
                    ->colors([
                        'primary' => 'general',
                        'success' => 'email',
                        'warning' => 'performance',
                        'danger' => 'security',
                        'info' => 'api',
                    ])
                    ->searchable(),

                Tables\Columns\TextColumn::make('group')
                    ->label('Group')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\BadgeColumn::make('type')
                    ->colors([
                        'secondary' => 'string',
                        'primary' => 'integer',
                        'success' => 'boolean',
                        'warning' => 'array',
                        'danger' => 'encrypted',
                    ]),

                Tables\Columns\TextColumn::make('value')
                    ->label('Current Value')
                    ->limit(50)
                    ->tooltip(function ($record) {
                        if ($record->is_encrypted) {
                            return 'Encrypted value - cannot display';
                        }
                        return $record->value;
                    })
                    ->formatStateUsing(function ($state, $record) {
                        if ($record->is_encrypted) {
                            return '••••••••';
                        }
                        if ($record->type === 'boolean') {
                            return $state ? 'True' : 'False';
                        }
                        if (in_array($record->type, ['array', 'json'])) {
                            return 'Array/Object';
                        }
                        return $state;
                    }),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),

                Tables\Columns\IconColumn::make('is_encrypted')
                    ->label('Encrypted')
                    ->boolean()
                    ->trueIcon('heroicon-o-lock-closed')
                    ->falseIcon('heroicon-o-lock-open')
                    ->trueColor('warning')
                    ->falseColor('gray'),

                Tables\Columns\IconColumn::make('is_public')
                    ->label('Public')
                    ->boolean()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Order')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('last_modified_at')
                    ->label('Last Modified')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('modifier.name')
                    ->label('Modified By')
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'general' => 'General',
                        'email' => 'Email Configuration',
                        'smtp' => 'SMTP Settings',
                        'performance' => 'Performance',
                        'security' => 'Security',
                        'geographic' => 'Geographic',
                        'localization' => 'Localization',
                        'api' => 'API Configuration',
                        'backup' => 'Backup & Recovery',
                        'monitoring' => 'Monitoring',
                        'whatsapp' => 'WhatsApp Integration',
                        'social' => 'Social Media',
                        'analytics' => 'Analytics',
                        'maintenance' => 'Maintenance',
                    ]),

                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'string' => 'Text',
                        'integer' => 'Number (Integer)',
                        'float' => 'Number (Decimal)',
                        'boolean' => 'True/False',
                        'array' => 'Array',
                        'json' => 'JSON Object',
                        'encrypted' => 'Encrypted Text',
                        'email' => 'Email Address',
                        'url' => 'URL',
                        'password' => 'Password',
                    ]),

                Tables\Filters\Filter::make('is_active')
                    ->label('Active Settings')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true)),

                Tables\Filters\Filter::make('is_encrypted')
                    ->label('Encrypted Settings')
                    ->query(fn (Builder $query): Builder => $query->where('is_encrypted', true)),

                Tables\Filters\Filter::make('is_public')
                    ->label('Public Settings')
                    ->query(fn (Builder $query): Builder => $query->where('is_public', true)),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->action(function (Setting $record): void {
                        $duplicate = $record->replicate();
                        $duplicate->key = $record->key . '_copy';
                        $duplicate->save();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),

                    Tables\Actions\BulkAction::make('activate')
                        ->label('Activate Selected')
                        ->icon('heroicon-o-check-circle')
                        ->action(fn ($records) => $records->each->update(['is_active' => true]))
                        ->color('success'),

                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Deactivate Selected')
                        ->icon('heroicon-o-x-circle')
                        ->action(fn ($records) => $records->each->update(['is_active' => false]))
                        ->color('danger'),
                ]),
            ])
            ->defaultSort('category')
            ->defaultSort('sort_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSettings::route('/'),
            'create' => Pages\CreateSetting::route('/create'),
            'edit' => Pages\EditSetting::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }

    public static function getGlobalSearchEloquentQuery(): Builder
    {
        return parent::getGlobalSearchEloquentQuery()->with(['modifier']);
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['key', 'category', 'description', 'value'];
    }

    public static function getGlobalSearchResultDetails($record): array
    {
        return [
            'Category' => $record->category,
            'Type' => $record->type,
            'Group' => $record->group,
        ];
    }
}
