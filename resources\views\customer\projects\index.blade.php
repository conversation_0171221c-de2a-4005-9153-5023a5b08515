@extends('layouts.customer')

@section('title', 'My Projects')

@section('content')
<div class="space-y-6">
    <!-- <PERSON> Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Projects</h1>
                <p class="text-gray-600 mt-1">Track the progress of all your projects with Bhavitech</p>
            </div>
            <div class="flex items-center space-x-3">
                <!-- Filter Dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                        </svg>
                        Filter by Status
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    
                    <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                        <div class="py-1">
                            <a href="{{ route('customer.projects') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">All Projects</a>
                            <a href="{{ route('customer.projects', ['status' => 'planning']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Planning</a>
                            <a href="{{ route('customer.projects', ['status' => 'in_progress']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">In Progress</a>
                            <a href="{{ route('customer.projects', ['status' => 'on_hold']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">On Hold</a>
                            <a href="{{ route('customer.projects', ['status' => 'completed']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Completed</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Projects Grid -->
    @if($projects->count() > 0)
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            @foreach($projects as $project)
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
                <!-- Project Header -->
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ $project->name }}</h3>
                            <p class="text-gray-600 text-sm line-clamp-2">{{ $project->description }}</p>
                        </div>
                        <div class="ml-4">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($project->status === 'completed') bg-green-100 text-green-800
                                @elseif($project->status === 'in_progress') bg-blue-100 text-blue-800
                                @elseif($project->status === 'on_hold') bg-yellow-100 text-yellow-800
                                @elseif($project->status === 'planning') bg-purple-100 text-purple-800
                                @else bg-gray-100 text-gray-800
                                @endif">
                                {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Project Details -->
                <div class="p-6">
                    <!-- Progress Bar -->
                    @if($project->progress_percentage !== null)
                    <div class="mb-4">
                        <div class="flex items-center justify-between text-sm mb-2">
                            <span class="text-gray-600">Progress</span>
                            <span class="font-medium text-gray-900">{{ $project->progress_percentage }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: {{ $project->progress_percentage }}%"></div>
                        </div>
                    </div>
                    @endif

                    <!-- Project Info -->
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">Type:</span>
                            <span class="font-medium text-gray-900 ml-1">{{ ucfirst($project->type ?? 'General') }}</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Priority:</span>
                            <span class="font-medium text-gray-900 ml-1">{{ ucfirst($project->priority ?? 'Medium') }}</span>
                        </div>
                        @if($project->start_date)
                        <div>
                            <span class="text-gray-500">Start Date:</span>
                            <span class="font-medium text-gray-900 ml-1">{{ $project->start_date->format('M d, Y') }}</span>
                        </div>
                        @endif
                        @if($project->end_date)
                        <div>
                            <span class="text-gray-500">Due Date:</span>
                            <span class="font-medium text-gray-900 ml-1">{{ $project->end_date->format('M d, Y') }}</span>
                        </div>
                        @endif
                    </div>

                    <!-- Project Manager -->
                    @if($project->projectManager)
                    <div class="mt-4 pt-4 border-t border-gray-100">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-primary-600">{{ substr($project->projectManager->name, 0, 1) }}</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900">{{ $project->projectManager->name }}</p>
                                <p class="text-xs text-gray-500">Project Manager</p>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Action Button -->
                    <div class="mt-6">
                        <a href="{{ route('customer.projects.show', $project) }}" 
                           class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            View Project Details
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($projects->hasPages())
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            {{ $projects->appends(request()->query())->links() }}
        </div>
        @endif
    @else
        <!-- Empty State -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-12">
            <div class="text-center">
                <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No projects found</h3>
                <p class="mt-2 text-gray-500">You don't have any projects yet. Contact us to get started with your first project!</p>
                <div class="mt-6">
                    <a href="{{ route('contact') }}" 
                       class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Start New Project
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>
@endsection
