<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class ContactList extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'type',
        'status',
        'segment_rules',
        'double_opt_in',
        'auto_cleanup',
        'cleanup_frequency',
        'tags',
        'created_by',
    ];

    protected $casts = [
        'segment_rules' => 'array',
        'tags' => 'array',
        'double_opt_in' => 'boolean',
        'auto_cleanup' => 'boolean',
    ];

    // Relationships
    public function contacts(): BelongsToMany
    {
        return $this->belongsToMany(Contact::class, 'contact_list_members')
            ->withTimestamps();
    }

    public function subscribedContacts(): BelongsToMany
    {
        return $this->contacts()->where('contacts.status', 'subscribed');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }



    public function emailCampaigns(): HasMany
    {
        return $this->hasMany(EmailCampaign::class, 'contact_list_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeStatic($query)
    {
        return $query->where('type', 'static');
    }

    public function scopeDynamic($query)
    {
        return $query->where('type', 'dynamic');
    }

    // Methods
    public function addContact(Contact $contact): void
    {
        if (!$this->contacts()->where('contact_id', $contact->id)->exists()) {
            $this->contacts()->attach($contact->id);
        }
    }

    public function removeContact(Contact $contact): void
    {
        $this->contacts()->detach($contact->id);
    }

    public function addContacts(array $contactIds): void
    {
        $this->contacts()->syncWithoutDetaching($contactIds);
    }

    public function refreshDynamicList(): void
    {
        if ($this->type !== 'dynamic' || empty($this->segment_rules)) {
            return;
        }

        $query = Contact::query();

        foreach ($this->segment_rules as $rule) {
            $field = $rule['field'];
            $operator = $rule['operator'];
            $value = $rule['value'] ?? null;

            switch ($operator) {
                case 'equals':
                    $query->where($field, $value);
                    break;
                case 'not_equals':
                    $query->where($field, '!=', $value);
                    break;
                case 'contains':
                    $query->where($field, 'like', "%{$value}%");
                    break;
                case 'not_contains':
                    $query->where($field, 'not like', "%{$value}%");
                    break;
                case 'greater_than':
                    $query->where($field, '>', $value);
                    break;
                case 'less_than':
                    $query->where($field, '<', $value);
                    break;
                case 'is_null':
                    $query->whereNull($field);
                    break;
                case 'is_not_null':
                    $query->whereNotNull($field);
                    break;
                case 'in_last_days':
                    $query->where($field, '>=', now()->subDays((int) $value));
                    break;
            }
        }

        $matchingContacts = $query->pluck('id')->toArray();
        $this->contacts()->sync($matchingContacts);
    }

    public function getContactsCount(): int
    {
        return $this->contacts()->count();
    }

    public function getSubscribedContactsCount(): int
    {
        return $this->subscribedContacts()->count();
    }

    public function getEngagementRate(): float
    {
        $totalContacts = $this->getContactsCount();
        if ($totalContacts === 0) {
            return 0;
        }

        $engagedContacts = $this->contacts()
            ->where('engagement_score', '>=', 50)
            ->count();

        return ($engagedContacts / $totalContacts) * 100;
    }

    public function cleanupList(): void
    {
        if (!$this->auto_cleanup) {
            return;
        }

        // Remove unsubscribed contacts
        $this->contacts()
            ->wherePivot('status', 'unsubscribed')
            ->detach();

        // Remove bounced contacts
        $this->contacts()
            ->wherePivot('status', 'bounced')
            ->detach();

        // Remove complained contacts
        $this->contacts()
            ->wherePivot('status', 'complained')
            ->detach();
    }

    public function exportContacts(array $fields = []): array
    {
        $defaultFields = [
            'first_name',
            'last_name',
            'email',
            'phone',
            'company',
            'status',
            'engagement_score',
        ];

        $fields = empty($fields) ? $defaultFields : $fields;

        return $this->contacts()
            ->select($fields)
            ->get()
            ->toArray();
    }

    public function importContacts(array $contactsData): array
    {
        $imported = 0;
        $skipped = 0;
        $errors = [];

        foreach ($contactsData as $index => $contactData) {
            try {
                // Validate required fields
                if (empty($contactData['email'])) {
                    $errors[] = "Row {$index}: Email is required";
                    $skipped++;
                    continue;
                }

                // Check if contact already exists
                $contact = Contact::firstOrCreate(
                    ['email' => $contactData['email']],
                    $contactData
                );

                // Add to this list
                $this->addContact($contact);
                $imported++;

            } catch (\Exception $e) {
                $errors[] = "Row {$index}: " . $e->getMessage();
                $skipped++;
            }
        }

        return [
            'imported' => $imported,
            'skipped' => $skipped,
            'errors' => $errors,
        ];
    }

    public function duplicate(string $newName): self
    {
        $duplicate = $this->replicate();
        $duplicate->name = $newName;
        $duplicate->save();

        // Copy contacts if it's a static list
        if ($this->type === 'static') {
            $contactIds = $this->contacts()->pluck('contact_id')->toArray();
            $duplicate->contacts()->sync($contactIds);
        }

        return $duplicate;
    }

    public function getGrowthRate(int $days = 30): float
    {
        $currentCount = $this->getContactsCount();
        $previousCount = $this->contacts()
            ->wherePivot('created_at', '<=', now()->subDays($days))
            ->count();

        if ($previousCount === 0) {
            return $currentCount > 0 ? 100 : 0;
        }

        return (($currentCount - $previousCount) / $previousCount) * 100;
    }

    public function getTopSources(): array
    {
        return $this->contacts()
            ->selectRaw('source, COUNT(*) as count')
            ->groupBy('source')
            ->orderByDesc('count')
            ->limit(5)
            ->pluck('count', 'source')
            ->toArray();
    }

    public function getEngagementDistribution(): array
    {
        return [
            'high' => $this->contacts()->where('engagement_score', '>=', 80)->count(),
            'medium' => $this->contacts()->whereBetween('engagement_score', [50, 79])->count(),
            'low' => $this->contacts()->where('engagement_score', '<', 50)->count(),
        ];
    }
}
