<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contacts', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('last_name')->nullable();
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->string('company')->nullable();
            $table->string('job_title')->nullable();
            $table->string('industry')->nullable();
            $table->text('address')->nullable();
            $table->string('city')->nullable();
            $table->string('state')->nullable();
            $table->string('country')->default('India');
            $table->string('source')->default('manual'); // manual, import, api, lead_conversion
            $table->json('tags')->nullable(); // For categorization
            $table->json('custom_fields')->nullable(); // Additional custom data
            $table->json('social_profiles')->nullable(); // LinkedIn, Facebook, etc.
            $table->integer('engagement_score')->default(0);
            $table->timestamp('last_interaction_at')->nullable();
            $table->boolean('is_subscribed')->default(true);
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['email', 'is_active']);
            $table->index(['company', 'is_active']);
            $table->index('engagement_score');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contacts');
    }
};
