<?php

namespace App\Services;

use App\Models\Lead;
use App\Models\LeadScoringRule;
use App\Models\AnalyticsEvent;
use Illuminate\Support\Facades\Log;

class LeadScoringService
{
    public function calculateScore(Lead $lead): int
    {
        $score = 0;
        $rules = LeadScoringRule::active()->ordered()->get();

        foreach ($rules as $rule) {
            $ruleScore = $this->applyRule($rule, $lead);
            $score += $ruleScore;

            Log::debug('Lead scoring rule applied', [
                'lead_id' => $lead->id,
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
                'score_added' => $ruleScore,
                'total_score' => $score,
            ]);
        }

        // Add behavioral scoring
        $behavioralScore = $this->calculateBehavioralScore($lead);
        $score += $behavioralScore;

        // Add engagement scoring
        $engagementScore = $this->calculateEngagementScore($lead);
        $score += $engagementScore;

        return max(0, min(100, $score)); // Ensure score is between 0-100
    }

    protected function applyRule(LeadScoringRule $rule, Lead $lead): int
    {
        return $rule->evaluateForLead($lead);
    }

    protected function calculateBehavioralScore(Lead $lead): int
    {
        $score = 0;

        // Check for multiple form submissions
        $formSubmissions = AnalyticsEvent::where('visitor_id', $lead->email)
            ->where('event_name', 'form_submit')
            ->count();

        if ($formSubmissions > 1) {
            $score += min(20, $formSubmissions * 5); // Max 20 points
        }

        // Check for page views
        $pageViews = AnalyticsEvent::where('visitor_id', $lead->email)
            ->where('event_name', 'page_view')
            ->count();

        if ($pageViews > 5) {
            $score += min(15, ($pageViews - 5) * 2); // Max 15 points
        }

        // Check for service page visits
        $servicePageViews = AnalyticsEvent::where('visitor_id', $lead->email)
            ->where('event_name', 'page_view')
            ->where('page_url', 'like', '%/services/%')
            ->count();

        if ($servicePageViews > 0) {
            $score += min(10, $servicePageViews * 3); // Max 10 points
        }

        return $score;
    }

    protected function calculateEngagementScore(Lead $lead): int
    {
        $score = 0;

        // Check for email opens (if we have email tracking)
        $emailOpens = $lead->interactions()
            ->where('type', 'email_open')
            ->count();

        if ($emailOpens > 0) {
            $score += min(15, $emailOpens * 3); // Max 15 points
        }

        // Check for email clicks
        $emailClicks = $lead->interactions()
            ->where('type', 'email_click')
            ->count();

        if ($emailClicks > 0) {
            $score += min(20, $emailClicks * 5); // Max 20 points
        }

        // Check for WhatsApp responses
        $whatsappResponses = $lead->whatsappMessages()
            ->where('status', 'read')
            ->count();

        if ($whatsappResponses > 0) {
            $score += min(10, $whatsappResponses * 2); // Max 10 points
        }

        return $score;
    }

    public function updateLeadScore(Lead $lead): void
    {
        $newScore = $this->calculateScore($lead);
        $oldScore = $lead->score;

        $lead->update(['score' => $newScore]);

        // Log score change
        Log::info('Lead score updated', [
            'lead_id' => $lead->id,
            'old_score' => $oldScore,
            'new_score' => $newScore,
            'change' => $newScore - $oldScore,
        ]);

        // Trigger automation workflows if score crosses thresholds
        $this->checkScoreThresholds($lead, $oldScore, $newScore);
    }

    protected function checkScoreThresholds(Lead $lead, int $oldScore, int $newScore): void
    {
        $thresholds = [
            80 => 'hot_lead',
            60 => 'warm_lead',
            40 => 'qualified_lead',
        ];

        foreach ($thresholds as $threshold => $status) {
            if ($oldScore < $threshold && $newScore >= $threshold) {
                // Score crossed threshold upward
                $this->triggerScoreThresholdWorkflow($lead, $status, 'increased');
                break;
            } elseif ($oldScore >= $threshold && $newScore < $threshold) {
                // Score crossed threshold downward
                $this->triggerScoreThresholdWorkflow($lead, $status, 'decreased');
                break;
            }
        }
    }

    protected function triggerScoreThresholdWorkflow(Lead $lead, string $status, string $direction): void
    {
        // This would trigger automation workflows
        // Implementation would use the AutomationWorkflow model
        Log::info('Lead score threshold crossed', [
            'lead_id' => $lead->id,
            'status' => $status,
            'direction' => $direction,
            'current_score' => $lead->score,
        ]);
    }

    public function getLeadQuality(int $score): string
    {
        return match (true) {
            $score >= 80 => 'hot',
            $score >= 60 => 'warm',
            $score >= 40 => 'qualified',
            $score >= 20 => 'cold',
            default => 'unqualified',
        };
    }

    public function getScoreInsights(Lead $lead): array
    {
        $insights = [];
        $score = $lead->score;

        if ($score < 20) {
            $insights[] = 'Lead needs more engagement to qualify';
        }

        if (empty($lead->company)) {
            $insights[] = 'Missing company information reduces score';
        }

        if (empty($lead->phone)) {
            $insights[] = 'Phone number would increase lead quality';
        }

        $emailDomain = substr(strrchr($lead->email, "@"), 1);
        if (in_array($emailDomain, ['gmail.com', 'yahoo.com', 'hotmail.com'])) {
            $insights[] = 'Personal email domain - consider business verification';
        }

        return $insights;
    }

    public function bulkUpdateScores(): int
    {
        $updated = 0;
        $leads = Lead::where('status', '!=', 'converted')->get();

        foreach ($leads as $lead) {
            $this->updateLeadScore($lead);
            $updated++;
        }

        Log::info('Bulk lead score update completed', [
            'leads_updated' => $updated,
        ]);

        return $updated;
    }
}
