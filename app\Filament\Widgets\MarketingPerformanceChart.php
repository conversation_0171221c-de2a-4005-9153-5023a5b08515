<?php

namespace App\Filament\Widgets;

use App\Models\EmailCampaign;
use App\Models\WhatsAppCampaign;
use App\Models\SocialMediaPost;
use App\Models\Lead;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\DB;

class MarketingPerformanceChart extends ChartWidget
{
    protected static ?string $heading = 'Marketing Performance Overview';
    protected static ?int $sort = 2;
    protected static ?string $pollingInterval = '60s';

    public ?string $filter = 'last_30_days';

    protected function getFilters(): ?array
    {
        return [
            'last_7_days' => 'Last 7 days',
            'last_30_days' => 'Last 30 days',
            'last_90_days' => 'Last 90 days',
            'last_12_months' => 'Last 12 months',
        ];
    }

    protected function getData(): array
    {
        $period = $this->filter;
        
        switch ($period) {
            case 'last_7_days':
                return $this->getWeeklyData();
            case 'last_30_days':
                return $this->getMonthlyData();
            case 'last_90_days':
                return $this->getQuarterlyData();
            case 'last_12_months':
                return $this->getYearlyData();
            default:
                return $this->getMonthlyData();
        }
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'mode' => 'index',
                    'intersect' => false,
                ],
            ],
            'scales' => [
                'x' => [
                    'display' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Time Period',
                    ],
                ],
                'y' => [
                    'display' => true,
                    'title' => [
                        'display' => true,
                        'text' => 'Count / Rate (%)',
                    ],
                    'beginAtZero' => true,
                ],
            ],
            'interaction' => [
                'mode' => 'nearest',
                'axis' => 'x',
                'intersect' => false,
            ],
        ];
    }

    protected function getWeeklyData(): array
    {
        $labels = [];
        $leadsData = [];
        $emailOpenRates = [];
        $whatsappDeliveryRates = [];
        $socialEngagement = [];

        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $labels[] = $date->format('M j');

            // Leads generated
            $leadsCount = Lead::whereDate('created_at', $date)->count();
            $leadsData[] = $leadsCount;

            // Email campaign performance
            $emailCampaigns = EmailCampaign::whereDate('created_at', $date)
                ->where('status', 'sent')
                ->get();
            $avgOpenRate = $emailCampaigns->avg('open_rate') ?? 0;
            $emailOpenRates[] = round($avgOpenRate, 2);

            // WhatsApp campaign performance
            $whatsappCampaigns = WhatsAppCampaign::whereDate('created_at', $date)
                ->where('status', 'sent')
                ->get();
            $avgDeliveryRate = $whatsappCampaigns->avg('delivery_rate') ?? 0;
            $whatsappDeliveryRates[] = round($avgDeliveryRate, 2);

            // Social media engagement
            $socialPosts = SocialMediaPost::whereDate('created_at', $date)
                ->where('status', 'published')
                ->get();
            $avgEngagement = $socialPosts->avg('total_engagement') ?? 0;
            $socialEngagement[] = round($avgEngagement, 0);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Leads Generated',
                    'data' => $leadsData,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'tension' => 0.4,
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Email Open Rate (%)',
                    'data' => $emailOpenRates,
                    'borderColor' => 'rgb(16, 185, 129)',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'tension' => 0.4,
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'WhatsApp Delivery Rate (%)',
                    'data' => $whatsappDeliveryRates,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                    'yAxisID' => 'y',
                ],
                [
                    'label' => 'Social Engagement',
                    'data' => $socialEngagement,
                    'borderColor' => 'rgb(168, 85, 247)',
                    'backgroundColor' => 'rgba(168, 85, 247, 0.1)',
                    'tension' => 0.4,
                    'yAxisID' => 'y',
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getMonthlyData(): array
    {
        $labels = [];
        $leadsData = [];
        $emailOpenRates = [];
        $whatsappDeliveryRates = [];
        $socialEngagement = [];
        $conversionRates = [];

        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $labels[] = $date->format('M j');

            // Leads generated
            $leadsCount = Lead::whereDate('created_at', $date)->count();
            $leadsData[] = $leadsCount;

            // Email campaign performance
            $emailCampaigns = EmailCampaign::whereDate('sent_at', $date)
                ->where('status', 'sent')
                ->get();
            $avgOpenRate = $emailCampaigns->avg('open_rate') ?? 0;
            $emailOpenRates[] = round($avgOpenRate, 2);

            // WhatsApp campaign performance
            $whatsappCampaigns = WhatsAppCampaign::whereDate('sent_at', $date)
                ->where('status', 'sent')
                ->get();
            $avgDeliveryRate = $whatsappCampaigns->avg('delivery_rate') ?? 0;
            $whatsappDeliveryRates[] = round($avgDeliveryRate, 2);

            // Social media engagement
            $socialPosts = SocialMediaPost::whereDate('published_at', $date)
                ->where('status', 'published')
                ->get();
            $avgEngagement = $socialPosts->avg('total_engagement') ?? 0;
            $socialEngagement[] = round($avgEngagement, 0);

            // Conversion rate
            $totalLeads = Lead::whereDate('created_at', '<=', $date)->count();
            $convertedLeads = Lead::whereDate('created_at', '<=', $date)
                ->where('status', 'converted')
                ->count();
            $conversionRate = $totalLeads > 0 ? ($convertedLeads / $totalLeads) * 100 : 0;
            $conversionRates[] = round($conversionRate, 2);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Leads Generated',
                    'data' => $leadsData,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Email Open Rate (%)',
                    'data' => $emailOpenRates,
                    'borderColor' => 'rgb(16, 185, 129)',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'WhatsApp Delivery Rate (%)',
                    'data' => $whatsappDeliveryRates,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Social Engagement',
                    'data' => $socialEngagement,
                    'borderColor' => 'rgb(168, 85, 247)',
                    'backgroundColor' => 'rgba(168, 85, 247, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Conversion Rate (%)',
                    'data' => $conversionRates,
                    'borderColor' => 'rgb(245, 158, 11)',
                    'backgroundColor' => 'rgba(245, 158, 11, 0.1)',
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getQuarterlyData(): array
    {
        $labels = [];
        $leadsData = [];
        $emailOpenRates = [];
        $whatsappDeliveryRates = [];
        $socialEngagement = [];

        for ($i = 12; $i >= 0; $i--) {
            $startDate = now()->subWeeks($i)->startOfWeek();
            $endDate = now()->subWeeks($i)->endOfWeek();
            $labels[] = $startDate->format('M j') . ' - ' . $endDate->format('M j');

            // Leads generated
            $leadsCount = Lead::whereBetween('created_at', [$startDate, $endDate])->count();
            $leadsData[] = $leadsCount;

            // Email campaign performance
            $emailCampaigns = EmailCampaign::whereBetween('sent_at', [$startDate, $endDate])
                ->where('status', 'sent')
                ->get();
            $avgOpenRate = $emailCampaigns->avg('open_rate') ?? 0;
            $emailOpenRates[] = round($avgOpenRate, 2);

            // WhatsApp campaign performance
            $whatsappCampaigns = WhatsAppCampaign::whereBetween('sent_at', [$startDate, $endDate])
                ->where('status', 'sent')
                ->get();
            $avgDeliveryRate = $whatsappCampaigns->avg('delivery_rate') ?? 0;
            $whatsappDeliveryRates[] = round($avgDeliveryRate, 2);

            // Social media engagement
            $socialPosts = SocialMediaPost::whereBetween('published_at', [$startDate, $endDate])
                ->where('status', 'published')
                ->get();
            $avgEngagement = $socialPosts->avg('total_engagement') ?? 0;
            $socialEngagement[] = round($avgEngagement, 0);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Weekly Leads',
                    'data' => $leadsData,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Email Open Rate (%)',
                    'data' => $emailOpenRates,
                    'borderColor' => 'rgb(16, 185, 129)',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'WhatsApp Delivery Rate (%)',
                    'data' => $whatsappDeliveryRates,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Social Engagement',
                    'data' => $socialEngagement,
                    'borderColor' => 'rgb(168, 85, 247)',
                    'backgroundColor' => 'rgba(168, 85, 247, 0.1)',
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getYearlyData(): array
    {
        $labels = [];
        $leadsData = [];
        $emailOpenRates = [];
        $whatsappDeliveryRates = [];
        $socialEngagement = [];

        for ($i = 11; $i >= 0; $i--) {
            $startDate = now()->subMonths($i)->startOfMonth();
            $endDate = now()->subMonths($i)->endOfMonth();
            $labels[] = $startDate->format('M Y');

            // Leads generated
            $leadsCount = Lead::whereBetween('created_at', [$startDate, $endDate])->count();
            $leadsData[] = $leadsCount;

            // Email campaign performance
            $emailCampaigns = EmailCampaign::whereBetween('sent_at', [$startDate, $endDate])
                ->where('status', 'sent')
                ->get();
            $avgOpenRate = $emailCampaigns->avg('open_rate') ?? 0;
            $emailOpenRates[] = round($avgOpenRate, 2);

            // WhatsApp campaign performance
            $whatsappCampaigns = WhatsAppCampaign::whereBetween('sent_at', [$startDate, $endDate])
                ->where('status', 'sent')
                ->get();
            $avgDeliveryRate = $whatsappCampaigns->avg('delivery_rate') ?? 0;
            $whatsappDeliveryRates[] = round($avgDeliveryRate, 2);

            // Social media engagement
            $socialPosts = SocialMediaPost::whereBetween('published_at', [$startDate, $endDate])
                ->where('status', 'published')
                ->get();
            $avgEngagement = $socialPosts->avg('total_engagement') ?? 0;
            $socialEngagement[] = round($avgEngagement, 0);
        }

        return [
            'datasets' => [
                [
                    'label' => 'Monthly Leads',
                    'data' => $leadsData,
                    'borderColor' => 'rgb(59, 130, 246)',
                    'backgroundColor' => 'rgba(59, 130, 246, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Email Open Rate (%)',
                    'data' => $emailOpenRates,
                    'borderColor' => 'rgb(16, 185, 129)',
                    'backgroundColor' => 'rgba(16, 185, 129, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'WhatsApp Delivery Rate (%)',
                    'data' => $whatsappDeliveryRates,
                    'borderColor' => 'rgb(34, 197, 94)',
                    'backgroundColor' => 'rgba(34, 197, 94, 0.1)',
                    'tension' => 0.4,
                ],
                [
                    'label' => 'Social Engagement',
                    'data' => $socialEngagement,
                    'borderColor' => 'rgb(168, 85, 247)',
                    'backgroundColor' => 'rgba(168, 85, 247, 0.1)',
                    'tension' => 0.4,
                ],
            ],
            'labels' => $labels,
        ];
    }
}
