<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class AnalyticsEvent extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_name',
        'event_category',
        'event_action',
        'event_label',
        'event_value',
        'session_id',
        'user_id',
        'visitor_id',
        'page_url',
        'page_title',
        'referrer_url',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'device_type',
        'browser',
        'operating_system',
        'country',
        'city',
        'ip_address',
        'custom_properties',
        'event_timestamp',
    ];

    protected $casts = [
        'event_value' => 'decimal:2',
        'custom_properties' => 'array',
        'event_timestamp' => 'datetime',
    ];

    // Scopes
    public function scopeByEvent($query, $eventName)
    {
        return $query->where('event_name', $eventName);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('event_category', $category);
    }

    public function scopeByCampaign($query, $campaign)
    {
        return $query->where('utm_campaign', $campaign);
    }

    public function scopeBySource($query, $source)
    {
        return $query->where('utm_source', $source);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('event_timestamp', today());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('event_timestamp', [
            now()->startOfWeek(),
            now()->endOfWeek()
        ]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('event_timestamp', now()->month)
                    ->whereYear('event_timestamp', now()->year);
    }

    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('event_timestamp', [$startDate, $endDate]);
    }

    public function scopeConversions($query)
    {
        return $query->whereIn('event_name', [
            'form_submit',
            'quote_request',
            'contact_submit',
            'phone_call',
            'email_click'
        ]);
    }

    // Methods
    public static function track(array $data): self
    {
        return static::create(array_merge($data, [
            'event_timestamp' => now(),
            'ip_address' => request()->ip(),
        ]));
    }

    public static function getTopPages($days = 30): \Illuminate\Support\Collection
    {
        return static::where('event_name', 'page_view')
            ->where('event_timestamp', '>=', now()->subDays($days))
            ->selectRaw('page_url, page_title, COUNT(*) as views')
            ->groupBy('page_url', 'page_title')
            ->orderByDesc('views')
            ->limit(10)
            ->get();
    }

    public static function getTrafficSources($days = 30): \Illuminate\Support\Collection
    {
        return static::where('event_timestamp', '>=', now()->subDays($days))
            ->selectRaw('utm_source, utm_medium, COUNT(*) as sessions')
            ->whereNotNull('utm_source')
            ->groupBy('utm_source', 'utm_medium')
            ->orderByDesc('sessions')
            ->get();
    }

    public static function getConversionRate($days = 30): float
    {
        $totalSessions = static::where('event_name', 'page_view')
            ->where('event_timestamp', '>=', now()->subDays($days))
            ->distinct('session_id')
            ->count();

        $conversions = static::conversions()
            ->where('event_timestamp', '>=', now()->subDays($days))
            ->distinct('session_id')
            ->count();

        return $totalSessions > 0 ? ($conversions / $totalSessions) * 100 : 0;
    }

    public static function getDailyStats($days = 30): \Illuminate\Support\Collection
    {
        return static::where('event_timestamp', '>=', now()->subDays($days))
            ->selectRaw('DATE(event_timestamp) as date,
                        COUNT(*) as total_events,
                        COUNT(DISTINCT session_id) as unique_sessions,
                        COUNT(DISTINCT visitor_id) as unique_visitors')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    public function isConversion(): bool
    {
        return in_array($this->event_name, [
            'form_submit',
            'quote_request',
            'contact_submit',
            'phone_call',
            'email_click'
        ]);
    }
}
