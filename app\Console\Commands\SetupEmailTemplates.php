<?php

namespace App\Console\Commands;

use App\Models\EmailTemplate;
use App\Models\User;
use Illuminate\Console\Command;

class SetupEmailTemplates extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bhavitech:setup-email-templates {--force : Force recreation of templates}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup default email templates for Bhavitech marketing campaigns';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up Bhavitech email templates...');

        if ($this->option('force')) {
            $this->warn('Force mode: Deleting existing templates...');
            EmailTemplate::truncate();
        }

        $this->setupEmailTemplates();

        $this->info('✅ Email templates setup completed!');

        return Command::SUCCESS;
    }

    protected function setupEmailTemplates(): void
    {
        $this->info('Creating email templates...');

        $templates = EmailTemplate::getDefaultTemplates();
        $created = 0;

        // Get the first admin user as creator
        $creator = User::first();
        if (!$creator) {
            $this->error('No users found. Please create a user first.');
            return;
        }

        foreach ($templates as $templateData) {
            $existing = EmailTemplate::where('name', $templateData['name'])->first();

            if (!$existing) {
                EmailTemplate::create(array_merge($templateData, [
                    'created_by' => $creator->id,
                ]));
                $created++;
                $this->line("  ✓ Created template: {$templateData['name']}");
            } else {
                $this->line("  - Template already exists: {$templateData['name']}");
            }
        }

        // Create additional Bhavitech-specific templates
        $bhavitechTemplates = [
            [
                'name' => 'Metal Rates Update',
                'subject' => 'Today\'s Gold & Silver Rates - {{current_date}}',
                'content' => $this->getMetalRatesTemplate(),
                'type' => 'newsletter',
                'category' => 'metal_rates',
                'variables' => ['gold_rate', 'silver_rate', 'rate_change'],
                'is_active' => true,
            ],
            [
                'name' => 'Project Completion Notification',
                'subject' => 'Your {{project_type}} Project is Complete!',
                'content' => $this->getProjectCompletionTemplate(),
                'type' => 'transactional',
                'category' => 'project_updates',
                'variables' => ['project_type', 'project_url', 'completion_date'],
                'is_active' => true,
            ],
            [
                'name' => 'Monthly Newsletter',
                'subject' => '{{company_name}} Monthly Update - {{current_date}}',
                'content' => $this->getNewsletterTemplate(),
                'type' => 'newsletter',
                'category' => 'company_updates',
                'variables' => ['featured_projects', 'company_news', 'tech_tips'],
                'is_active' => true,
            ],
        ];

        foreach ($bhavitechTemplates as $templateData) {
            $existing = EmailTemplate::where('name', $templateData['name'])->first();

            if (!$existing) {
                EmailTemplate::create(array_merge($templateData, [
                    'created_by' => $creator->id,
                ]));
                $created++;
                $this->line("  ✓ Created template: {$templateData['name']}");
            }
        }

        $this->info("Created {$created} new email templates.");
    }

    protected function getMetalRatesTemplate(): string
    {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h1 style="color: #f59e0b;">Today\'s Metal Rates - {{current_date}}</h1>

            <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h2 style="color: #92400e; margin-top: 0;">Current Rates</h2>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 10px; border-bottom: 1px solid #d97706;"><strong>Gold (24K)</strong></td>
                        <td style="padding: 10px; border-bottom: 1px solid #d97706; text-align: right;">₹{{gold_rate}}/gram</td>
                    </tr>
                    <tr>
                        <td style="padding: 10px; border-bottom: 1px solid #d97706;"><strong>Silver (999)</strong></td>
                        <td style="padding: 10px; border-bottom: 1px solid #d97706; text-align: right;">₹{{silver_rate}}/gram</td>
                    </tr>
                </table>

                <p style="margin-top: 15px; color: #92400e;">
                    <strong>Change from yesterday:</strong> {{rate_change}}
                </p>
            </div>

            <p>Stay updated with the latest metal rates. Contact us for the best deals on gold and silver jewelry.</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="tel:{{company_phone}}" style="background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Call for Best Rates</a>
            </div>

            <p>Best regards,<br>{{company_name}} Team</p>
        </div>';
    }

    protected function getProjectCompletionTemplate(): string
    {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h1 style="color: #10b981;">🎉 Your Project is Complete!</h1>

            <p>Dear {{recipient_name}},</p>

            <p>We\'re excited to announce that your {{project_type}} project has been completed successfully!</p>

            <div style="background: #d1fae5; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #10b981;">
                <h3>✅ Project Details:</h3>
                <ul>
                    <li><strong>Project Type:</strong> {{project_type}}</li>
                    <li><strong>Completion Date:</strong> {{completion_date}}</li>
                    <li><strong>Project URL:</strong> <a href="{{project_url}}">{{project_url}}</a></li>
                </ul>
            </div>

            <p><strong>What\'s Next?</strong></p>
            <ul>
                <li>Review your completed project</li>
                <li>30 days of free support included</li>
                <li>Training session available upon request</li>
                <li>Maintenance packages available</li>
            </ul>

            <p>Thank you for choosing {{company_name}}. We look forward to working with you again!</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{project_url}}" style="background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">View Your Project</a>
            </div>

            <p>Best regards,<br>The {{company_name}} Team</p>
        </div>';
    }

    protected function getNewsletterTemplate(): string
    {
        return '
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h1 style="color: #2563eb;">{{company_name}} Monthly Update</h1>

            <p>Hello {{recipient_name}},</p>

            <p>Here\'s what\'s been happening at {{company_name}} this month:</p>

            <div style="background: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>🚀 Featured Projects</h3>
                <p>{{featured_projects}}</p>
            </div>

            <div style="background: #fef3c7; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>📢 Company News</h3>
                <p>{{company_news}}</p>
            </div>

            <div style="background: #dbeafe; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>💡 Tech Tips</h3>
                <p>{{tech_tips}}</p>
            </div>

            <p>Thank you for being part of the {{company_name}} community!</p>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{company_website}}" style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Visit Our Website</a>
            </div>

            <p>Best regards,<br>The {{company_name}} Team</p>
        </div>';
    }
}
