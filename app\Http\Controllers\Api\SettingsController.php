<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\SettingsService;
use App\Models\Setting;
use Illuminate\Http\JsonResponse;

class SettingsController extends Controller
{
    /**
     * Get public settings
     */
    public function index(): JsonResponse
    {
        $settings = SettingsService::getPublicSettings();

        return response()->json([
            'success' => true,
            'data' => $settings,
            'meta' => [
                'total' => count($settings),
                'timestamp' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Get settings by category
     */
    public function category(string $category): JsonResponse
    {
        $settings = Setting::where('category', $category)
            ->where('is_public', true)
            ->where('is_active', true)
            ->get()
            ->pluck('value', 'key');

        return response()->json([
            'success' => true,
            'data' => $settings,
            'meta' => [
                'category' => $category,
                'total' => $settings->count(),
                'timestamp' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Get specific setting
     */
    public function show(string $key): JsonResponse
    {
        $setting = Setting::where('key', $key)
            ->where('is_public', true)
            ->where('is_active', true)
            ->first();

        if (!$setting) {
            return response()->json([
                'success' => false,
                'message' => 'Setting not found or not public',
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'key' => $setting->key,
                'value' => $setting->value,
                'category' => $setting->category,
                'type' => $setting->type,
                'description' => $setting->description,
            ]
        ]);
    }

    /**
     * Get system information
     */
    public function systemInfo(): JsonResponse
    {
        $info = SettingsService::getSystemInfo();

        return response()->json([
            'success' => true,
            'data' => $info,
            'meta' => [
                'timestamp' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Get email configuration (public parts only)
     */
    public function emailConfig(): JsonResponse
    {
        $config = [
            'provider' => SettingsService::get('email.default_provider', 'smtp'),
            'daily_limit' => (int) SettingsService::get('email.daily_send_limit', 500),
            'batch_size' => (int) SettingsService::get('email.batch_size', 100),
            'bounce_threshold' => (int) SettingsService::get('email.bounce_threshold', 5),
        ];

        return response()->json([
            'success' => true,
            'data' => $config,
            'meta' => [
                'category' => 'email',
                'timestamp' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Get geographic configuration
     */
    public function geographicConfig(): JsonResponse
    {
        $config = SettingsService::getGeographicConfig();

        return response()->json([
            'success' => true,
            'data' => $config,
            'meta' => [
                'category' => 'geographic',
                'timestamp' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Get performance configuration (public parts only)
     */
    public function performanceConfig(): JsonResponse
    {
        $config = [
            'import_chunk_size' => (int) SettingsService::get('performance.import_chunk_size', 1000),
            'query_cache_ttl' => (int) SettingsService::get('performance.query_cache_ttl', 3600),
            'enable_query_optimization' => SettingsService::get('performance.enable_query_optimization', 'true') === 'true',
        ];

        return response()->json([
            'success' => true,
            'data' => $config,
            'meta' => [
                'category' => 'performance',
                'timestamp' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Get available categories
     */
    public function categories(): JsonResponse
    {
        $categories = Setting::where('is_public', true)
            ->where('is_active', true)
            ->distinct()
            ->pluck('category')
            ->sort()
            ->values();

        return response()->json([
            'success' => true,
            'data' => $categories,
            'meta' => [
                'total' => $categories->count(),
                'timestamp' => now()->toISOString(),
            ]
        ]);
    }
}
