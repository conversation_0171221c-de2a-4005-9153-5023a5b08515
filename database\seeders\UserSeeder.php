<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'phone' => '+91 7010860889',
            'company' => 'Bhavitech',
            'city' => 'Salem',
            'state' => 'Tamil Nadu',
            'country' => 'India',
            'email_verified_at' => now(),
        ]);

        // Create sample customer users
        User::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'customer',
            'phone' => '+91 9876543210',
            'company' => 'Example Corp',
            'city' => 'Chennai',
            'state' => 'Tamil Nadu',
            'country' => 'India',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'customer',
            'phone' => '+91 9876543211',
            'company' => 'Smith Industries',
            'city' => 'Bangalore',
            'state' => 'Karnataka',
            'country' => 'India',
            'email_verified_at' => now(),
        ]);

        // Create sample staff users
        User::create([
            'name' => 'Project Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'user',
            'phone' => '+91 7010860890',
            'company' => 'Bhavitech',
            'city' => 'Salem',
            'state' => 'Tamil Nadu',
            'country' => 'India',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Developer',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'user',
            'phone' => '+91 7010860891',
            'company' => 'Bhavitech',
            'city' => 'Salem',
            'state' => 'Tamil Nadu',
            'country' => 'India',
            'email_verified_at' => now(),
        ]);
    }
}
