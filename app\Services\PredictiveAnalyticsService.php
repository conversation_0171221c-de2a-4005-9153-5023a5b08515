<?php

namespace App\Services;

use App\Models\Lead;
use App\Models\AnalyticsEvent;
use App\Models\LeadInteraction;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PredictiveAnalyticsService
{
    public function predictConversionProbability(Lead $lead): float
    {
        $factors = $this->getConversionFactors($lead);
        $probability = $this->calculateProbability($factors);
        
        return min(100, max(0, $probability));
    }

    protected function getConversionFactors(Lead $lead): array
    {
        return [
            'lead_score' => $this->normalizeScore($lead->score),
            'engagement_level' => $this->calculateEngagementLevel($lead),
            'response_time' => $this->calculateResponseTimeScore($lead),
            'interaction_frequency' => $this->calculateInteractionFrequency($lead),
            'source_quality' => $this->getSourceQualityScore($lead->source),
            'service_match' => $this->getServiceMatchScore($lead),
            'demographic_score' => $this->getDemographicScore($lead),
            'behavioral_score' => $this->getBehavioralScore($lead),
        ];
    }

    protected function normalizeScore(int $score): float
    {
        return $score / 100; // Convert to 0-1 range
    }

    protected function calculateEngagementLevel(Lead $lead): float
    {
        $interactions = $lead->interactions()->count();
        $emailOpens = $lead->interactions()->where('type', 'email_open')->count();
        $emailClicks = $lead->interactions()->where('type', 'email_click')->count();
        $whatsappResponses = $lead->whatsappMessages()->where('status', 'read')->count();

        $engagementScore = ($interactions * 0.1) + ($emailOpens * 0.2) + 
                          ($emailClicks * 0.4) + ($whatsappResponses * 0.3);

        return min(1, $engagementScore / 10); // Normalize to 0-1
    }

    protected function calculateResponseTimeScore(Lead $lead): float
    {
        $firstInteraction = $lead->interactions()
            ->where('is_automated', false)
            ->orderBy('interaction_date')
            ->first();

        if (!$firstInteraction) {
            return 0.5; // Neutral score if no interactions
        }

        $responseTime = $lead->created_at->diffInHours($firstInteraction->interaction_date);

        // Faster response = higher score
        return match (true) {
            $responseTime <= 1 => 1.0,
            $responseTime <= 4 => 0.8,
            $responseTime <= 24 => 0.6,
            $responseTime <= 72 => 0.4,
            default => 0.2,
        };
    }

    protected function calculateInteractionFrequency(Lead $lead): float
    {
        $daysSinceCreated = max(1, $lead->created_at->diffInDays(now()));
        $totalInteractions = $lead->interactions()->count();
        
        $frequency = $totalInteractions / $daysSinceCreated;
        
        return min(1, $frequency / 2); // Normalize, assuming 2 interactions/day is high
    }

    protected function getSourceQualityScore(string $source): float
    {
        $sourceScores = [
            'referral' => 0.9,
            'direct' => 0.8,
            'organic' => 0.7,
            'social' => 0.6,
            'email' => 0.5,
            'paid' => 0.4,
            'other' => 0.3,
        ];

        return $sourceScores[$source] ?? 0.3;
    }

    protected function getServiceMatchScore(Lead $lead): float
    {
        if (!$lead->service_interest) {
            return 0.5;
        }

        // High-value services get higher scores
        $serviceScores = [
            'web-development' => 0.8,
            'mobile-development' => 0.9,
            'digital-marketing' => 0.7,
            'graphic-design' => 0.6,
        ];

        return $serviceScores[$lead->service_interest] ?? 0.5;
    }

    protected function getDemographicScore(Lead $lead): float
    {
        $score = 0;

        // Company presence
        if ($lead->company) {
            $score += 0.3;
        }

        // Phone number
        if ($lead->phone) {
            $score += 0.2;
        }

        // Business email domain
        $emailDomain = substr(strrchr($lead->email, "@"), 1);
        if (!in_array($emailDomain, ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'])) {
            $score += 0.3;
        }

        // Message quality
        if ($lead->message && strlen($lead->message) > 50) {
            $score += 0.2;
        }

        return min(1, $score);
    }

    protected function getBehavioralScore(Lead $lead): float
    {
        $pageViews = AnalyticsEvent::where('visitor_id', $lead->email)
            ->where('event_name', 'page_view')
            ->count();

        $servicePageViews = AnalyticsEvent::where('visitor_id', $lead->email)
            ->where('event_name', 'page_view')
            ->where('page_url', 'like', '%/services/%')
            ->count();

        $formSubmissions = AnalyticsEvent::where('visitor_id', $lead->email)
            ->where('event_name', 'form_submit')
            ->count();

        $score = ($pageViews * 0.1) + ($servicePageViews * 0.3) + ($formSubmissions * 0.6);
        
        return min(1, $score / 5); // Normalize
    }

    protected function calculateProbability(array $factors): float
    {
        // Weighted average of all factors
        $weights = [
            'lead_score' => 0.25,
            'engagement_level' => 0.20,
            'response_time' => 0.15,
            'interaction_frequency' => 0.10,
            'source_quality' => 0.10,
            'service_match' => 0.08,
            'demographic_score' => 0.07,
            'behavioral_score' => 0.05,
        ];

        $weightedSum = 0;
        foreach ($factors as $factor => $value) {
            $weight = $weights[$factor] ?? 0;
            $weightedSum += $value * $weight;
        }

        return $weightedSum * 100; // Convert to percentage
    }

    public function predictOptimalContactTime(Lead $lead): array
    {
        $interactions = $lead->interactions()
            ->where('is_automated', false)
            ->get();

        if ($interactions->isEmpty()) {
            return $this->getDefaultOptimalTimes();
        }

        $hourCounts = [];
        $dayOfWeekCounts = [];

        foreach ($interactions as $interaction) {
            $hour = $interaction->interaction_date->hour;
            $dayOfWeek = $interaction->interaction_date->dayOfWeek;

            $hourCounts[$hour] = ($hourCounts[$hour] ?? 0) + 1;
            $dayOfWeekCounts[$dayOfWeek] = ($dayOfWeekCounts[$dayOfWeek] ?? 0) + 1;
        }

        $bestHour = array_keys($hourCounts, max($hourCounts))[0] ?? 10;
        $bestDayOfWeek = array_keys($dayOfWeekCounts, max($dayOfWeekCounts))[0] ?? 1;

        return [
            'best_hour' => $bestHour,
            'best_day_of_week' => $bestDayOfWeek,
            'confidence' => min(100, count($interactions) * 10),
        ];
    }

    protected function getDefaultOptimalTimes(): array
    {
        return [
            'best_hour' => 10, // 10 AM
            'best_day_of_week' => 2, // Tuesday
            'confidence' => 50, // Medium confidence for defaults
        ];
    }

    public function predictChurnRisk(Lead $lead): array
    {
        $daysSinceLastContact = $lead->last_contacted_at 
            ? $lead->last_contacted_at->diffInDays(now())
            : $lead->created_at->diffInDays(now());

        $totalInteractions = $lead->interactions()->count();
        $recentInteractions = $lead->interactions()
            ->where('interaction_date', '>=', now()->subDays(7))
            ->count();

        $riskScore = $this->calculateChurnRisk($daysSinceLastContact, $totalInteractions, $recentInteractions);
        
        return [
            'risk_score' => $riskScore,
            'risk_level' => $this->getRiskLevel($riskScore),
            'days_since_contact' => $daysSinceLastContact,
            'recommended_action' => $this->getRecommendedAction($riskScore),
        ];
    }

    protected function calculateChurnRisk(int $daysSinceContact, int $totalInteractions, int $recentInteractions): float
    {
        $timeRisk = min(100, $daysSinceContact * 5); // 5 points per day
        $engagementRisk = $totalInteractions > 0 ? (1 - ($recentInteractions / $totalInteractions)) * 100 : 100;
        
        return ($timeRisk + $engagementRisk) / 2;
    }

    protected function getRiskLevel(float $riskScore): string
    {
        return match (true) {
            $riskScore >= 80 => 'high',
            $riskScore >= 60 => 'medium',
            $riskScore >= 40 => 'low',
            default => 'very_low',
        };
    }

    protected function getRecommendedAction(float $riskScore): string
    {
        return match (true) {
            $riskScore >= 80 => 'immediate_contact_required',
            $riskScore >= 60 => 'schedule_follow_up',
            $riskScore >= 40 => 'send_nurture_email',
            default => 'continue_monitoring',
        };
    }

    public function getLeadInsights(Lead $lead): array
    {
        return [
            'conversion_probability' => $this->predictConversionProbability($lead),
            'optimal_contact_time' => $this->predictOptimalContactTime($lead),
            'churn_risk' => $this->predictChurnRisk($lead),
            'lead_quality' => app(LeadScoringService::class)->getLeadQuality($lead->score),
            'score_insights' => app(LeadScoringService::class)->getScoreInsights($lead),
        ];
    }
}
