<?php

namespace App\Console\Commands;

use App\Models\BusinessSetting;
use App\Models\User;
use App\Models\Lead;
use App\Services\CustomerPortalService;
use Illuminate\Console\Command;

class SetupCustomerPortal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bhavitech:setup-customer-portal {--force : Force recreation of settings} {--create-sample : Create sample customer accounts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup customer portal system for Bhavitech';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up customer portal system for Bhavitech...');

        if ($this->option('force')) {
            $this->warn('Force mode: Resetting customer portal settings...');
        }

        $this->setupCustomerPortalSettings();

        if ($this->option('create-sample')) {
            $this->createSampleCustomerAccounts();
        }

        $this->info('✅ Customer portal system setup completed!');

        return Command::SUCCESS;
    }

    protected function setupCustomerPortalSettings(): void
    {
        $this->info('Setting up customer portal settings...');

        $settings = [
            [
                'key' => 'customer_portal_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'customer_portal',
                'label' => 'Enable Customer Portal',
                'description' => 'Enable customer portal functionality',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'customer_auto_account_creation',
                'value' => true,
                'type' => 'boolean',
                'group' => 'customer_portal',
                'label' => 'Auto Account Creation',
                'description' => 'Automatically create customer accounts when leads are converted',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'customer_portal_url',
                'value' => '/customer',
                'type' => 'string',
                'group' => 'customer_portal',
                'label' => 'Portal URL',
                'description' => 'Base URL for customer portal',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'customer_welcome_email_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'customer_portal',
                'label' => 'Send Welcome Email',
                'description' => 'Send welcome email when customer account is created',
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'customer_notifications_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'customer_portal',
                'label' => 'Enable Notifications',
                'description' => 'Enable customer notifications system',
                'is_public' => false,
                'sort_order' => 5,
            ],
            [
                'key' => 'customer_document_upload_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'customer_portal',
                'label' => 'Enable Document Upload',
                'description' => 'Allow customers to upload documents',
                'is_public' => false,
                'sort_order' => 6,
            ],
            [
                'key' => 'customer_document_max_size',
                'value' => 20480,
                'type' => 'integer',
                'group' => 'customer_portal',
                'label' => 'Max Document Size (KB)',
                'description' => 'Maximum file size for document uploads',
                'is_public' => false,
                'sort_order' => 7,
            ],
            [
                'key' => 'customer_support_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'customer_portal',
                'label' => 'Enable Support System',
                'description' => 'Enable customer support ticket system',
                'is_public' => false,
                'sort_order' => 8,
            ],
            [
                'key' => 'support_ticket_prefix',
                'value' => 'TKT',
                'type' => 'string',
                'group' => 'customer_portal',
                'label' => 'Support Ticket Prefix',
                'description' => 'Prefix for support ticket numbers',
                'is_public' => false,
                'sort_order' => 9,
            ],
            [
                'key' => 'customer_project_visibility',
                'value' => 'full',
                'type' => 'string',
                'group' => 'customer_portal',
                'label' => 'Project Visibility',
                'description' => 'Level of project information visible to customers',
                'options' => ['basic', 'detailed', 'full'],
                'is_public' => false,
                'sort_order' => 10,
            ],
            [
                'key' => 'customer_invoice_download_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'customer_portal',
                'label' => 'Enable Invoice Download',
                'description' => 'Allow customers to download invoices',
                'is_public' => false,
                'sort_order' => 11,
            ],
            [
                'key' => 'customer_activity_tracking_enabled',
                'value' => true,
                'type' => 'boolean',
                'group' => 'customer_portal',
                'label' => 'Enable Activity Tracking',
                'description' => 'Track customer portal activity',
                'is_public' => false,
                'sort_order' => 12,
            ],
        ];

        $created = 0;
        foreach ($settings as $settingData) {
            $existing = BusinessSetting::where('key', $settingData['key'])->first();

            if (!$existing || $this->option('force')) {
                if ($existing) {
                    $existing->delete();
                }

                BusinessSetting::create($settingData);
                $created++;
                $this->line("  ✓ Created setting: {$settingData['key']}");
            } else {
                $this->line("  - Setting already exists: {$settingData['key']}");
            }
        }

        $this->info("Created {$created} customer portal settings.");
    }

    protected function createSampleCustomerAccounts(): void
    {
        $this->info('Creating sample customer accounts...');

        // Find converted leads that don't have customer accounts
        $convertedLeads = Lead::where('status', 'converted')
            ->whereNull('customer_id')
            ->limit(5)
            ->get();

        if ($convertedLeads->isEmpty()) {
            $this->info('No converted leads found to create customer accounts.');
            return;
        }

        $portalService = app(CustomerPortalService::class);
        $created = 0;

        foreach ($convertedLeads as $lead) {
            try {
                $customer = $portalService->createCustomerAccount($lead, [
                    'created_via' => 'setup_command',
                    'sample_account' => true,
                ]);

                $this->line("  ✓ Created customer account for: {$customer->name} ({$customer->email})");
                $created++;

            } catch (\Exception $e) {
                $this->error("  ✗ Failed to create account for lead {$lead->id}: " . $e->getMessage());
            }
        }

        $this->info("Created {$created} sample customer accounts.");

        if ($created > 0) {
            $this->info('Sample customer login credentials have been sent via email.');
            $this->warn('Note: In production, ensure proper email configuration for welcome emails.');
        }
    }
}
