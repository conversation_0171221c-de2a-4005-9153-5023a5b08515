<?php

namespace App\Mail;

use App\Models\Lead;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ContactAutoResponse extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct(
        public Lead $lead
    ) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Thank you for contacting Bhavitech - We\'ll be in touch soon!',
            to: [$this->lead->email],
            from: [config('mail.from.address', '<EMAIL>'), config('mail.from.name', 'Bhavitech')],
        );
    }

    public function content(): Content
    {
        return new Content(
            view: 'emails.customer.contact-auto-response',
            with: [
                'lead' => $this->lead,
                'companyName' => 'Bhavitech',
                'supportEmail' => config('mail.support_email', '<EMAIL>'),
                'supportPhone' => '+91 70108 60889',
            ],
        );
    }

    public function attachments(): array
    {
        return [];
    }
}
