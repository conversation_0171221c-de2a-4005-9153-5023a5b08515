<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class EmailValidation extends Model
{
    use HasFactory;

    protected $fillable = [
        'email',
        'is_valid_format',
        'domain_exists',
        'is_disposable',
        'is_role_based',
        'risk_score',
        'engagement_prediction',
        'deliverability_score',
        'validated_at'
    ];

    protected $casts = [
        'is_valid_format' => 'boolean',
        'domain_exists' => 'boolean',
        'is_disposable' => 'boolean',
        'is_role_based' => 'boolean',
        'risk_score' => 'integer',
        'engagement_prediction' => 'integer',
        'deliverability_score' => 'integer',
        'validated_at' => 'datetime'
    ];

    /**
     * Get the contact associated with this email validation
     */
    public function contact()
    {
        return $this->belongsTo(Contact::class, 'email', 'email');
    }

    /**
     * Scope for valid emails
     */
    public function scopeValid($query)
    {
        return $query->where('deliverability_score', '>=', 70)
                    ->where('risk_score', '<', 40);
    }

    /**
     * Scope for invalid emails
     */
    public function scopeInvalid($query)
    {
        return $query->where('risk_score', '>=', 70);
    }

    /**
     * Scope for risky emails
     */
    public function scopeRisky($query)
    {
        return $query->where('risk_score', '>=', 40)
                    ->where('risk_score', '<', 70);
    }

    /**
     * Scope for high engagement prediction
     */
    public function scopeHighEngagement($query)
    {
        return $query->where('engagement_prediction', '>=', 60);
    }

    /**
     * Check if validation is recent (within 30 days)
     */
    public function isRecentValidation(): bool
    {
        return $this->validated_at && $this->validated_at->gt(now()->subDays(30));
    }

    /**
     * Get validation status as string
     */
    public function getValidationStatusAttribute(): string
    {
        if ($this->risk_score >= 70) {
            return 'invalid';
        }
        
        if ($this->risk_score >= 40) {
            return 'risky';
        }
        
        if ($this->deliverability_score >= 70 && $this->engagement_prediction >= 30) {
            return 'valid';
        }
        
        return 'unknown';
    }

    /**
     * Get validation quality score (0-100)
     */
    public function getQualityScoreAttribute(): int
    {
        return max(0, $this->deliverability_score - $this->risk_score);
    }
}
