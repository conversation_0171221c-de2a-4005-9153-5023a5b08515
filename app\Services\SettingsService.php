<?php

namespace App\Services;

use App\Models\Setting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

class SettingsService
{
    /**
     * Get a setting value with caching
     */
    public static function get(string $key, $default = null)
    {
        return Setting::get($key, $default);
    }

    /**
     * Set a setting value
     */
    public static function set(string $key, $value, array $attributes = []): Setting
    {
        return Setting::set($key, $value, $attributes);
    }

    /**
     * Get all settings by category
     */
    public static function getByCategory(string $category): array
    {
        return Setting::getByCategory($category);
    }

    /**
     * Get email configuration settings
     */
    public static function getEmailConfig(): array
    {
        return [
            'provider' => self::get('email.default_provider', 'smtp'),
            'daily_limit' => (int) self::get('email.daily_send_limit', 500),
            'batch_size' => (int) self::get('email.batch_size', 100),
            'retry_attempts' => (int) self::get('email.retry_attempts', 3),
            'bounce_threshold' => (int) self::get('email.bounce_threshold', 5),
        ];
    }

    /**
     * Get SMTP configuration settings
     */
    public static function getSMTPConfig(): array
    {
        return [
            'host' => self::get('smtp.host', 'smtp.gmail.com'),
            'port' => (int) self::get('smtp.port', 587),
            'encryption' => self::get('smtp.encryption', 'tls'),
            'username' => self::get('smtp.username', ''),
            'password' => self::get('smtp.password', ''),
        ];
    }

    /**
     * Get performance settings
     */
    public static function getPerformanceConfig(): array
    {
        return [
            'import_chunk_size' => (int) self::get('performance.import_chunk_size', 1000),
            'query_cache_ttl' => (int) self::get('performance.query_cache_ttl', 3600),
            'max_memory_usage' => (int) self::get('performance.max_memory_usage', 512),
            'enable_query_optimization' => self::get('performance.enable_query_optimization', 'true') === 'true',
        ];
    }

    /**
     * Get security settings
     */
    public static function getSecurityConfig(): array
    {
        return [
            'enable_2fa' => self::get('security.enable_2fa', 'false') === 'true',
            'session_timeout' => (int) self::get('security.session_timeout', 7200),
            'api_rate_limit' => (int) self::get('security.api_rate_limit', 1000),
            'encrypt_sensitive_data' => self::get('security.encrypt_sensitive_data', 'true') === 'true',
        ];
    }

    /**
     * Get geographic settings
     */
    public static function getGeographicConfig(): array
    {
        $supportedStates = self::get('geographic.supported_states', '[]');
        if (is_string($supportedStates)) {
            $supportedStates = json_decode($supportedStates, true) ?: [];
        }

        return [
            'default_country' => self::get('geographic.default_country', 'India'),
            'enable_state_filtering' => self::get('geographic.enable_state_filtering', 'true') === 'true',
            'enable_city_filtering' => self::get('geographic.enable_city_filtering', 'true') === 'true',
            'supported_states' => $supportedStates,
        ];
    }

    /**
     * Get WhatsApp configuration
     */
    public static function getWhatsAppConfig(): array
    {
        return [
            'enable_integration' => self::get('whatsapp.enable_integration', 'true') === 'true',
            'api_endpoint' => self::get('whatsapp.api_endpoint', ''),
            'api_token' => self::get('whatsapp.api_token', ''),
            'daily_message_limit' => (int) self::get('whatsapp.daily_message_limit', 1000),
        ];
    }

    /**
     * Get API configuration
     */
    public static function getAPIConfig(): array
    {
        return [
            'enable_public_api' => self::get('api.enable_public_api', 'true') === 'true',
            'require_authentication' => self::get('api.require_authentication', 'true') === 'true',
            'default_response_format' => self::get('api.default_response_format', 'json'),
        ];
    }

    /**
     * Get backup configuration
     */
    public static function getBackupConfig(): array
    {
        return [
            'enable_auto_backup' => self::get('backup.enable_auto_backup', 'true') === 'true',
            'backup_frequency' => self::get('backup.backup_frequency', 'daily'),
            'retention_days' => (int) self::get('backup.retention_days', 30),
        ];
    }

    /**
     * Get monitoring configuration
     */
    public static function getMonitoringConfig(): array
    {
        return [
            'enable_performance_monitoring' => self::get('monitoring.enable_performance_monitoring', 'true') === 'true',
            'log_slow_queries' => self::get('monitoring.log_slow_queries', 'true') === 'true',
            'slow_query_threshold' => (int) self::get('monitoring.slow_query_threshold', 1000),
        ];
    }

    /**
     * Apply settings to Laravel configuration
     */
    public static function applyToConfig(): void
    {
        // Apply email settings
        $emailConfig = self::getEmailConfig();
        $smtpConfig = self::getSMTPConfig();

        Config::set('mail.default', $emailConfig['provider']);
        Config::set('mail.mailers.smtp.host', $smtpConfig['host']);
        Config::set('mail.mailers.smtp.port', $smtpConfig['port']);
        Config::set('mail.mailers.smtp.encryption', $smtpConfig['encryption']);
        Config::set('mail.mailers.smtp.username', $smtpConfig['username']);
        Config::set('mail.mailers.smtp.password', $smtpConfig['password']);

        // Apply app settings
        Config::set('app.name', self::get('app.name', 'Email Management System'));
        Config::set('app.timezone', self::get('app.timezone', 'Asia/Kolkata'));
        Config::set('app.locale', self::get('app.locale', 'en'));

        // Apply session settings
        $securityConfig = self::getSecurityConfig();
        Config::set('session.lifetime', $securityConfig['session_timeout'] / 60); // Convert to minutes

        // Apply cache settings
        $performanceConfig = self::getPerformanceConfig();
        Config::set('cache.default_ttl', $performanceConfig['query_cache_ttl']);
    }

    /**
     * Validate setting value against rules
     */
    public static function validateSetting(string $key, $value): bool
    {
        $setting = Setting::where('key', $key)->first();
        
        if (!$setting || !$setting->validation_rules) {
            return true;
        }

        $rules = $setting->validation_rules;
        $validator = validator(['value' => $value], ['value' => $rules]);

        return !$validator->fails();
    }

    /**
     * Get all public settings (for frontend/API)
     */
    public static function getPublicSettings(): array
    {
        return Cache::remember('settings.public', 3600, function () {
            return Setting::where('is_public', true)
                ->where('is_active', true)
                ->get()
                ->pluck('value', 'key')
                ->toArray();
        });
    }

    /**
     * Clear all settings cache
     */
    public static function clearCache(): void
    {
        Setting::clearCache();
        Cache::forget('settings.public');
    }

    /**
     * Export settings to array
     */
    public static function exportSettings(): array
    {
        return Setting::where('is_active', true)
            ->get()
            ->map(function ($setting) {
                return [
                    'key' => $setting->key,
                    'value' => $setting->value,
                    'category' => $setting->category,
                    'type' => $setting->type,
                    'description' => $setting->description,
                    'is_public' => $setting->is_public,
                    'is_encrypted' => $setting->is_encrypted,
                ];
            })
            ->toArray();
    }

    /**
     * Import settings from array
     */
    public static function importSettings(array $settings): void
    {
        foreach ($settings as $settingData) {
            if (isset($settingData['key']) && isset($settingData['value'])) {
                self::set($settingData['key'], $settingData['value'], [
                    'category' => $settingData['category'] ?? 'general',
                    'type' => $settingData['type'] ?? 'string',
                    'description' => $settingData['description'] ?? '',
                    'is_public' => $settingData['is_public'] ?? false,
                    'is_encrypted' => $settingData['is_encrypted'] ?? false,
                ]);
            }
        }
    }

    /**
     * Get system information
     */
    public static function getSystemInfo(): array
    {
        return [
            'app_version' => '1.0.0',
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'database_version' => self::getDatabaseVersion(),
            'total_contacts' => \App\Models\Contact::count(),
            'active_email_accounts' => \App\Models\EmailAccount::where('is_active', true)->count(),
            'total_campaigns' => \App\Models\EmailCampaign::count(),
            'total_settings' => Setting::where('is_active', true)->count(),
        ];
    }

    /**
     * Get database version
     */
    private static function getDatabaseVersion(): string
    {
        try {
            $result = \DB::select('SELECT VERSION() as version');
            return $result[0]->version ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
}
