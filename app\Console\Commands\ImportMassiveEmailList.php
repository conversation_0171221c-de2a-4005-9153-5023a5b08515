<?php

namespace App\Console\Commands;

use App\Jobs\BulkEmailImportJob;
use App\Models\ContactList;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class ImportMassiveEmailList extends Command
{
    protected $signature = 'emails:import-massive 
                            {file : CSV file path in storage/app directory}
                            {list-name : Name for the new contact list}
                            {--chunk-size=1000 : Number of records to process per chunk}
                            {--skip-duplicates=true : Skip duplicate email addresses}
                            {--validate-emails=true : Validate email format}
                            {--description= : Description for the contact list}';

    protected $description = 'Import massive email lists (1 crore+) with optimized processing';

    public function handle(): int
    {
        $filePath = $this->argument('file');
        $listName = $this->argument('list-name');
        $chunkSize = (int) $this->option('chunk-size');
        $skipDuplicates = $this->option('skip-duplicates') === 'true';
        $validateEmails = $this->option('validate-emails') === 'true';
        $description = $this->option('description') ?? 'Imported via massive import command';

        // Validate file exists
        if (!Storage::exists($filePath)) {
            $this->error("File not found: {$filePath}");
            $this->info("Place your CSV file in storage/app/ directory");
            return 1;
        }

        // Get file size
        $fileSize = Storage::size($filePath);
        $fileSizeMB = round($fileSize / 1024 / 1024, 2);
        
        $this->info("File: {$filePath} ({$fileSizeMB} MB)");
        
        // Estimate record count
        $estimatedRecords = $this->estimateRecordCount($filePath);
        $this->info("Estimated records: " . number_format($estimatedRecords));
        
        if ($estimatedRecords > 1000000) { // 10 lakh+
            $this->warn("⚠️  Large dataset detected! This will take significant time and resources.");
            if (!$this->confirm('Do you want to continue?')) {
                return 0;
            }
        }

        // Create contact list
        $admin = User::where('role', 'admin')->first();
        $contactList = ContactList::create([
            'name' => $listName,
            'description' => $description,
            'type' => 'static',
            'status' => 'active',
            'created_by' => $admin->id,
        ]);

        $this->info("Created contact list: {$listName} (ID: {$contactList->id})");

        // Optimize chunk size based on file size
        if ($estimatedRecords > 5000000) { // 50 lakh+
            $chunkSize = min($chunkSize, 500); // Smaller chunks for very large files
        }

        $this->info("Using chunk size: {$chunkSize}");

        // Dispatch import job
        $this->info("🚀 Starting import job...");
        
        BulkEmailImportJob::dispatch(
            $filePath,
            $contactList->id,
            $chunkSize,
            $skipDuplicates,
            $validateEmails
        );

        $this->info("✅ Import job queued successfully!");
        $this->info("Monitor progress with: php artisan queue:work");
        $this->info("Check logs: tail -f storage/logs/laravel.log");
        
        // Show monitoring commands
        $this->newLine();
        $this->info("📊 Monitoring Commands:");
        $this->line("• Queue status: php artisan queue:work --verbose");
        $this->line("• Failed jobs: php artisan queue:failed");
        $this->line("• Restart queue: php artisan queue:restart");
        
        return 0;
    }

    private function estimateRecordCount(string $filePath): int
    {
        $fullPath = storage_path('app/' . $filePath);
        $file = fopen($fullPath, 'r');
        
        if (!$file) {
            return 0;
        }
        
        $lineCount = 0;
        while (!feof($file)) {
            fgets($file);
            $lineCount++;
            
            // Stop counting after 10000 lines and estimate
            if ($lineCount > 10000) {
                $fileSize = filesize($fullPath);
                $currentPosition = ftell($file);
                $estimatedTotal = ($fileSize / $currentPosition) * $lineCount;
                fclose($file);
                return (int) $estimatedTotal - 1; // Subtract header
            }
        }
        
        fclose($file);
        return max(0, $lineCount - 1); // Subtract header
    }
}
