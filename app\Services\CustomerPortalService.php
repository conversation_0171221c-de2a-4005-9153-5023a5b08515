<?php

namespace App\Services;

use App\Models\User;
use App\Models\Lead;
use App\Models\Project;
use App\Models\Invoice;
use App\Models\SupportTicket;
use App\Models\CustomerDocument;
use App\Models\CustomerNotification;
use App\Models\BusinessSetting;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Carbon\Carbon;

class CustomerPortalService
{
    public function createCustomerAccount(Lead $lead, array $additionalData = []): User
    {
        // Generate a secure password
        $password = Str::random(12);
        
        $customer = User::create([
            'name' => $lead->name,
            'email' => $lead->email,
            'phone' => $lead->phone,
            'password' => Hash::make($password),
            'role' => 'customer',
            'email_verified_at' => now(),
            'customer_data' => array_merge([
                'company' => $lead->company,
                'source' => $lead->source,
                'service_interest' => $lead->service_interest,
                'lead_id' => $lead->id,
                'onboarded_at' => now(),
            ], $additionalData),
        ]);

        // Update lead status
        $lead->update([
            'status' => 'converted',
            'converted_at' => now(),
            'customer_id' => $customer->id,
        ]);

        // Send welcome email with login credentials
        $this->sendWelcomeEmail($customer, $password);

        // Create initial notification
        $this->createNotification($customer, [
            'type' => 'welcome',
            'title' => 'Welcome to Bhavitech Customer Portal',
            'message' => 'Your account has been created successfully. You can now track your projects and communicate with our team.',
            'action_url' => route('customer.dashboard'),
        ]);

        return $customer;
    }

    public function getDashboardData(User $customer): array
    {
        $projects = $this->getCustomerProjects($customer);
        $recentInvoices = $this->getRecentInvoices($customer, 5);
        $openTickets = $this->getOpenSupportTickets($customer);
        $notifications = $this->getRecentNotifications($customer, 10);

        return [
            'customer' => $customer,
            'summary' => [
                'total_projects' => $projects->count(),
                'active_projects' => $projects->where('status', 'in_progress')->count(),
                'completed_projects' => $projects->where('status', 'completed')->count(),
                'total_invoices' => Invoice::where('customer_id', $customer->id)->count(),
                'pending_invoices' => Invoice::where('customer_id', $customer->id)
                    ->where('status', 'pending')->count(),
                'open_tickets' => $openTickets->count(),
                'total_spent' => Invoice::where('customer_id', $customer->id)
                    ->where('status', 'paid')->sum('total_amount'),
            ],
            'recent_projects' => $projects->take(3),
            'recent_invoices' => $recentInvoices,
            'open_tickets' => $openTickets->take(5),
            'notifications' => $notifications,
            'quick_actions' => $this->getQuickActions($customer),
        ];
    }

    public function getCustomerProjects(User $customer)
    {
        return Project::where('customer_id', $customer->id)
            ->with(['milestones', 'documents', 'team_members'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getProjectDetails(User $customer, int $projectId): ?Project
    {
        return Project::where('customer_id', $customer->id)
            ->where('id', $projectId)
            ->with([
                'milestones' => function ($query) {
                    $query->orderBy('due_date');
                },
                'documents',
                'team_members',
                'updates' => function ($query) {
                    $query->orderBy('created_at', 'desc');
                },
                'invoices',
            ])
            ->first();
    }

    public function getRecentInvoices(User $customer, int $limit = 10)
    {
        return Invoice::where('customer_id', $customer->id)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function getInvoiceDetails(User $customer, int $invoiceId): ?Invoice
    {
        return Invoice::where('customer_id', $customer->id)
            ->where('id', $invoiceId)
            ->with(['items', 'payments'])
            ->first();
    }

    public function createSupportTicket(User $customer, array $ticketData): SupportTicket
    {
        $ticket = SupportTicket::create([
            'customer_id' => $customer->id,
            'title' => $ticketData['title'],
            'description' => $ticketData['description'],
            'priority' => $ticketData['priority'] ?? 'medium',
            'category' => $ticketData['category'] ?? 'general',
            'status' => 'open',
            'ticket_number' => $this->generateTicketNumber(),
        ]);

        // Attach files if provided
        if (isset($ticketData['attachments'])) {
            foreach ($ticketData['attachments'] as $attachment) {
                $ticket->attachments()->create([
                    'filename' => $attachment['filename'],
                    'file_path' => $attachment['file_path'],
                    'file_size' => $attachment['file_size'],
                    'mime_type' => $attachment['mime_type'],
                ]);
            }
        }

        // Notify support team
        $this->notifySupportTeam($ticket);

        // Create notification for customer
        $this->createNotification($customer, [
            'type' => 'ticket_created',
            'title' => 'Support Ticket Created',
            'message' => "Your support ticket #{$ticket->ticket_number} has been created and assigned to our team.",
            'action_url' => route('customer.tickets.show', $ticket),
        ]);

        return $ticket;
    }

    public function getSupportTickets(User $customer, array $filters = [])
    {
        $query = SupportTicket::where('customer_id', $customer->id);

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['priority'])) {
            $query->where('priority', $filters['priority']);
        }

        if (isset($filters['category'])) {
            $query->where('category', $filters['category']);
        }

        return $query->with(['responses', 'attachments'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function getOpenSupportTickets(User $customer)
    {
        return SupportTicket::where('customer_id', $customer->id)
            ->whereIn('status', ['open', 'in_progress'])
            ->with(['responses'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function addTicketResponse(User $customer, SupportTicket $ticket, array $responseData): void
    {
        if ($ticket->customer_id !== $customer->id) {
            throw new \Exception('Unauthorized access to ticket');
        }

        $response = $ticket->responses()->create([
            'user_id' => $customer->id,
            'message' => $responseData['message'],
            'is_internal' => false,
        ]);

        // Attach files if provided
        if (isset($responseData['attachments'])) {
            foreach ($responseData['attachments'] as $attachment) {
                $response->attachments()->create([
                    'filename' => $attachment['filename'],
                    'file_path' => $attachment['file_path'],
                    'file_size' => $attachment['file_size'],
                    'mime_type' => $attachment['mime_type'],
                ]);
            }
        }

        // Update ticket status
        $ticket->update([
            'status' => 'awaiting_response',
            'last_response_at' => now(),
        ]);

        // Notify support team
        $this->notifySupportTeam($ticket, 'customer_response');
    }

    public function getCustomerDocuments(User $customer, array $filters = [])
    {
        $query = CustomerDocument::where('customer_id', $customer->id);

        if (isset($filters['category'])) {
            $query->where('category', $filters['category']);
        }

        if (isset($filters['project_id'])) {
            $query->where('project_id', $filters['project_id']);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    public function uploadDocument(User $customer, array $documentData): CustomerDocument
    {
        return CustomerDocument::create([
            'customer_id' => $customer->id,
            'project_id' => $documentData['project_id'] ?? null,
            'title' => $documentData['title'],
            'description' => $documentData['description'] ?? null,
            'category' => $documentData['category'] ?? 'general',
            'filename' => $documentData['filename'],
            'file_path' => $documentData['file_path'],
            'file_size' => $documentData['file_size'],
            'mime_type' => $documentData['mime_type'],
            'is_public' => $documentData['is_public'] ?? false,
        ]);
    }

    public function getRecentNotifications(User $customer, int $limit = 10)
    {
        return CustomerNotification::where('customer_id', $customer->id)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function createNotification(User $customer, array $notificationData): CustomerNotification
    {
        return CustomerNotification::create([
            'customer_id' => $customer->id,
            'type' => $notificationData['type'],
            'title' => $notificationData['title'],
            'message' => $notificationData['message'],
            'action_url' => $notificationData['action_url'] ?? null,
            'is_read' => false,
        ]);
    }

    public function markNotificationAsRead(User $customer, int $notificationId): bool
    {
        $notification = CustomerNotification::where('customer_id', $customer->id)
            ->where('id', $notificationId)
            ->first();

        if ($notification) {
            $notification->update(['is_read' => true, 'read_at' => now()]);
            return true;
        }

        return false;
    }

    public function markAllNotificationsAsRead(User $customer): int
    {
        return CustomerNotification::where('customer_id', $customer->id)
            ->where('is_read', false)
            ->update(['is_read' => true, 'read_at' => now()]);
    }

    public function updateCustomerProfile(User $customer, array $profileData): User
    {
        $allowedFields = ['name', 'phone', 'company', 'address', 'city', 'state', 'country', 'timezone'];
        $updateData = array_intersect_key($profileData, array_flip($allowedFields));

        // Handle customer_data updates
        if (isset($profileData['company']) || isset($profileData['address']) || 
            isset($profileData['city']) || isset($profileData['state']) || 
            isset($profileData['country']) || isset($profileData['timezone'])) {
            
            $customerData = $customer->customer_data ?? [];
            $customerData = array_merge($customerData, array_intersect_key($profileData, [
                'company' => true, 'address' => true, 'city' => true, 
                'state' => true, 'country' => true, 'timezone' => true
            ]));
            
            $updateData['customer_data'] = $customerData;
        }

        $customer->update($updateData);

        return $customer->fresh();
    }

    public function changePassword(User $customer, string $currentPassword, string $newPassword): bool
    {
        if (!Hash::check($currentPassword, $customer->password)) {
            throw new \Exception('Current password is incorrect');
        }

        $customer->update(['password' => Hash::make($newPassword)]);

        // Create notification
        $this->createNotification($customer, [
            'type' => 'security',
            'title' => 'Password Changed',
            'message' => 'Your password has been successfully changed.',
        ]);

        return true;
    }

    public function getCustomerActivity(User $customer, int $days = 30): array
    {
        $startDate = now()->subDays($days);

        return [
            'login_history' => $this->getLoginHistory($customer, $startDate),
            'project_activities' => $this->getProjectActivities($customer, $startDate),
            'ticket_activities' => $this->getTicketActivities($customer, $startDate),
            'document_activities' => $this->getDocumentActivities($customer, $startDate),
        ];
    }

    protected function getQuickActions(User $customer): array
    {
        $actions = [];

        // Check for pending invoices
        $pendingInvoices = Invoice::where('customer_id', $customer->id)
            ->where('status', 'pending')
            ->count();

        if ($pendingInvoices > 0) {
            $actions[] = [
                'type' => 'payment',
                'title' => 'Pay Pending Invoices',
                'description' => "You have {$pendingInvoices} pending invoice(s)",
                'url' => route('customer.invoices'),
                'priority' => 'high',
            ];
        }

        // Check for project updates
        $projectsNeedingFeedback = Project::where('customer_id', $customer->id)
            ->where('status', 'awaiting_feedback')
            ->count();

        if ($projectsNeedingFeedback > 0) {
            $actions[] = [
                'type' => 'feedback',
                'title' => 'Provide Project Feedback',
                'description' => "{$projectsNeedingFeedback} project(s) awaiting your feedback",
                'url' => route('customer.projects'),
                'priority' => 'medium',
            ];
        }

        // Check for open tickets
        $openTickets = SupportTicket::where('customer_id', $customer->id)
            ->where('status', 'awaiting_response')
            ->count();

        if ($openTickets > 0) {
            $actions[] = [
                'type' => 'support',
                'title' => 'Respond to Support Tickets',
                'description' => "{$openTickets} ticket(s) awaiting your response",
                'url' => route('customer.tickets'),
                'priority' => 'medium',
            ];
        }

        return $actions;
    }

    protected function generateTicketNumber(): string
    {
        $prefix = BusinessSetting::getValue('support_ticket_prefix', 'TKT');
        $number = SupportTicket::count() + 1;
        return $prefix . '-' . str_pad($number, 6, '0', STR_PAD_LEFT);
    }

    protected function sendWelcomeEmail(User $customer, string $password): void
    {
        // This would send a welcome email with login credentials
        // Implementation would use Laravel's Mail facade
    }

    protected function notifySupportTeam(SupportTicket $ticket, string $type = 'new_ticket'): void
    {
        // This would notify the support team about new tickets or responses
        // Implementation would use notifications or email
    }

    protected function getLoginHistory(User $customer, Carbon $startDate): array
    {
        // This would track login history from a separate table
        // For now, return sample data
        return [];
    }

    protected function getProjectActivities(User $customer, Carbon $startDate): array
    {
        return Project::where('customer_id', $customer->id)
            ->where('updated_at', '>=', $startDate)
            ->with(['updates' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
            ->get()
            ->toArray();
    }

    protected function getTicketActivities(User $customer, Carbon $startDate): array
    {
        return SupportTicket::where('customer_id', $customer->id)
            ->where('updated_at', '>=', $startDate)
            ->with(['responses' => function ($query) use ($startDate) {
                $query->where('created_at', '>=', $startDate);
            }])
            ->get()
            ->toArray();
    }

    protected function getDocumentActivities(User $customer, Carbon $startDate): array
    {
        return CustomerDocument::where('customer_id', $customer->id)
            ->where('created_at', '>=', $startDate)
            ->get()
            ->toArray();
    }
}
