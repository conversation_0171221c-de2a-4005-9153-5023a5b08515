<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;

class ReportsCenter extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-chart-bar';

    protected static ?string $navigationLabel = 'Reports Center';

    protected static ?string $title = 'Reports & Exports';

    protected static ?string $navigationGroup = 'Analytics & Reports';

    protected static ?int $navigationSort = 2;

    protected static string $view = 'filament.pages.reports-center';
}
