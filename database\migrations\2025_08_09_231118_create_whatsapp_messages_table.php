<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('whatsapp_number_id')->constrained('whatsapp_numbers');
            $table->string('recipient_phone'); // Recipient's phone number
            $table->string('recipient_name')->nullable();
            $table->enum('message_type', ['text', 'template', 'media', 'interactive', 'location']);
            $table->text('message_content');
            $table->json('template_data')->nullable(); // Template parameters
            $table->json('media_data')->nullable(); // Media URLs and metadata
            $table->json('interactive_data')->nullable(); // Buttons, lists, etc.
            $table->enum('status', ['pending', 'sent', 'delivered', 'read', 'failed'])->default('pending');
            $table->string('whatsapp_message_id')->nullable(); // WhatsApp's message ID
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->text('failure_reason')->nullable();
            $table->string('campaign_id')->nullable(); // Link to campaign
            $table->foreignId('lead_id')->nullable()->constrained('leads');
            $table->boolean('is_automated')->default(false);
            $table->decimal('cost', 8, 4)->nullable(); // Message cost
            $table->json('webhook_data')->nullable(); // Raw webhook response
            $table->timestamps();

            $table->index(['whatsapp_number_id', 'status']);
            $table->index(['recipient_phone', 'sent_at']);
            $table->index(['campaign_id', 'status']);
            $table->index(['lead_id', 'sent_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_messages');
    }
};
