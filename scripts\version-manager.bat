@echo off
REM Version Management Script for 1 Crore Email Management System
REM This script handles version bumping, tagging, and release management

setlocal enabledelayedexpansion

echo.
echo 🏷️ Version Management for 1 Crore Email Management System
echo.

REM Check if Git is available
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Git is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if we're in a Git repository
if not exist .git (
    echo ❌ Not in a Git repository
    pause
    exit /b 1
)

:MENU
echo.
echo 📋 Version Management Options:
echo.
echo 1. Show current version
echo 2. Bump patch version (1.0.0 → 1.0.1)
echo 3. Bump minor version (1.0.0 → 1.1.0)
echo 4. Bump major version (1.0.0 → 2.0.0)
echo 5. Create custom version
echo 6. Create release branch
echo 7. Tag current commit
echo 8. Show version history
echo 9. Create changelog
echo 0. Exit
echo.
set /p choice="Select an option (0-9): "

if "%choice%"=="1" goto SHOW_VERSION
if "%choice%"=="2" goto BUMP_PATCH
if "%choice%"=="3" goto BUMP_MINOR
if "%choice%"=="4" goto BUMP_MAJOR
if "%choice%"=="5" goto CUSTOM_VERSION
if "%choice%"=="6" goto CREATE_RELEASE
if "%choice%"=="7" goto TAG_COMMIT
if "%choice%"=="8" goto VERSION_HISTORY
if "%choice%"=="9" goto CREATE_CHANGELOG
if "%choice%"=="0" goto EXIT
goto MENU

:SHOW_VERSION
echo.
echo 📊 Current Version Information:
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo Latest Git tag:
git describe --tags --abbrev=0 2>nul || echo "No tags found"
echo.
echo Current branch:
git branch --show-current
echo.
echo Latest commit:
git log --oneline -1
echo.
echo Version in composer.json:
findstr "version" composer.json 2>nul || echo "No version found in composer.json"
goto MENU

:BUMP_PATCH
echo.
echo 🔧 Bumping Patch Version
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
call :GET_CURRENT_VERSION
call :BUMP_VERSION patch
goto MENU

:BUMP_MINOR
echo.
echo ✨ Bumping Minor Version
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
call :GET_CURRENT_VERSION
call :BUMP_VERSION minor
goto MENU

:BUMP_MAJOR
echo.
echo 🚀 Bumping Major Version
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
call :GET_CURRENT_VERSION
call :BUMP_VERSION major
goto MENU

:CUSTOM_VERSION
echo.
echo 🎯 Create Custom Version
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
set /p new_version="Enter new version (e.g., 1.2.0): "
if "%new_version%"=="" goto MENU

echo Creating version %new_version%...
call :CREATE_VERSION %new_version%
goto MENU

:CREATE_RELEASE
echo.
echo 🚀 Create Release Branch
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
call :GET_CURRENT_VERSION
set /p release_version="Enter release version (current: %current_version%): "
if "%release_version%"=="" set release_version=%current_version%

set release_branch=release/%release_version%
echo Creating release branch: %release_branch%

git checkout development
git pull origin development
git checkout -b %release_branch%

if %errorlevel% equ 0 (
    echo ✅ Release branch '%release_branch%' created
    echo 📝 Update version numbers and prepare for release
    echo 📝 Run tests and finalize documentation
    echo 📝 When ready, merge to main and tag the release
) else (
    echo ❌ Failed to create release branch
)
goto MENU

:TAG_COMMIT
echo.
echo 🏷️ Tag Current Commit
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
set /p tag_version="Enter tag version (e.g., v1.0.0): "
if "%tag_version%"=="" goto MENU

set /p tag_message="Enter tag message: "
if "%tag_message%"=="" set tag_message="Release %tag_version%"

git tag -a %tag_version% -m "%tag_message%"
if %errorlevel% equ 0 (
    echo ✅ Tag '%tag_version%' created
    echo 📝 Push tag with: git push origin %tag_version%
) else (
    echo ❌ Failed to create tag
)
goto MENU

:VERSION_HISTORY
echo.
echo 📚 Version History
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo Recent tags:
git tag --sort=-version:refname | head -10 2>nul || git tag -l
echo.
echo Tag details:
for /f "tokens=*" %%i in ('git tag --sort=-version:refname') do (
    echo.
    echo Tag: %%i
    git log --oneline -1 %%i 2>nul
)
goto MENU

:CREATE_CHANGELOG
echo.
echo 📝 Create Changelog
echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
echo.
echo Generating changelog...

REM Get the latest two tags
for /f "tokens=1" %%i in ('git tag --sort=-version:refname') do (
    if not defined latest_tag (
        set latest_tag=%%i
    ) else if not defined previous_tag (
        set previous_tag=%%i
        goto :generate_changelog
    )
)

:generate_changelog
if defined previous_tag (
    echo.
    echo Changes from %previous_tag% to %latest_tag%:
    echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    git log --oneline %previous_tag%..%latest_tag%
) else (
    echo.
    echo All commits in %latest_tag%:
    echo ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
    git log --oneline %latest_tag%
)

echo.
echo 📝 Changelog generated. Consider updating CHANGELOG.md
goto MENU

:GET_CURRENT_VERSION
REM Get current version from git tags
for /f "tokens=*" %%i in ('git describe --tags --abbrev=0 2^>nul') do set current_version=%%i
if not defined current_version set current_version=v0.0.0
REM Remove 'v' prefix if present
set current_version=%current_version:v=%
exit /b

:BUMP_VERSION
set version_type=%1
echo Current version: %current_version%

REM Parse version numbers
for /f "tokens=1,2,3 delims=." %%a in ("%current_version%") do (
    set major=%%a
    set minor=%%b
    set patch=%%c
)

REM Bump version based on type
if "%version_type%"=="patch" (
    set /a patch+=1
) else if "%version_type%"=="minor" (
    set /a minor+=1
    set patch=0
) else if "%version_type%"=="major" (
    set /a major+=1
    set minor=0
    set patch=0
)

set new_version=%major%.%minor%.%patch%
echo New version: %new_version%

call :CREATE_VERSION %new_version%
exit /b

:CREATE_VERSION
set version=%1
echo.
echo Creating version %version%...

REM Update version in composer.json if it exists
if exist composer.json (
    echo Updating composer.json...
    REM This would need a more sophisticated approach in a real script
    echo ✅ Remember to update version in composer.json manually
)

REM Update version in package.json if it exists
if exist package.json (
    echo Updating package.json...
    echo ✅ Remember to update version in package.json manually
)

REM Commit version changes
git add .
git commit -m "chore(release): bump version to %version%"

REM Create tag
git tag -a v%version% -m "Release version %version%

Email Management System Features:
- Support for 1 crore (10 million) emails
- Geographic filtering by state/city
- MySQL performance optimizations
- Advanced analytics dashboard
- WhatsApp integration
- Comprehensive admin panel

Performance:
- Optimized for massive datasets
- Daily sending limits management
- Real-time monitoring and analytics"

if %errorlevel% equ 0 (
    echo ✅ Version %version% created and tagged
    echo 📝 Push changes with: git push origin HEAD --tags
    echo 📝 Create release notes on GitHub/GitLab
) else (
    echo ❌ Failed to create version
)
exit /b

:EXIT
echo.
echo 👋 Goodbye!
echo.
exit /b 0
