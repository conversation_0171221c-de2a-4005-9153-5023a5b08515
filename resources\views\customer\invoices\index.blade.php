@extends('layouts.customer')

@section('title', 'My Invoices')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">My Invoices</h1>
                <p class="text-gray-600 mt-1">View and manage all your invoices and payments</p>
            </div>
            <div class="flex items-center space-x-3">
                <!-- Filter Dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                        </svg>
                        Filter by Status
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    
                    <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="origin-top-right absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                        <div class="py-1">
                            <a href="{{ route('customer.invoices') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">All Invoices</a>
                            <a href="{{ route('customer.invoices', ['status' => 'draft']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Draft</a>
                            <a href="{{ route('customer.invoices', ['status' => 'pending']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Pending</a>
                            <a href="{{ route('customer.invoices', ['status' => 'paid']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Paid</a>
                            <a href="{{ route('customer.invoices', ['status' => 'overdue']) }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Overdue</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <!-- Total Amount -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Invoiced</p>
                    <p class="text-2xl font-semibold text-gray-900">₹{{ number_format($invoices->sum('total_amount')) }}</p>
                </div>
            </div>
        </div>

        <!-- Pending Amount -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Pending Payment</p>
                    <p class="text-2xl font-semibold text-gray-900">₹{{ number_format($invoices->where('status', 'pending')->sum('total_amount')) }}</p>
                </div>
            </div>
        </div>

        <!-- Paid Amount -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Paid Amount</p>
                    <p class="text-2xl font-semibold text-gray-900">₹{{ number_format($invoices->where('status', 'paid')->sum('total_amount')) }}</p>
                </div>
            </div>
        </div>

        <!-- Overdue Amount -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Overdue Amount</p>
                    <p class="text-2xl font-semibold text-gray-900">₹{{ number_format($invoices->filter(fn($invoice) => $invoice->isOverdue())->sum('total_amount')) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices List -->
    @if($invoices->count() > 0)
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Invoice History</h3>
            </div>
            
            <!-- Desktop Table -->
            <div class="hidden md:block overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($invoices as $invoice)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">#{{ $invoice->number }}</div>
                                    <div class="text-sm text-gray-500">{{ $invoice->created_at->format('M d, Y') }}</div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">
                                    @if($invoice->project)
                                        <a href="{{ route('customer.projects.show', $invoice->project) }}" class="text-primary-600 hover:text-primary-700">
                                            {{ $invoice->project->name }}
                                        </a>
                                    @else
                                        {{ $invoice->description }}
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">₹{{ number_format($invoice->total_amount) }}</div>
                                @if($invoice->tax_amount > 0)
                                <div class="text-xs text-gray-500">Tax: ₹{{ number_format($invoice->tax_amount) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    @if($invoice->status === 'paid') bg-green-100 text-green-800
                                    @elseif($invoice->status === 'pending') bg-yellow-100 text-yellow-800
                                    @elseif($invoice->status === 'overdue' || $invoice->isOverdue()) bg-red-100 text-red-800
                                    @elseif($invoice->status === 'draft') bg-gray-100 text-gray-800
                                    @else bg-gray-100 text-gray-800
                                    @endif">
                                    @if($invoice->isOverdue() && $invoice->status === 'pending')
                                        Overdue
                                    @else
                                        {{ ucfirst($invoice->status) }}
                                    @endif
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                @if($invoice->due_date)
                                    {{ $invoice->due_date->format('M d, Y') }}
                                    @if($invoice->isOverdue())
                                        <div class="text-xs text-red-600">{{ $invoice->getDaysOverdue() }} days overdue</div>
                                    @endif
                                @else
                                    -
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{{ route('customer.invoices.show', $invoice) }}" 
                                   class="text-primary-600 hover:text-primary-700 mr-3">View</a>
                                @if($invoice->status === 'pending')
                                <button class="text-green-600 hover:text-green-700">Pay Now</button>
                                @endif
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Mobile Cards -->
            <div class="md:hidden">
                <div class="space-y-4 p-4">
                    @foreach($invoices as $invoice)
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-start justify-between mb-3">
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">#{{ $invoice->number }}</h4>
                                <p class="text-xs text-gray-500">{{ $invoice->created_at->format('M d, Y') }}</p>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                @if($invoice->status === 'paid') bg-green-100 text-green-800
                                @elseif($invoice->status === 'pending') bg-yellow-100 text-yellow-800
                                @elseif($invoice->status === 'overdue' || $invoice->isOverdue()) bg-red-100 text-red-800
                                @elseif($invoice->status === 'draft') bg-gray-100 text-gray-800
                                @else bg-gray-100 text-gray-800
                                @endif">
                                @if($invoice->isOverdue() && $invoice->status === 'pending')
                                    Overdue
                                @else
                                    {{ ucfirst($invoice->status) }}
                                @endif
                            </span>
                        </div>
                        
                        <div class="space-y-2">
                            @if($invoice->project)
                            <div class="text-sm">
                                <span class="text-gray-500">Project:</span>
                                <a href="{{ route('customer.projects.show', $invoice->project) }}" class="text-primary-600 hover:text-primary-700 ml-1">
                                    {{ $invoice->project->name }}
                                </a>
                            </div>
                            @endif
                            
                            <div class="text-sm">
                                <span class="text-gray-500">Amount:</span>
                                <span class="font-medium text-gray-900 ml-1">₹{{ number_format($invoice->total_amount) }}</span>
                            </div>
                            
                            @if($invoice->due_date)
                            <div class="text-sm">
                                <span class="text-gray-500">Due Date:</span>
                                <span class="text-gray-900 ml-1">{{ $invoice->due_date->format('M d, Y') }}</span>
                                @if($invoice->isOverdue())
                                    <span class="text-red-600 ml-1">({{ $invoice->getDaysOverdue() }} days overdue)</span>
                                @endif
                            </div>
                            @endif
                        </div>
                        
                        <div class="mt-4 flex space-x-3">
                            <a href="{{ route('customer.invoices.show', $invoice) }}" 
                               class="flex-1 text-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                View Details
                            </a>
                            @if($invoice->status === 'pending')
                            <button class="flex-1 text-center px-3 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                                Pay Now
                            </button>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Pagination -->
            @if($invoices->hasPages())
            <div class="px-6 py-3 border-t border-gray-200">
                {{ $invoices->appends(request()->query())->links() }}
            </div>
            @endif
        </div>
    @else
        <!-- Empty State -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-12">
            <div class="text-center">
                <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-gray-900">No invoices found</h3>
                <p class="mt-2 text-gray-500">You don't have any invoices yet. Invoices will appear here once projects begin.</p>
                <div class="mt-6">
                    <a href="{{ route('customer.projects') }}" 
                       class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        View Projects
                    </a>
                </div>
            </div>
        </div>
    @endif
</div>
@endsection
