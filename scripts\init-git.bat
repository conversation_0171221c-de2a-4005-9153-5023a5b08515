@echo off
REM Git Repository Initialization Script for 1 Crore Email Management System
REM This script initializes Git repository with proper configuration and branch structure

echo.
echo 🌿 Initializing Git Repository for 1 Crore Email Management System...
echo.

REM Check if Git is installed
git --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Git is not installed or not in PATH
    echo Please install Git from: https://git-scm.com/download/windows
    pause
    exit /b 1
)

echo ✅ Git is available
git --version

REM Initialize Git repository if not already initialized
if not exist .git (
    echo.
    echo 📁 Initializing Git repository...
    git init
    echo ✅ Git repository initialized
) else (
    echo.
    echo 📁 Git repository already exists
)

REM Configure Git user (if not already configured)
echo.
echo 👤 Configuring Git user...
git config user.name >nul 2>&1
if %errorlevel% neq 0 (
    set /p username="Enter your name: "
    git config user.name "%username%"
)

git config user.email >nul 2>&1
if %errorlevel% neq 0 (
    set /p email="Enter your email: "
    git config user.email "%email%"
)

echo ✅ Git user configured:
git config user.name
git config user.email

REM Set up Git configuration for the project
echo.
echo ⚙️ Setting up Git configuration...
git config core.autocrlf true
git config core.safecrlf false
git config pull.rebase false
git config init.defaultBranch main
git config branch.autosetupmerge always
git config branch.autosetuprebase always

echo ✅ Git configuration completed

REM Create initial commit if no commits exist
git rev-parse HEAD >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo 📝 Creating initial commit...
    git add .
    git commit -m "🎉 Initial commit: 1 Crore Email Management System

Features:
- Laravel 11 application
- MySQL database with optimizations
- Email list management for massive datasets
- Geographic filtering capabilities
- Advanced analytics dashboard
- WhatsApp integration
- Comprehensive admin panel

System Specifications:
- Supports 1 crore (10 million) emails
- Geographic filtering by state/city
- Daily sending limits (500 emails per account)
- Performance optimized for massive datasets
- Production-ready with monitoring tools"
    
    echo ✅ Initial commit created
) else (
    echo.
    echo 📝 Repository already has commits
)

REM Create and switch to development branch
echo.
echo 🌿 Setting up branch structure...

git checkout -b development 2>nul
if %errorlevel% equ 0 (
    echo ✅ Created and switched to 'development' branch
) else (
    git checkout development 2>nul
    if %errorlevel% equ 0 (
        echo ✅ Switched to existing 'development' branch
    ) else (
        echo ⚠️ Could not create/switch to development branch
    )
)

REM Create feature branches
echo.
echo 🔧 Creating feature branches...

set branches=feature/email-campaigns feature/analytics feature/whatsapp-integration feature/performance-optimization feature/api-development

for %%b in (%branches%) do (
    git checkout -b %%b development 2>nul
    if %errorlevel% equ 0 (
        echo ✅ Created branch: %%b
    ) else (
        echo ⚠️ Branch %%b already exists or could not be created
    )
)

REM Switch back to main branch
git checkout main 2>nul
if %errorlevel% neq 0 (
    git checkout master 2>nul
    if %errorlevel% neq 0 (
        echo ⚠️ Could not switch to main/master branch
    )
)

echo.
echo 📊 Current branch structure:
git branch -a

echo.
echo 📋 Git Repository Information:
echo Repository: %cd%
echo Current branch: 
git branch --show-current 2>nul || echo "Unable to determine current branch"
echo Total commits: 
git rev-list --count HEAD 2>nul || echo "0"
echo Remote origin: 
git remote get-url origin 2>nul || echo "No remote origin configured"

echo.
echo 📝 Next Steps:
echo 1. Configure remote repository: git remote add origin [URL]
echo 2. Push to remote: git push -u origin main
echo 3. Set up branch protection rules on GitHub/GitLab
echo 4. Configure CI/CD workflows
echo.
echo ✅ Git repository initialization completed!
echo.
pause
