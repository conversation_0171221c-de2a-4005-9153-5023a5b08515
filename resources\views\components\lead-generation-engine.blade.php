<!-- Advanced Lead Generation Engine -->
<div x-data="leadGenerationEngine()" x-init="init()">
    <!-- Exit Intent Pop-up -->
    <div x-show="showExitIntent"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95"
         class="fixed inset-0 z-[60] overflow-y-auto"
         style="display: none;">
        
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" @click="closeExitIntent()">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-white">
                            🎯 Wait! Don't Miss Out!
                        </h3>
                        <button @click="closeExitIntent()" class="text-white hover:text-gray-200">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                
                <div class="bg-white px-6 py-4">
                    <div class="text-center mb-4">
                        <p class="text-gray-700 text-lg font-semibold">Get a FREE Website Audit Worth ₹5,000!</p>
                        <p class="text-gray-600 mt-2">Before you go, let us show you how to improve your website's performance and get more customers.</p>
                    </div>
                    
                    <form @submit.prevent="submitExitIntentForm()" class="space-y-4">
                        <div>
                            <input type="text" x-model="exitIntentForm.name" placeholder="Your Name" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        </div>
                        <div>
                            <input type="email" x-model="exitIntentForm.email" placeholder="Your Email" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        </div>
                        <div>
                            <input type="url" x-model="exitIntentForm.website" placeholder="Your Website URL (Optional)"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        </div>
                        
                        <button type="submit" :disabled="exitIntentLoading"
                                class="w-full bg-primary-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-700 transition-colors disabled:opacity-50">
                            <span x-show="!exitIntentLoading">Get My FREE Audit 🚀</span>
                            <span x-show="exitIntentLoading">Processing...</span>
                        </button>
                        
                        <p class="text-xs text-gray-500 text-center">
                            No spam. We'll send you a detailed audit report within 24 hours.
                        </p>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scroll-Based CTA -->
    <div x-show="showScrollCTA"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform translate-y-full"
         x-transition:enter-end="opacity-100 transform translate-y-0"
         class="fixed bottom-4 right-4 z-[55] max-w-sm"
         style="display: none;">
        
        <div class="bg-white rounded-lg shadow-xl border border-gray-200 p-4">
            <div class="flex items-center justify-between mb-2">
                <h4 class="font-semibold text-gray-900">Ready to Get Started?</h4>
                <button @click="closeScrollCTA()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <p class="text-sm text-gray-600 mb-3">Get a free consultation and see how we can help grow your business.</p>
            <div class="flex space-x-2">
                <a href="{{ route('quote') }}" 
                   @click="trackConversion('scroll_cta_quote')"
                   class="flex-1 bg-primary-600 text-white text-sm py-2 px-3 rounded text-center hover:bg-primary-700 transition-colors">
                    Get Quote
                </a>
                <a href="tel:7010860889" 
                   @click="trackConversion('scroll_cta_call')"
                   class="flex-1 bg-green-600 text-white text-sm py-2 px-3 rounded text-center hover:bg-green-700 transition-colors">
                    Call Now
                </a>
            </div>
        </div>
    </div>

    <!-- Time-Delayed Offer -->
    <div x-show="showTimeDelayedOffer"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         class="fixed top-4 right-4 z-[60] max-w-sm"
         style="display: none;">
        
        <div class="bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg shadow-xl p-4">
            <div class="flex items-center justify-between mb-2">
                <h4 class="font-bold">⏰ Limited Time Offer!</h4>
                <button @click="closeTimeDelayedOffer()" class="text-white hover:text-gray-200">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            <p class="text-sm mb-3">Get 20% OFF on your first project! Valid for the next 48 hours.</p>
            <a href="{{ route('quote') }}" 
               @click="trackConversion('time_delayed_offer')"
               class="block w-full bg-white text-orange-600 text-sm py-2 px-3 rounded text-center font-semibold hover:bg-gray-100 transition-colors">
                Claim Offer Now
            </a>
        </div>
    </div>

    <!-- Social Proof Notifications -->
    <div x-show="showSocialProof"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform translate-x-full"
         x-transition:enter-end="opacity-100 transform translate-x-0"
         class="fixed bottom-4 left-4 z-[55] max-w-sm"
         style="display: none;">
        
        <div class="bg-white rounded-lg shadow-xl border border-gray-200 p-4">
            <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900" x-text="socialProofMessage"></p>
                    <p class="text-xs text-gray-500">Just now</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function leadGenerationEngine() {
    return {
        showExitIntent: false,
        showScrollCTA: false,
        showTimeDelayedOffer: false,
        showSocialProof: false,
        exitIntentTriggered: false,
        scrollCTATriggered: false,
        timeDelayedOfferTriggered: false,
        exitIntentLoading: false,
        socialProofMessage: '',
        socialProofMessages: [
            'Someone from Chennai just requested a quote',
            'A business in Bangalore signed up for our services',
            'Someone from Mumbai downloaded our portfolio',
            'A startup in Delhi just booked a consultation',
            'Someone from Pune requested a website audit'
        ],
        
        exitIntentForm: {
            name: '',
            email: '',
            website: ''
        },
        
        init() {
            this.setupExitIntent();
            this.setupScrollTracking();
            this.setupTimeDelayedOffer();
            this.setupSocialProof();
        },
        
        setupExitIntent() {
            let exitIntentTriggered = false;
            document.addEventListener('mouseleave', (e) => {
                if (e.clientY <= 0 && !exitIntentTriggered && !this.exitIntentTriggered) {
                    this.showExitIntent = true;
                    this.exitIntentTriggered = true;
                    exitIntentTriggered = true;
                    this.trackEvent('exit_intent_triggered');
                }
            });
        },
        
        setupScrollTracking() {
            let scrollTriggered = false;
            window.addEventListener('scroll', () => {
                const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
                
                if (scrollPercent > 50 && !scrollTriggered && !this.scrollCTATriggered) {
                    setTimeout(() => {
                        this.showScrollCTA = true;
                        this.scrollCTATriggered = true;
                        scrollTriggered = true;
                        this.trackEvent('scroll_cta_triggered');
                    }, 2000);
                }
            });
        },
        
        setupTimeDelayedOffer() {
            setTimeout(() => {
                if (!this.timeDelayedOfferTriggered) {
                    this.showTimeDelayedOffer = true;
                    this.timeDelayedOfferTriggered = true;
                    this.trackEvent('time_delayed_offer_triggered');
                }
            }, 30000); // Show after 30 seconds
        },
        
        setupSocialProof() {
            this.showRandomSocialProof();
            setInterval(() => {
                this.showRandomSocialProof();
            }, 15000); // Show every 15 seconds
        },
        
        showRandomSocialProof() {
            if (Math.random() > 0.7) { // 30% chance
                this.socialProofMessage = this.socialProofMessages[Math.floor(Math.random() * this.socialProofMessages.length)];
                this.showSocialProof = true;
                
                setTimeout(() => {
                    this.showSocialProof = false;
                }, 5000);
            }
        },
        
        async submitExitIntentForm() {
            this.exitIntentLoading = true;
            
            try {
                const response = await fetch('/api/leads', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        name: this.exitIntentForm.name,
                        email: this.exitIntentForm.email,
                        website: this.exitIntentForm.website,
                        source: 'exit_intent_popup',
                        service_interest: 'website_audit',
                        message: 'Requested free website audit via exit intent popup'
                    })
                });
                
                if (response.ok) {
                    this.trackConversion('exit_intent_lead', 5000);
                    this.showExitIntent = false;
                    
                    // Show success message
                    alert('Thank you! We\'ll send your free website audit within 24 hours.');
                } else {
                    throw new Error('Failed to submit form');
                }
            } catch (error) {
                alert('Something went wrong. Please try again or call us at 7010860889.');
            } finally {
                this.exitIntentLoading = false;
            }
        },
        
        closeExitIntent() {
            this.showExitIntent = false;
            this.trackEvent('exit_intent_closed');
        },
        
        closeScrollCTA() {
            this.showScrollCTA = false;
            this.trackEvent('scroll_cta_closed');
        },
        
        closeTimeDelayedOffer() {
            this.showTimeDelayedOffer = false;
            this.trackEvent('time_delayed_offer_closed');
        },
        
        trackEvent(event, data = {}) {
            if (window.bhavitechAnalytics) {
                window.bhavitechAnalytics.trackEvent(event, data);
            }
        },
        
        trackConversion(type, value = 0) {
            if (window.bhavitechAnalytics) {
                window.bhavitechAnalytics.trackConversion(type, value);
            }
        }
    }
}
</script>
