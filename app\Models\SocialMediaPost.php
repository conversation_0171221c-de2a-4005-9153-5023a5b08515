<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class SocialMediaPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'media_urls',
        'hashtags',
        'status',
        'scheduled_at',
        'published_at',
        'platform_posts',
        'analytics',
        'campaign_id',
        'post_type',
        'targeting',
        'boost_post',
        'boost_budget',
        'cross_post_platforms',
        'failure_reason',
        'retry_count',
        'created_by',
    ];

    protected $casts = [
        'media_urls' => 'array',
        'hashtags' => 'array',
        'platform_posts' => 'array',
        'analytics' => 'array',
        'targeting' => 'array',
        'cross_post_platforms' => 'array',
        'boost_post' => 'boolean',
        'boost_budget' => 'decimal:2',
        'scheduled_at' => 'datetime',
        'published_at' => 'datetime',
    ];

    // Relationships
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    public function scopeReadyToPost($query)
    {
        return $query->where('status', 'scheduled')
            ->where('scheduled_at', '<=', now());
    }

    public function scopeByCampaign($query, $campaignId)
    {
        return $query->where('campaign_id', $campaignId);
    }

    // Methods
    public function isScheduled(): bool
    {
        return $this->status === 'scheduled';
    }

    public function isPublished(): bool
    {
        return $this->status === 'published';
    }

    public function isDraft(): bool
    {
        return $this->status === 'draft';
    }

    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    public function isReadyToPost(): bool
    {
        return $this->isScheduled() && $this->scheduled_at <= now();
    }

    public function canRetry(): bool
    {
        return $this->isFailed() && $this->retry_count < 3;
    }

    public function markAsPublished(array $platformPosts = []): void
    {
        $this->update([
            'status' => 'published',
            'published_at' => now(),
            'platform_posts' => array_merge($this->platform_posts ?? [], $platformPosts),
        ]);
    }

    public function markAsFailed(string $reason): void
    {
        $this->update([
            'status' => 'failed',
            'failure_reason' => $reason,
            'retry_count' => $this->retry_count + 1,
        ]);
    }

    public function updateAnalytics(array $analytics): void
    {
        $this->update([
            'analytics' => array_merge($this->analytics ?? [], $analytics),
        ]);
    }

    public function getFormattedContent(): string
    {
        $content = $this->content;

        // Add hashtags if they exist
        if ($this->hashtags && count($this->hashtags) > 0) {
            $content .= "\n\n" . implode(' ', array_map(fn($tag) => "#{$tag}", $this->hashtags));
        }

        return $content;
    }

    public function getTotalEngagement(): int
    {
        $analytics = $this->analytics ?? [];

        return ($analytics['likes'] ?? 0) +
               ($analytics['comments'] ?? 0) +
               ($analytics['shares'] ?? 0) +
               ($analytics['clicks'] ?? 0);
    }

    public function getTotalReach(): int
    {
        $analytics = $this->analytics ?? [];
        return $analytics['reach'] ?? 0;
    }

    public function getTotalImpressions(): int
    {
        $analytics = $this->analytics ?? [];
        return $analytics['impressions'] ?? 0;
    }

    public function getEngagementRate(): float
    {
        $impressions = $this->getTotalImpressions();
        $engagement = $this->getTotalEngagement();

        return $impressions > 0 ? ($engagement / $impressions) * 100 : 0;
    }

    public function getLikes(): int
    {
        $analytics = $this->analytics ?? [];
        return $analytics['likes'] ?? 0;
    }

    public function getComments(): int
    {
        $analytics = $this->analytics ?? [];
        return $analytics['comments'] ?? 0;
    }

    public function getShares(): int
    {
        $analytics = $this->analytics ?? [];
        return $analytics['shares'] ?? 0;
    }

    public function getClicks(): int
    {
        $analytics = $this->analytics ?? [];
        return $analytics['clicks'] ?? 0;
    }

    // Accessor attributes for easier use in Filament and other places
    public function getTotalEngagementAttribute(): int
    {
        return $this->getTotalEngagement();
    }

    public function getTotalReachAttribute(): int
    {
        return $this->getTotalReach();
    }

    public function getTotalImpressionsAttribute(): int
    {
        return $this->getTotalImpressions();
    }

    public function getEngagementRateAttribute(): float
    {
        return $this->getEngagementRate();
    }
}
