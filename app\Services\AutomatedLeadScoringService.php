<?php

namespace App\Services;

use App\Models\Lead;
use App\Models\LeadInteraction;
use App\Models\AnalyticsEvent;
use App\Models\EmailCampaignRecipient;
use App\Models\WhatsAppMessage;
use App\Models\BusinessSetting;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class AutomatedLeadScoringService
{
    protected array $scoringRules = [
        'demographic' => [
            'company_size' => ['weight' => 15, 'max_score' => 20],
            'industry_match' => ['weight' => 10, 'max_score' => 15],
            'location' => ['weight' => 5, 'max_score' => 10],
            'contact_completeness' => ['weight' => 8, 'max_score' => 10],
        ],
        'behavioral' => [
            'website_engagement' => ['weight' => 20, 'max_score' => 25],
            'email_engagement' => ['weight' => 15, 'max_score' => 20],
            'whatsapp_engagement' => ['weight' => 12, 'max_score' => 15],
            'social_media_engagement' => ['weight' => 8, 'max_score' => 10],
        ],
        'interaction' => [
            'response_speed' => ['weight' => 10, 'max_score' => 15],
            'interaction_frequency' => ['weight' => 12, 'max_score' => 15],
            'content_consumption' => ['weight' => 8, 'max_score' => 10],
            'form_submissions' => ['weight' => 15, 'max_score' => 20],
        ],
        'intent' => [
            'service_page_views' => ['weight' => 18, 'max_score' => 25],
            'pricing_inquiries' => ['weight' => 20, 'max_score' => 25],
            'quote_requests' => ['weight' => 25, 'max_score' => 30],
            'direct_contact_attempts' => ['weight' => 22, 'max_score' => 25],
        ],
    ];

    public function calculateLeadScore(Lead $lead): array
    {
        $scores = [
            'demographic' => $this->calculateDemographicScore($lead),
            'behavioral' => $this->calculateBehavioralScore($lead),
            'interaction' => $this->calculateInteractionScore($lead),
            'intent' => $this->calculateIntentScore($lead),
        ];

        $totalScore = array_sum($scores);
        $maxPossibleScore = $this->getMaxPossibleScore();
        $normalizedScore = min(100, ($totalScore / $maxPossibleScore) * 100);

        $leadGrade = $this->determineLeadGrade($normalizedScore);
        $priority = $this->determinePriority($normalizedScore, $lead);

        return [
            'total_score' => round($normalizedScore, 2),
            'category_scores' => $scores,
            'lead_grade' => $leadGrade,
            'priority' => $priority,
            'scoring_factors' => $this->getDetailedScoringFactors($lead),
            'recommendations' => $this->generateRecommendations($lead, $normalizedScore),
            'next_actions' => $this->suggestNextActions($lead, $normalizedScore),
        ];
    }

    protected function calculateDemographicScore(Lead $lead): float
    {
        $score = 0;

        // Company size scoring
        $companySize = $this->estimateCompanySize($lead);
        $score += $this->scoringRules['demographic']['company_size']['weight'] * 
                 $this->getCompanySizeMultiplier($companySize);

        // Industry match scoring
        $industryMatch = $this->calculateIndustryMatch($lead);
        $score += $this->scoringRules['demographic']['industry_match']['weight'] * $industryMatch;

        // Location scoring (preference for local/regional clients)
        $locationScore = $this->calculateLocationScore($lead);
        $score += $this->scoringRules['demographic']['location']['weight'] * $locationScore;

        // Contact completeness
        $completeness = $this->calculateContactCompleteness($lead);
        $score += $this->scoringRules['demographic']['contact_completeness']['weight'] * $completeness;

        return $score;
    }

    protected function calculateBehavioralScore(Lead $lead): float
    {
        $score = 0;

        // Website engagement
        $websiteEngagement = $this->calculateWebsiteEngagement($lead);
        $score += $this->scoringRules['behavioral']['website_engagement']['weight'] * $websiteEngagement;

        // Email engagement
        $emailEngagement = $this->calculateEmailEngagement($lead);
        $score += $this->scoringRules['behavioral']['email_engagement']['weight'] * $emailEngagement;

        // WhatsApp engagement
        $whatsappEngagement = $this->calculateWhatsAppEngagement($lead);
        $score += $this->scoringRules['behavioral']['whatsapp_engagement']['weight'] * $whatsappEngagement;

        // Social media engagement
        $socialEngagement = $this->calculateSocialMediaEngagement($lead);
        $score += $this->scoringRules['behavioral']['social_media_engagement']['weight'] * $socialEngagement;

        return $score;
    }

    protected function calculateInteractionScore(Lead $lead): float
    {
        $score = 0;

        // Response speed
        $responseSpeed = $this->calculateResponseSpeed($lead);
        $score += $this->scoringRules['interaction']['response_speed']['weight'] * $responseSpeed;

        // Interaction frequency
        $frequency = $this->calculateInteractionFrequency($lead);
        $score += $this->scoringRules['interaction']['interaction_frequency']['weight'] * $frequency;

        // Content consumption
        $contentConsumption = $this->calculateContentConsumption($lead);
        $score += $this->scoringRules['interaction']['content_consumption']['weight'] * $contentConsumption;

        // Form submissions
        $formSubmissions = $this->calculateFormSubmissions($lead);
        $score += $this->scoringRules['interaction']['form_submissions']['weight'] * $formSubmissions;

        return $score;
    }

    protected function calculateIntentScore(Lead $lead): float
    {
        $score = 0;

        // Service page views
        $serviceViews = $this->calculateServicePageViews($lead);
        $score += $this->scoringRules['intent']['service_page_views']['weight'] * $serviceViews;

        // Pricing inquiries
        $pricingInquiries = $this->calculatePricingInquiries($lead);
        $score += $this->scoringRules['intent']['pricing_inquiries']['weight'] * $pricingInquiries;

        // Quote requests
        $quoteRequests = $this->calculateQuoteRequests($lead);
        $score += $this->scoringRules['intent']['quote_requests']['weight'] * $quoteRequests;

        // Direct contact attempts
        $contactAttempts = $this->calculateDirectContactAttempts($lead);
        $score += $this->scoringRules['intent']['direct_contact_attempts']['weight'] * $contactAttempts;

        return $score;
    }

    // Demographic scoring methods
    protected function estimateCompanySize(Lead $lead): string
    {
        $company = strtolower($lead->company ?? '');
        
        // Keywords that suggest company size
        $largeCompanyKeywords = ['ltd', 'limited', 'corporation', 'corp', 'inc', 'pvt', 'private'];
        $mediumCompanyKeywords = ['solutions', 'services', 'technologies', 'systems'];
        
        foreach ($largeCompanyKeywords as $keyword) {
            if (str_contains($company, $keyword)) {
                return 'large';
            }
        }
        
        foreach ($mediumCompanyKeywords as $keyword) {
            if (str_contains($company, $keyword)) {
                return 'medium';
            }
        }
        
        return 'small';
    }

    protected function getCompanySizeMultiplier(string $size): float
    {
        return match ($size) {
            'large' => 1.0,
            'medium' => 0.8,
            'small' => 0.6,
            default => 0.4,
        };
    }

    protected function calculateIndustryMatch(Lead $lead): float
    {
        $targetIndustries = ['technology', 'software', 'digital', 'startup', 'ecommerce', 'retail'];
        $company = strtolower($lead->company ?? '');
        $serviceInterest = strtolower($lead->service_interest ?? '');
        
        foreach ($targetIndustries as $industry) {
            if (str_contains($company, $industry) || str_contains($serviceInterest, $industry)) {
                return 1.0;
            }
        }
        
        return 0.5;
    }

    protected function calculateLocationScore(Lead $lead): float
    {
        $preferredLocations = ['salem', 'chennai', 'bangalore', 'coimbatore', 'tamil nadu', 'india'];
        $location = strtolower($lead->city ?? $lead->state ?? '');
        
        foreach ($preferredLocations as $preferred) {
            if (str_contains($location, $preferred)) {
                return 1.0;
            }
        }
        
        return 0.3; // International or other locations
    }

    protected function calculateContactCompleteness(Lead $lead): float
    {
        $fields = ['name', 'email', 'phone', 'company', 'service_interest'];
        $completedFields = 0;
        
        foreach ($fields as $field) {
            if (!empty($lead->$field)) {
                $completedFields++;
            }
        }
        
        return $completedFields / count($fields);
    }

    // Behavioral scoring methods
    protected function calculateWebsiteEngagement(Lead $lead): float
    {
        if (!$lead->visitor_id) {
            return 0;
        }

        $events = AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->where('created_at', '>=', now()->subDays(30))
            ->get();

        $pageViews = $events->where('event_name', 'page_view')->count();
        $timeOnSite = $events->sum('session_duration') / 60; // Convert to minutes
        $uniqueSessions = $events->unique('session_id')->count();

        // Scoring based on engagement level
        $score = 0;
        $score += min(0.4, $pageViews / 20); // Up to 0.4 for page views
        $score += min(0.3, $timeOnSite / 30); // Up to 0.3 for time on site
        $score += min(0.3, $uniqueSessions / 5); // Up to 0.3 for return visits

        return min(1.0, $score);
    }

    protected function calculateEmailEngagement(Lead $lead): float
    {
        $emailRecipients = EmailCampaignRecipient::where('email', $lead->email)
            ->where('sent_at', '>=', now()->subDays(30))
            ->get();

        if ($emailRecipients->isEmpty()) {
            return 0;
        }

        $totalSent = $emailRecipients->count();
        $opened = $emailRecipients->where('opened_at', '!=', null)->count();
        $clicked = $emailRecipients->where('clicked_at', '!=', null)->count();

        $openRate = $totalSent > 0 ? $opened / $totalSent : 0;
        $clickRate = $totalSent > 0 ? $clicked / $totalSent : 0;

        return min(1.0, ($openRate * 0.6) + ($clickRate * 0.4));
    }

    protected function calculateWhatsAppEngagement(Lead $lead): float
    {
        if (!$lead->phone) {
            return 0;
        }

        $messages = WhatsAppMessage::where('recipient_phone', $lead->phone)
            ->where('sent_at', '>=', now()->subDays(30))
            ->get();

        if ($messages->isEmpty()) {
            return 0;
        }

        $totalSent = $messages->count();
        $delivered = $messages->where('status', 'delivered')->count();
        $read = $messages->where('status', 'read')->count();

        $deliveryRate = $totalSent > 0 ? $delivered / $totalSent : 0;
        $readRate = $totalSent > 0 ? $read / $totalSent : 0;

        // Check for replies (interactions)
        $replies = $lead->interactions()
            ->where('channel', 'whatsapp')
            ->where('type', 'whatsapp_reply')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        $replyRate = $totalSent > 0 ? min(1, $replies / $totalSent) : 0;

        return min(1.0, ($deliveryRate * 0.3) + ($readRate * 0.4) + ($replyRate * 0.3));
    }

    protected function calculateSocialMediaEngagement(Lead $lead): float
    {
        // This would integrate with social media APIs to track engagement
        // For now, return a base score
        return 0.2;
    }

    // Interaction scoring methods
    protected function calculateResponseSpeed(Lead $lead): float
    {
        $interactions = $lead->interactions()
            ->where('created_at', '>=', now()->subDays(30))
            ->orderBy('created_at')
            ->get();

        if ($interactions->count() < 2) {
            return 0.5; // Default score for insufficient data
        }

        $responseTimes = [];
        for ($i = 1; $i < $interactions->count(); $i++) {
            $timeDiff = $interactions[$i]->created_at->diffInHours($interactions[$i-1]->created_at);
            $responseTimes[] = $timeDiff;
        }

        $avgResponseTime = array_sum($responseTimes) / count($responseTimes);

        // Score based on response speed (faster = higher score)
        if ($avgResponseTime <= 1) return 1.0;
        if ($avgResponseTime <= 4) return 0.8;
        if ($avgResponseTime <= 24) return 0.6;
        if ($avgResponseTime <= 72) return 0.4;
        return 0.2;
    }

    protected function calculateInteractionFrequency(Lead $lead): float
    {
        $interactions = $lead->interactions()
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        // Score based on interaction frequency
        if ($interactions >= 10) return 1.0;
        if ($interactions >= 7) return 0.8;
        if ($interactions >= 5) return 0.6;
        if ($interactions >= 3) return 0.4;
        if ($interactions >= 1) return 0.2;
        return 0;
    }

    protected function calculateContentConsumption(Lead $lead): float
    {
        if (!$lead->visitor_id) {
            return 0;
        }

        $contentViews = AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->where('event_name', 'page_view')
            ->where('page_url', 'like', '%blog%')
            ->orWhere('page_url', 'like', '%portfolio%')
            ->orWhere('page_url', 'like', '%case-study%')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return min(1.0, $contentViews / 5);
    }

    protected function calculateFormSubmissions(Lead $lead): float
    {
        $formSubmissions = $lead->interactions()
            ->where('type', 'form_submit')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return min(1.0, $formSubmissions / 3);
    }

    // Intent scoring methods
    protected function calculateServicePageViews(Lead $lead): float
    {
        if (!$lead->visitor_id) {
            return 0;
        }

        $serviceViews = AnalyticsEvent::where('visitor_id', $lead->visitor_id)
            ->where('event_name', 'page_view')
            ->where('page_url', 'like', '%/services/%')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return min(1.0, $serviceViews / 5);
    }

    protected function calculatePricingInquiries(Lead $lead): float
    {
        $pricingInteractions = $lead->interactions()
            ->where('description', 'like', '%pricing%')
            ->orWhere('description', 'like', '%cost%')
            ->orWhere('description', 'like', '%quote%')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return min(1.0, $pricingInteractions / 2);
    }

    protected function calculateQuoteRequests(Lead $lead): float
    {
        $quoteRequests = $lead->interactions()
            ->where('type', 'quote_request')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return min(1.0, $quoteRequests / 1); // Even one quote request is high intent
    }

    protected function calculateDirectContactAttempts(Lead $lead): float
    {
        $contactAttempts = $lead->interactions()
            ->whereIn('type', ['phone_call', 'email_reply', 'whatsapp_reply'])
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return min(1.0, $contactAttempts / 3);
    }

    protected function getMaxPossibleScore(): float
    {
        $maxScore = 0;
        foreach ($this->scoringRules as $category => $rules) {
            foreach ($rules as $rule => $config) {
                $maxScore += $config['weight'];
            }
        }
        return $maxScore;
    }

    protected function determineLeadGrade(float $score): string
    {
        if ($score >= 80) return 'A+';
        if ($score >= 70) return 'A';
        if ($score >= 60) return 'B+';
        if ($score >= 50) return 'B';
        if ($score >= 40) return 'C+';
        if ($score >= 30) return 'C';
        if ($score >= 20) return 'D';
        return 'F';
    }

    protected function determinePriority(float $score, Lead $lead): string
    {
        // Consider both score and recency
        $daysSinceCreated = $lead->created_at->diffInDays(now());
        
        if ($score >= 70 && $daysSinceCreated <= 3) return 'urgent';
        if ($score >= 60) return 'high';
        if ($score >= 40) return 'medium';
        if ($score >= 20) return 'low';
        return 'very_low';
    }

    protected function getDetailedScoringFactors(Lead $lead): array
    {
        return [
            'demographic_factors' => [
                'company_size' => $this->estimateCompanySize($lead),
                'industry_match' => $this->calculateIndustryMatch($lead),
                'location_preference' => $this->calculateLocationScore($lead),
                'profile_completeness' => $this->calculateContactCompleteness($lead),
            ],
            'behavioral_factors' => [
                'website_activity' => $this->calculateWebsiteEngagement($lead),
                'email_responsiveness' => $this->calculateEmailEngagement($lead),
                'whatsapp_engagement' => $this->calculateWhatsAppEngagement($lead),
            ],
            'interaction_factors' => [
                'response_timeliness' => $this->calculateResponseSpeed($lead),
                'communication_frequency' => $this->calculateInteractionFrequency($lead),
                'content_interest' => $this->calculateContentConsumption($lead),
            ],
            'intent_indicators' => [
                'service_research' => $this->calculateServicePageViews($lead),
                'pricing_interest' => $this->calculatePricingInquiries($lead),
                'quote_requests' => $this->calculateQuoteRequests($lead),
                'direct_contact' => $this->calculateDirectContactAttempts($lead),
            ],
        ];
    }

    protected function generateRecommendations(Lead $lead, float $score): array
    {
        $recommendations = [];

        if ($score >= 70) {
            $recommendations[] = 'High-priority lead - Schedule immediate follow-up call';
            $recommendations[] = 'Prepare personalized proposal based on service interest';
            $recommendations[] = 'Assign to senior sales representative';
        } elseif ($score >= 50) {
            $recommendations[] = 'Qualified lead - Follow up within 24 hours';
            $recommendations[] = 'Send relevant case studies and portfolio items';
            $recommendations[] = 'Schedule discovery call to understand requirements';
        } elseif ($score >= 30) {
            $recommendations[] = 'Nurture lead with educational content';
            $recommendations[] = 'Add to email automation sequence';
            $recommendations[] = 'Monitor for increased engagement signals';
        } else {
            $recommendations[] = 'Low-priority lead - Add to long-term nurture campaign';
            $recommendations[] = 'Focus on providing value through content marketing';
            $recommendations[] = 'Re-evaluate after 30 days of nurturing';
        }

        return $recommendations;
    }

    protected function suggestNextActions(Lead $lead, float $score): array
    {
        $actions = [];

        if ($score >= 70) {
            $actions[] = ['action' => 'schedule_call', 'priority' => 'urgent', 'timeline' => 'within 2 hours'];
            $actions[] = ['action' => 'prepare_proposal', 'priority' => 'high', 'timeline' => 'within 24 hours'];
            $actions[] = ['action' => 'send_welcome_package', 'priority' => 'medium', 'timeline' => 'within 4 hours'];
        } elseif ($score >= 50) {
            $actions[] = ['action' => 'send_follow_up_email', 'priority' => 'high', 'timeline' => 'within 24 hours'];
            $actions[] = ['action' => 'share_case_studies', 'priority' => 'medium', 'timeline' => 'within 48 hours'];
            $actions[] = ['action' => 'schedule_discovery_call', 'priority' => 'medium', 'timeline' => 'within 3 days'];
        } elseif ($score >= 30) {
            $actions[] = ['action' => 'add_to_nurture_sequence', 'priority' => 'medium', 'timeline' => 'within 24 hours'];
            $actions[] = ['action' => 'send_educational_content', 'priority' => 'low', 'timeline' => 'within 1 week'];
        } else {
            $actions[] = ['action' => 'add_to_long_term_nurture', 'priority' => 'low', 'timeline' => 'within 1 week'];
        }

        return $actions;
    }

    public function updateLeadScore(Lead $lead): Lead
    {
        $scoringResult = $this->calculateLeadScore($lead);
        
        $lead->update([
            'score' => $scoringResult['total_score'],
            'grade' => $scoringResult['lead_grade'],
            'priority' => $scoringResult['priority'],
            'scoring_data' => [
                'category_scores' => $scoringResult['category_scores'],
                'scoring_factors' => $scoringResult['scoring_factors'],
                'last_scored_at' => now(),
            ],
        ]);

        // Log scoring update
        Log::info('Lead score updated', [
            'lead_id' => $lead->id,
            'old_score' => $lead->getOriginal('score'),
            'new_score' => $scoringResult['total_score'],
            'grade' => $scoringResult['lead_grade'],
            'priority' => $scoringResult['priority'],
        ]);

        return $lead->fresh();
    }

    public function batchUpdateLeadScores(array $leadIds = null): array
    {
        $query = Lead::query();
        
        if ($leadIds) {
            $query->whereIn('id', $leadIds);
        } else {
            // Update leads that haven't been scored in the last 24 hours
            $query->where(function ($q) {
                $q->whereNull('scoring_data->last_scored_at')
                  ->orWhere('scoring_data->last_scored_at', '<', now()->subDay());
            });
        }

        $leads = $query->get();
        $results = [
            'updated' => 0,
            'errors' => 0,
            'details' => [],
        ];

        foreach ($leads as $lead) {
            try {
                $this->updateLeadScore($lead);
                $results['updated']++;
                $results['details'][] = [
                    'lead_id' => $lead->id,
                    'status' => 'updated',
                    'score' => $lead->score,
                ];
            } catch (\Exception $e) {
                $results['errors']++;
                $results['details'][] = [
                    'lead_id' => $lead->id,
                    'status' => 'error',
                    'error' => $e->getMessage(),
                ];
                
                Log::error('Failed to update lead score', [
                    'lead_id' => $lead->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $results;
    }
}
