<?php

namespace App\Http\Controllers;

use App\Services\EmailMarketingService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class EmailTrackingController extends Controller
{
    public function __construct(
        protected EmailMarketingService $emailService
    ) {}

    public function trackOpen(Request $request, string $token): Response
    {
        $this->emailService->trackEmailOpen($token);

        // Return a 1x1 transparent pixel
        $pixel = base64_decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7');

        return response($pixel, 200, [
            'Content-Type' => 'image/gif',
            'Content-Length' => strlen($pixel),
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }

    public function trackClick(Request $request, string $token): \Illuminate\Http\RedirectResponse
    {
        $url = $request->get('url', config('app.url'));

        $this->emailService->trackEmailClick($token, $url);

        return redirect($url);
    }

    public function unsubscribe(Request $request, string $token): \Illuminate\Contracts\View\View
    {
        $success = $this->emailService->unsubscribeRecipient($token);

        return view('email.unsubscribe', [
            'success' => $success,
            'company_name' => config('app.name', 'Bhavitech'),
        ]);
    }

    public function handleUnsubscribe(Request $request, string $token): \Illuminate\Http\RedirectResponse
    {
        $success = $this->emailService->unsubscribeRecipient($token);

        return redirect()->route('email.unsubscribe', $token)
            ->with('message', $success ? 'You have been unsubscribed successfully.' : 'Unable to process unsubscribe request.');
    }
}
