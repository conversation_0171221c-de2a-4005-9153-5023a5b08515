<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations for handling 1 crore+ emails efficiently.
     */
    public function up(): void
    {
        Schema::table('contacts', function (Blueprint $table) {
            // Geographic indexes for filtering
            $table->index(['country', 'state', 'city'], 'idx_contacts_geography');
            $table->index(['state', 'city'], 'idx_contacts_state_city');
            $table->index('country', 'idx_contacts_country');
            $table->index('state', 'idx_contacts_state');
            $table->index('city', 'idx_contacts_city');
            
            // Email marketing indexes
            $table->index(['is_subscribed', 'is_active'], 'idx_contacts_subscription_status');
            $table->index(['engagement_score', 'is_subscribed'], 'idx_contacts_engagement');
            $table->index(['industry', 'is_subscribed'], 'idx_contacts_industry_subscribed');
            $table->index(['source', 'created_at'], 'idx_contacts_source_date');
            
            // Performance indexes
            $table->index('last_interaction_at', 'idx_contacts_last_interaction');
            $table->index(['created_at', 'is_active'], 'idx_contacts_created_active');
            $table->index(['updated_at', 'is_subscribed'], 'idx_contacts_updated_subscribed');
        });

        Schema::table('contact_list_members', function (Blueprint $table) {
            // Optimize list membership queries
            $table->index(['contact_list_id', 'status', 'subscribed_at'], 'idx_list_members_status');
            $table->index(['status', 'subscribed_at'], 'idx_list_members_subscription');
            $table->index('subscription_source', 'idx_list_members_source');
        });

        Schema::table('email_accounts', function (Blueprint $table) {
            // Email sending optimization
            $table->index(['is_active', 'daily_send_limit'], 'idx_email_accounts_active_limit');
            $table->index(['is_primary', 'is_active'], 'idx_email_accounts_primary_active');
            $table->index('emails_sent_today', 'idx_email_accounts_sent_today');
        });

        Schema::table('email_campaigns', function (Blueprint $table) {
            // Campaign management indexes
            $table->index(['status', 'scheduled_at'], 'idx_campaigns_status_schedule');
            $table->index(['type', 'status'], 'idx_campaigns_type_status');
            $table->index(['created_by', 'status'], 'idx_campaigns_creator_status');
        });

        // Create a separate table for email sending queue to handle massive volumes
        Schema::create('email_sending_queue', function (Blueprint $table) {
            $table->id();
            $table->foreignId('email_campaign_id')->constrained('email_campaigns')->onDelete('cascade');
            $table->foreignId('contact_id')->constrained('contacts')->onDelete('cascade');
            $table->foreignId('email_account_id')->constrained('email_accounts')->onDelete('cascade');
            $table->enum('status', ['pending', 'sending', 'sent', 'failed', 'bounced'])->default('pending');
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->text('error_message')->nullable();
            $table->json('metadata')->nullable(); // Store additional data like personalization
            $table->timestamps();

            // Indexes for massive email processing
            $table->index(['status', 'scheduled_at'], 'idx_queue_status_schedule');
            $table->index(['email_account_id', 'status'], 'idx_queue_account_status');
            $table->index(['email_campaign_id', 'status'], 'idx_queue_campaign_status');
            $table->index(['contact_id', 'status'], 'idx_queue_contact_status');
            $table->index('scheduled_at', 'idx_queue_scheduled');
            $table->index(['status', 'created_at'], 'idx_queue_status_created');
        });

        // Create daily sending limits tracking table
        Schema::create('daily_sending_limits', function (Blueprint $table) {
            $table->id();
            $table->foreignId('email_account_id')->constrained('email_accounts')->onDelete('cascade');
            $table->date('date');
            $table->integer('emails_sent')->default(0);
            $table->integer('daily_limit');
            $table->timestamps();

            $table->unique(['email_account_id', 'date'], 'unique_account_date');
            $table->index(['date', 'emails_sent'], 'idx_daily_limits_date_sent');
        });

        // Create geographic email distribution table for analytics
        Schema::create('geographic_email_stats', function (Blueprint $table) {
            $table->id();
            $table->string('country');
            $table->string('state')->nullable();
            $table->string('city')->nullable();
            $table->integer('total_contacts')->default(0);
            $table->integer('subscribed_contacts')->default(0);
            $table->integer('emails_sent_today')->default(0);
            $table->integer('emails_sent_this_month')->default(0);
            $table->decimal('engagement_rate', 5, 2)->default(0);
            $table->date('last_updated');
            $table->timestamps();

            $table->index(['country', 'state', 'city'], 'idx_geo_stats_location');
            $table->index(['country', 'total_contacts'], 'idx_geo_stats_country_total');
            $table->index('last_updated', 'idx_geo_stats_updated');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('geographic_email_stats');
        Schema::dropIfExists('daily_sending_limits');
        Schema::dropIfExists('email_sending_queue');

        Schema::table('email_campaigns', function (Blueprint $table) {
            $table->dropIndex('idx_campaigns_status_schedule');
            $table->dropIndex('idx_campaigns_type_status');
            $table->dropIndex('idx_campaigns_creator_status');
        });

        Schema::table('email_accounts', function (Blueprint $table) {
            $table->dropIndex('idx_email_accounts_active_limit');
            $table->dropIndex('idx_email_accounts_primary_active');
            $table->dropIndex('idx_email_accounts_sent_today');
        });

        Schema::table('contact_list_members', function (Blueprint $table) {
            $table->dropIndex('idx_list_members_status');
            $table->dropIndex('idx_list_members_subscription');
            $table->dropIndex('idx_list_members_source');
        });

        Schema::table('contacts', function (Blueprint $table) {
            $table->dropIndex('idx_contacts_geography');
            $table->dropIndex('idx_contacts_state_city');
            $table->dropIndex('idx_contacts_country');
            $table->dropIndex('idx_contacts_state');
            $table->dropIndex('idx_contacts_city');
            $table->dropIndex('idx_contacts_subscription_status');
            $table->dropIndex('idx_contacts_engagement');
            $table->dropIndex('idx_contacts_industry_subscribed');
            $table->dropIndex('idx_contacts_source_date');
            $table->dropIndex('idx_contacts_last_interaction');
            $table->dropIndex('idx_contacts_created_active');
            $table->dropIndex('idx_contacts_updated_subscribed');
        });
    }
};
