<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\MetalRatesService;
use App\Models\MetalRate;
use App\Models\MetalRateAlert;
use App\Models\MetalRateSubscription;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class MetalRatesController extends Controller
{
    public function __construct(
        protected MetalRatesService $metalRatesService
    ) {}

    public function getCurrentRates(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'metals' => 'nullable|array',
            'metals.*' => 'in:gold,silver,platinum,palladium,copper,aluminum,zinc,nickel',
        ]);

        $rates = $this->metalRatesService->fetchCurrentRates($validated['metals'] ?? null);

        return response()->json([
            'success' => true,
            'data' => $rates,
        ]);
    }

    public function getHistoricalRates(Request $request, string $metal): JsonResponse
    {
        $validated = $request->validate([
            'days' => 'nullable|integer|min:1|max:365',
        ]);

        if (!in_array($metal, array_keys($this->metalRatesService->getSupportedMetals()))) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported metal type',
            ], 400);
        }

        $historical = $this->metalRatesService->getHistoricalRates($metal, $validated['days'] ?? 30);

        return response()->json([
            'success' => true,
            'data' => $historical,
        ]);
    }

    public function getSupportedMetals(): JsonResponse
    {
        $metals = $this->metalRatesService->getSupportedMetals();

        return response()->json([
            'success' => true,
            'data' => [
                'metals' => $metals,
                'providers' => $this->metalRatesService->getProviders(),
            ],
        ]);
    }

    public function createAlert(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'user_id' => 'nullable|exists:users,id',
            'metal' => 'required|in:gold,silver,platinum,palladium,copper,aluminum,zinc,nickel',
            'condition' => 'required|in:above,below,change_percent',
            'threshold' => 'required|numeric|min:0',
            'notification_channels' => 'nullable|array',
            'notification_channels.*' => 'in:email,whatsapp,sms',
        ]);

        try {
            $alert = $this->metalRatesService->createAlert($validated);

            return response()->json([
                'success' => true,
                'message' => 'Alert created successfully',
                'data' => $alert,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create alert: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function getAlerts(Request $request): JsonResponse
    {
        $query = MetalRateAlert::query();

        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->has('metal')) {
            $query->where('metal', $request->metal);
        }

        if ($request->has('active')) {
            $query->where('is_active', $request->boolean('active'));
        }

        $alerts = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $alerts,
        ]);
    }

    public function updateAlert(Request $request, MetalRateAlert $alert): JsonResponse
    {
        $validated = $request->validate([
            'condition' => 'sometimes|in:above,below,change_percent',
            'threshold' => 'sometimes|numeric|min:0',
            'notification_channels' => 'sometimes|array',
            'notification_channels.*' => 'in:email,whatsapp,sms',
            'is_active' => 'sometimes|boolean',
        ]);

        $alert->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Alert updated successfully',
            'data' => $alert->fresh(),
        ]);
    }

    public function deleteAlert(MetalRateAlert $alert): JsonResponse
    {
        $alert->delete();

        return response()->json([
            'success' => true,
            'message' => 'Alert deleted successfully',
        ]);
    }

    public function subscribe(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'email' => 'required|email',
            'phone' => 'nullable|string',
            'metals' => 'nullable|array',
            'metals.*' => 'in:gold,silver,platinum,palladium,copper,aluminum,zinc,nickel',
            'frequency' => 'nullable|in:hourly,daily,weekly',
            'notification_channels' => 'nullable|array',
            'notification_channels.*' => 'in:email,whatsapp,sms',
        ]);

        try {
            $subscription = $this->metalRatesService->subscribeToUpdates($validated);

            return response()->json([
                'success' => true,
                'message' => 'Subscription created successfully',
                'data' => $subscription,
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create subscription: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function getSubscriptions(Request $request): JsonResponse
    {
        $query = MetalRateSubscription::query();

        if ($request->has('email')) {
            $query->where('email', $request->email);
        }

        if ($request->has('active')) {
            $query->where('is_active', $request->boolean('active'));
        }

        if ($request->has('frequency')) {
            $query->where('frequency', $request->frequency);
        }

        $subscriptions = $query->orderBy('created_at', 'desc')->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $subscriptions,
        ]);
    }

    public function updateSubscription(Request $request, MetalRateSubscription $subscription): JsonResponse
    {
        $validated = $request->validate([
            'metals' => 'sometimes|array',
            'metals.*' => 'in:gold,silver,platinum,palladium,copper,aluminum,zinc,nickel',
            'frequency' => 'sometimes|in:hourly,daily,weekly',
            'notification_channels' => 'sometimes|array',
            'notification_channels.*' => 'in:email,whatsapp,sms',
            'is_active' => 'sometimes|boolean',
        ]);

        $subscription->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Subscription updated successfully',
            'data' => $subscription->fresh(),
        ]);
    }

    public function unsubscribe(MetalRateSubscription $subscription): JsonResponse
    {
        $subscription->update(['is_active' => false]);

        return response()->json([
            'success' => true,
            'message' => 'Unsubscribed successfully',
        ]);
    }

    public function generateReport(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'metals' => 'nullable|array',
            'metals.*' => 'in:gold,silver,platinum,palladium,copper,aluminum,zinc,nickel',
            'days' => 'nullable|integer|min:1|max:365',
            'format' => 'nullable|in:json,csv,excel',
        ]);

        $metals = $validated['metals'] ?? array_keys($this->metalRatesService->getSupportedMetals());
        $days = $validated['days'] ?? 7;

        $report = $this->metalRatesService->generateRateReport($metals, $days);

        return response()->json([
            'success' => true,
            'data' => $report,
            'format' => $validated['format'] ?? 'json',
        ]);
    }

    public function refreshRates(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'metals' => 'nullable|array',
            'metals.*' => 'in:gold,silver,platinum,palladium,copper,aluminum,zinc,nickel',
            'force' => 'nullable|boolean',
        ]);

        try {
            $rates = $this->metalRatesService->fetchCurrentRates($validated['metals'] ?? null);

            return response()->json([
                'success' => true,
                'message' => 'Rates refreshed successfully',
                'data' => $rates,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh rates: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function checkAlerts(): JsonResponse
    {
        try {
            $triggeredAlerts = $this->metalRatesService->checkAlerts();

            return response()->json([
                'success' => true,
                'message' => 'Alerts checked successfully',
                'data' => [
                    'triggered_alerts' => count($triggeredAlerts),
                    'alerts' => $triggeredAlerts,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check alerts: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function broadcastUpdates(): JsonResponse
    {
        try {
            $result = $this->metalRatesService->broadcastRateUpdates();

            return response()->json([
                'success' => true,
                'message' => 'Updates broadcasted successfully',
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to broadcast updates: ' . $e->getMessage(),
            ], 400);
        }
    }

    public function getMarketSummary(): JsonResponse
    {
        $currentRates = $this->metalRatesService->fetchCurrentRates();
        $metals = array_keys($this->metalRatesService->getSupportedMetals());

        $summary = [
            'market_status' => 'open', // This would be determined by market hours
            'last_updated' => now(),
            'metals_summary' => [],
            'market_trends' => [],
        ];

        foreach ($metals as $metal) {
            $historical = $this->metalRatesService->getHistoricalRates($metal, 1);
            $stats = $historical['statistics'] ?? [];

            $summary['metals_summary'][$metal] = [
                'current_rate' => $currentRates['rates'][$metal] ?? null,
                'change' => $stats['change'] ?? 0,
                'change_percent' => $stats['change_percent'] ?? 0,
                'formatted_rate' => isset($currentRates['rates'][$metal]) ?
                    $this->metalRatesService->formatRateForDisplay($metal, $currentRates['rates'][$metal]) : null,
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $summary,
        ]);
    }

    public function getWidget(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'metals' => 'nullable|array',
            'metals.*' => 'in:gold,silver,platinum,palladium,copper,aluminum,zinc,nickel',
            'theme' => 'nullable|in:light,dark',
            'size' => 'nullable|in:small,medium,large',
        ]);

        $metals = $validated['metals'] ?? ['gold', 'silver'];
        $currentRates = $this->metalRatesService->fetchCurrentRates($metals);

        $widget = [
            'theme' => $validated['theme'] ?? 'light',
            'size' => $validated['size'] ?? 'medium',
            'rates' => [],
            'last_updated' => $currentRates['timestamp'],
        ];

        foreach ($metals as $metal) {
            if (isset($currentRates['rates'][$metal])) {
                $widget['rates'][] = [
                    'metal' => ucfirst($metal),
                    'rate' => $currentRates['rates'][$metal],
                    'formatted' => $this->metalRatesService->formatRateForDisplay($metal, $currentRates['rates'][$metal]),
                    'symbol' => $this->metalRatesService->getSupportedMetals()[$metal]['symbol'],
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $widget,
        ]);
    }
}
