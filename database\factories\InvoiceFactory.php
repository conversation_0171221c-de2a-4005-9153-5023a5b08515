<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;
use App\Models\Project;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Invoice>
 */
class InvoiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $amount = $this->faker->numberBetween(10000, 500000);
        $taxAmount = $amount * 0.18; // 18% GST
        $totalAmount = $amount + $taxAmount;

        return [
            'customer_id' => User::factory()->create(['role' => 'customer'])->id,
            'project_id' => Project::factory()->create()->id,
            'number' => 'INV-' . date('Y') . '-' . $this->faker->unique()->numberBetween(1000, 9999),
            'description' => $this->faker->sentence(),
            'amount' => $amount,
            'tax_amount' => $taxAmount,
            'total_amount' => $totalAmount,
            'status' => $this->faker->randomElement(['draft', 'pending', 'paid', 'overdue', 'cancelled']),
            'due_date' => $this->faker->dateTimeBetween('now', '+30 days'),
            'paid_at' => $this->faker->optional(0.6)->dateTimeBetween('-30 days', 'now'),
            'payment_method' => $this->faker->optional()->randomElement(['bank_transfer', 'upi', 'cash', 'cheque', 'card']),
            'payment_reference' => $this->faker->optional()->regexify('[A-Z0-9]{10}'),
            'notes' => $this->faker->optional()->paragraph(),
            'metadata' => [
                'tax_rate' => 18,
                'currency' => 'INR',
                'payment_terms' => '30 days',
            ],
        ];
    }

    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
                'paid_at' => null,
                'payment_method' => null,
                'payment_reference' => null,
            ];
        });
    }

    public function paid()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'paid',
                'paid_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
                'payment_method' => $this->faker->randomElement(['bank_transfer', 'upi', 'cash', 'cheque']),
                'payment_reference' => $this->faker->regexify('[A-Z0-9]{10}'),
            ];
        });
    }

    public function overdue()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'pending',
                'due_date' => $this->faker->dateTimeBetween('-30 days', '-1 day'),
                'paid_at' => null,
                'payment_method' => null,
                'payment_reference' => null,
            ];
        });
    }
}
