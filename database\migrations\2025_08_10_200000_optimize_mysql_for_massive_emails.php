<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations to optimize MySQL for massive email datasets.
     */
    public function up(): void
    {
        // Only run MySQL-specific optimizations if using MySQL
        if (DB::connection()->getDriverName() !== 'mysql') {
            return;
        }

        // Optimize contacts table for massive datasets
        DB::statement('ALTER TABLE contacts ENGINE=InnoDB');
        DB::statement('ALTER TABLE contacts ROW_FORMAT=DYNAMIC');
        
        // Add MySQL-specific indexes for contacts table
        // Add indexes only if they don't exist
        $this->addIndexIfNotExists('contacts', ['email', 'is_subscribed', 'is_active'], 'idx_email_subscription_active');
        $this->addIndexIfNotExists('contacts', ['country', 'state', 'city', 'is_subscribed'], 'idx_geo_subscribed');
        $this->addIndexIfNotExists('contacts', ['engagement_score', 'is_subscribed', 'is_active'], 'idx_engagement_active');
        $this->addIndexIfNotExists('contacts', ['industry', 'country', 'is_subscribed'], 'idx_industry_country_sub');
        $this->addIndexIfNotExists('contacts', ['source', 'created_at', 'is_active'], 'idx_source_date_active');
        $this->addIndexIfNotExists('contacts', ['last_interaction_at', 'engagement_score'], 'idx_interaction_engagement');

        // Add full-text indexes if they don't exist
        $this->addFullTextIndexIfNotExists('contacts', ['first_name', 'last_name'], 'ft_contact_names');
        $this->addFullTextIndexIfNotExists('contacts', ['company'], 'ft_company');

        // Optimize contact_lists table
        DB::statement('ALTER TABLE contact_lists ENGINE=InnoDB');
        DB::statement('ALTER TABLE contact_lists ROW_FORMAT=DYNAMIC');

        $this->addIndexIfNotExists('contact_lists', ['type', 'status', 'created_at'], 'idx_type_status_date');
        $this->addIndexIfNotExists('contact_lists', ['created_by', 'status'], 'idx_creator_status');
        $this->addFullTextIndexIfNotExists('contact_lists', ['name', 'description'], 'ft_list_search');

        // Optimize contact_list_members table for massive joins
        DB::statement('ALTER TABLE contact_list_members ENGINE=InnoDB');
        DB::statement('ALTER TABLE contact_list_members ROW_FORMAT=DYNAMIC');

        $this->addIndexIfNotExists('contact_list_members', ['contact_list_id', 'status', 'subscribed_at'], 'idx_list_status_date');
        $this->addIndexIfNotExists('contact_list_members', ['contact_id', 'status'], 'idx_contact_status');
        $this->addIndexIfNotExists('contact_list_members', ['subscription_source', 'status'], 'idx_source_status');
        $this->addIndexIfNotExists('contact_list_members', ['subscribed_at', 'status'], 'idx_date_status');

        // Optimize email_accounts table
        DB::statement('ALTER TABLE email_accounts ENGINE=InnoDB');

        $this->addIndexIfNotExists('email_accounts', ['is_active', 'daily_send_limit', 'emails_sent_today'], 'idx_active_limits');
        $this->addIndexIfNotExists('email_accounts', ['provider', 'is_active'], 'idx_provider_active');
        $this->addIndexIfNotExists('email_accounts', ['is_primary', 'is_active'], 'idx_primary_active');

        // Optimize email_campaigns table
        DB::statement('ALTER TABLE email_campaigns ENGINE=InnoDB');

        $this->addIndexIfNotExists('email_campaigns', ['status', 'scheduled_at', 'type'], 'idx_status_schedule_type');
        $this->addIndexIfNotExists('email_campaigns', ['created_by', 'status', 'created_at'], 'idx_creator_status_date');
        $this->addIndexIfNotExists('email_campaigns', ['type', 'status'], 'idx_type_status');
        $this->addFullTextIndexIfNotExists('email_campaigns', ['subject', 'content'], 'ft_campaign_content');

        // Optimize email_deliverability_logs table for massive volumes
        DB::statement('ALTER TABLE email_deliverability_logs ENGINE=InnoDB');
        DB::statement('ALTER TABLE email_deliverability_logs ROW_FORMAT=DYNAMIC');

        $this->addIndexIfNotExists('email_deliverability_logs', ['email_account_id', 'status', 'created_at'], 'idx_account_status_date');
        $this->addIndexIfNotExists('email_deliverability_logs', ['status', 'bounce_type', 'created_at'], 'idx_status_bounce_date');
        $this->addIndexIfNotExists('email_deliverability_logs', ['email_address', 'status'], 'idx_email_status');
        $this->addIndexIfNotExists('email_deliverability_logs', ['spam_score', 'status'], 'idx_spam_status');
        $this->addIndexIfNotExists('email_deliverability_logs', ['delivery_time', 'status'], 'idx_delivery_status');

        // Create partitioned table for email logs (for massive datasets)
        // Note: Partitioning with TIMESTAMP requires specific configuration
        // $this->createPartitionedEmailLogs(); // Disabled for now

        // Optimize other tables
        $this->optimizeOtherTables();

        // Create MySQL-specific stored procedures for common operations
        // $this->createStoredProcedures(); // Disabled for initial setup

        // Create views for common queries
        $this->createOptimizedViews();
    }

    /**
     * Create partitioned table for email logs to handle massive volumes.
     */
    private function createPartitionedEmailLogs(): void
    {
        // Create partitioned email logs table for better performance with massive data
        DB::statement("
            CREATE TABLE IF NOT EXISTS email_logs_partitioned (
                id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
                email_account_id BIGINT UNSIGNED NOT NULL,
                contact_id BIGINT UNSIGNED NULL,
                email_campaign_id BIGINT UNSIGNED NULL,
                email_address VARCHAR(255) NOT NULL,
                status ENUM('sent', 'delivered', 'bounced', 'failed', 'opened', 'clicked') NOT NULL,
                sent_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                delivered_at TIMESTAMP NULL,
                opened_at TIMESTAMP NULL,
                clicked_at TIMESTAMP NULL,
                bounce_reason TEXT NULL,
                metadata JSON NULL,
                created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (id, sent_at),
                INDEX idx_account_status (email_account_id, status),
                INDEX idx_email_status (email_address, status),
                INDEX idx_campaign_status (email_campaign_id, status)
            ) ENGINE=InnoDB
            PARTITION BY RANGE (YEAR(sent_at)) (
                PARTITION p2024 VALUES LESS THAN (2025),
                PARTITION p2025 VALUES LESS THAN (2026),
                PARTITION p2026 VALUES LESS THAN (2027),
                PARTITION p_future VALUES LESS THAN MAXVALUE
            )
        ");
    }

    /**
     * Optimize other tables for MySQL.
     */
    private function optimizeOtherTables(): void
    {
        $tables = [
            'email_templates',
            'whatsapp_numbers',
            'whatsapp_campaigns',
            'leads',
            'users',
            'sender_reputation_logs',
            'email_warming_schedules',
            'blacklist_monitoring'
        ];

        foreach ($tables as $table) {
            if (Schema::hasTable($table)) {
                DB::statement("ALTER TABLE {$table} ENGINE=InnoDB");
                DB::statement("ALTER TABLE {$table} ROW_FORMAT=DYNAMIC");
            }
        }
    }

    /**
     * Create stored procedures for common operations.
     */
    private function createStoredProcedures(): void
    {
        // Stored procedure to get contact statistics by geography
        DB::statement("
            DROP PROCEDURE IF EXISTS GetContactStatsByGeography;
        ");
        
        DB::statement("
            CREATE PROCEDURE GetContactStatsByGeography(
                IN p_country VARCHAR(100),
                IN p_state VARCHAR(100)
            )
            BEGIN
                SELECT 
                    country,
                    state,
                    city,
                    COUNT(*) as total_contacts,
                    SUM(CASE WHEN is_subscribed = 1 THEN 1 ELSE 0 END) as subscribed_contacts,
                    AVG(engagement_score) as avg_engagement,
                    COUNT(DISTINCT industry) as industries_count
                FROM contacts 
                WHERE (p_country IS NULL OR country = p_country)
                  AND (p_state IS NULL OR state = p_state)
                  AND is_active = 1
                GROUP BY country, state, city
                ORDER BY total_contacts DESC;
            END
        ");

        // Stored procedure to get email account performance
        DB::statement("
            DROP PROCEDURE IF EXISTS GetEmailAccountPerformance;
        ");
        
        DB::statement("
            CREATE PROCEDURE GetEmailAccountPerformance(
                IN p_days INT DEFAULT 30
            )
            BEGIN
                SELECT 
                    ea.name,
                    ea.email,
                    ea.daily_send_limit,
                    ea.emails_sent_today,
                    COUNT(edl.id) as total_emails_sent,
                    SUM(CASE WHEN edl.status = 'delivered' THEN 1 ELSE 0 END) as delivered_count,
                    SUM(CASE WHEN edl.status = 'bounced' THEN 1 ELSE 0 END) as bounced_count,
                    SUM(CASE WHEN edl.opened_at IS NOT NULL THEN 1 ELSE 0 END) as opened_count,
                    ROUND((SUM(CASE WHEN edl.status = 'delivered' THEN 1 ELSE 0 END) / COUNT(edl.id)) * 100, 2) as delivery_rate,
                    ROUND((SUM(CASE WHEN edl.opened_at IS NOT NULL THEN 1 ELSE 0 END) / COUNT(edl.id)) * 100, 2) as open_rate
                FROM email_accounts ea
                LEFT JOIN email_deliverability_logs edl ON ea.id = edl.email_account_id 
                    AND edl.created_at >= DATE_SUB(NOW(), INTERVAL p_days DAY)
                WHERE ea.is_active = 1
                GROUP BY ea.id
                ORDER BY delivery_rate DESC;
            END
        ");
    }

    /**
     * Create optimized views for common queries.
     */
    private function createOptimizedViews(): void
    {
        // View for active subscribed contacts with geographic info
        DB::statement("
            CREATE OR REPLACE VIEW active_subscribed_contacts AS
            SELECT 
                id,
                email,
                CONCAT(first_name, ' ', last_name) as full_name,
                company,
                industry,
                city,
                state,
                country,
                engagement_score,
                created_at
            FROM contacts 
            WHERE is_active = 1 
              AND is_subscribed = 1
              AND email IS NOT NULL
              AND email != ''
        ");

        // View for email campaign performance summary
        DB::statement("
            CREATE OR REPLACE VIEW campaign_performance_summary AS
            SELECT 
                ec.id,
                ec.name,
                ec.subject,
                ec.status,
                ec.scheduled_at,
                COUNT(edl.id) as total_sent,
                SUM(CASE WHEN edl.status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN edl.status = 'bounced' THEN 1 ELSE 0 END) as bounced,
                SUM(CASE WHEN edl.opened_at IS NOT NULL THEN 1 ELSE 0 END) as opened,
                SUM(CASE WHEN edl.clicked_at IS NOT NULL THEN 1 ELSE 0 END) as clicked,
                ROUND((SUM(CASE WHEN edl.status = 'delivered' THEN 1 ELSE 0 END) / COUNT(edl.id)) * 100, 2) as delivery_rate,
                ROUND((SUM(CASE WHEN edl.opened_at IS NOT NULL THEN 1 ELSE 0 END) / COUNT(edl.id)) * 100, 2) as open_rate,
                ROUND((SUM(CASE WHEN edl.clicked_at IS NOT NULL THEN 1 ELSE 0 END) / COUNT(edl.id)) * 100, 2) as click_rate
            FROM email_campaigns ec
            LEFT JOIN email_deliverability_logs edl ON ec.id = edl.email_campaign_id
            GROUP BY ec.id
        ");

        // View for geographic distribution summary
        DB::statement("
            CREATE OR REPLACE VIEW geographic_distribution AS
            SELECT 
                country,
                state,
                COUNT(*) as total_contacts,
                SUM(CASE WHEN is_subscribed = 1 THEN 1 ELSE 0 END) as subscribed_contacts,
                AVG(engagement_score) as avg_engagement_score,
                COUNT(DISTINCT industry) as unique_industries,
                COUNT(DISTINCT city) as unique_cities
            FROM contacts 
            WHERE is_active = 1
            GROUP BY country, state
            ORDER BY total_contacts DESC
        ");
    }

    /**
     * Helper method to add index only if it doesn't exist.
     */
    private function addIndexIfNotExists(string $table, array $columns, string $indexName): void
    {
        try {
            $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$indexName]);
            if (empty($indexes)) {
                Schema::table($table, function (Blueprint $tableSchema) use ($columns, $indexName) {
                    $tableSchema->index($columns, $indexName);
                });
            }
        } catch (\Exception $e) {
            // Index might already exist or there might be an error, ignore
        }
    }

    /**
     * Helper method to add full-text index only if it doesn't exist.
     */
    private function addFullTextIndexIfNotExists(string $table, array $columns, string $indexName): void
    {
        try {
            $indexes = DB::select("SHOW INDEX FROM {$table} WHERE Key_name = ?", [$indexName]);
            if (empty($indexes)) {
                Schema::table($table, function (Blueprint $tableSchema) use ($columns, $indexName) {
                    $tableSchema->fullText($columns, $indexName);
                });
            }
        } catch (\Exception $e) {
            // Index might already exist or there might be an error, ignore
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop views
        DB::statement("DROP VIEW IF EXISTS geographic_distribution");
        DB::statement("DROP VIEW IF EXISTS campaign_performance_summary");
        DB::statement("DROP VIEW IF EXISTS active_subscribed_contacts");

        // Drop stored procedures
        DB::statement("DROP PROCEDURE IF EXISTS GetEmailAccountPerformance");
        DB::statement("DROP PROCEDURE IF EXISTS GetContactStatsByGeography");

        // Drop partitioned table
        DB::statement("DROP TABLE IF EXISTS email_logs_partitioned");

        // Note: We don't remove indexes in down() as they might be needed
        // and removing them could impact performance
    }
};
