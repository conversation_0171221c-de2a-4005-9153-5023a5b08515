<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Page Header -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        Advanced Analytics Dashboard
                    </h1>
                    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                        Comprehensive analytics for your 1 crore email management system
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    <x-filament::badge color="success">
                        Real-time Data
                    </x-filament::badge>
                </div>
            </div>
        </div>

        <!-- Analytics Widgets -->
        <div class="grid grid-cols-1 gap-6">
            {{ $this->getHeaderWidgets() }}
        </div>

        <!-- Quick Analytics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-envelope class="h-8 w-8 text-blue-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Emails Sent</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">
                            {{ number_format(\App\Models\EmailCampaign::sum('emails_sent')) }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-users class="h-8 w-8 text-green-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Contacts</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">
                            {{ number_format(\App\Models\Contact::count()) }}
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-chart-bar class="h-8 w-8 text-purple-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Open Rate</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">
                            {{ number_format(\App\Models\EmailCampaign::where('status', 'sent')->avg('open_rate') ?? 0, 1) }}%
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <x-heroicon-o-cursor-arrow-ripple class="h-8 w-8 text-red-500" />
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Click Rate</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">
                            {{ number_format(\App\Models\EmailCampaign::where('status', 'sent')->avg('click_rate') ?? 0, 1) }}%
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Campaign Performance
                </h3>
                <div class="space-y-4">
                    @php
                        $recentCampaigns = \App\Models\EmailCampaign::where('status', 'sent')
                            ->orderBy('sent_at', 'desc')
                            ->limit(5)
                            ->get();
                    @endphp
                    @forelse($recentCampaigns as $campaign)
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ Str::limit($campaign->name, 30) }}
                                </p>
                                <p class="text-xs text-gray-500">
                                    {{ $campaign->sent_at?->format('M d, Y') }}
                                </p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">
                                    {{ number_format($campaign->open_rate, 1) }}% open
                                </p>
                                <p class="text-xs text-gray-500">
                                    {{ number_format($campaign->emails_sent) }} sent
                                </p>
                            </div>
                        </div>
                    @empty
                        <p class="text-gray-500 text-center py-4">No campaigns sent yet</p>
                    @endforelse
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    Contact Growth
                </h3>
                <div class="space-y-4">
                    @php
                        $contactGrowth = [
                            'Today' => \App\Models\Contact::whereDate('created_at', today())->count(),
                            'This Week' => \App\Models\Contact::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
                            'This Month' => \App\Models\Contact::whereMonth('created_at', now()->month)->count(),
                            'Total' => \App\Models\Contact::count(),
                        ];
                    @endphp
                    @foreach($contactGrowth as $period => $count)
                        <div class="flex items-center justify-between">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">{{ $period }}</p>
                            <p class="text-lg font-bold text-gray-900 dark:text-white">
                                {{ number_format($count) }}
                            </p>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- System Health -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                System Health & Performance
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Email Accounts</p>
                    <p class="text-2xl font-bold text-green-600">
                        {{ \App\Models\EmailAccount::where('is_active', true)->count() }}
                    </p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Active Campaigns</p>
                    <p class="text-2xl font-bold text-blue-600">
                        {{ \App\Models\EmailCampaign::whereIn('status', ['sending', 'scheduled'])->count() }}
                    </p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">System Uptime</p>
                    <p class="text-2xl font-bold text-purple-600">99.9%</p>
                </div>
            </div>
        </div>
    </div>
</x-filament-panels::page>
