<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SocialMediaAccountResource\Pages;
use App\Filament\Resources\SocialMediaAccountResource\RelationManagers;
use App\Models\SocialMediaAccount;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SocialMediaAccountResource extends Resource
{
    protected static ?string $model = SocialMediaAccount::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationGroup = 'Social Media';

    protected static ?string $navigationLabel = 'Accounts';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('platform')
                    ->required(),
                Forms\Components\TextInput::make('account_id')
                    ->maxLength(255),
                Forms\Components\TextInput::make('username')
                    ->maxLength(255),
                Forms\Components\DateTimePicker::make('token_expires_at'),
                Forms\Components\TextInput::make('account_info'),
                Forms\Components\TextInput::make('permissions'),
                Forms\Components\Toggle::make('is_active')
                    ->required(),
                Forms\Components\Toggle::make('auto_post')
                    ->required(),
                Forms\Components\TextInput::make('posting_schedule'),
                Forms\Components\TextInput::make('daily_post_limit')
                    ->required()
                    ->numeric()
                    ->default(10),
                Forms\Components\DateTimePicker::make('last_posted_at'),
                Forms\Components\TextInput::make('created_by')
                    ->required()
                    ->numeric(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('platform'),
                Tables\Columns\TextColumn::make('account_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('username')
                    ->searchable(),
                Tables\Columns\TextColumn::make('token_expires_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\IconColumn::make('auto_post')
                    ->boolean(),
                Tables\Columns\TextColumn::make('daily_post_limit')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_posted_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_by')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSocialMediaAccounts::route('/'),
            'create' => Pages\CreateSocialMediaAccount::route('/create'),
            'edit' => Pages\EditSocialMediaAccount::route('/{record}/edit'),
        ];
    }
}
