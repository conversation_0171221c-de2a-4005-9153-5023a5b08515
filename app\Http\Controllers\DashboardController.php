<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Lead;
use App\Models\EmailCampaign;
use App\Models\WhatsAppCampaign;
use App\Models\SocialMediaPost;
use App\Models\Project;
use App\Models\User;

class DashboardController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        $user = Auth::user();
        
        // Redirect based on user role
        switch ($user->role) {
            case 'admin':
                return redirect('/admin');
                
            case 'customer':
                return redirect()->route('customer.dashboard');
                
            default:
                return $this->generalDashboard();
        }
    }

    protected function generalDashboard()
    {
        $user = Auth::user();
        
        // Basic statistics for general users
        $stats = [
            'total_leads' => Lead::count(),
            'my_leads' => Lead::where('assigned_to', $user->id)->count(),
            'new_leads_today' => Lead::whereDate('created_at', today())->count(),
            'active_campaigns' => EmailCampaign::whereIn('status', ['sending', 'scheduled'])->count(),
            'recent_activities' => $this->getRecentActivities($user),
        ];
        
        // Get user's assigned leads
        $myLeads = Lead::where('assigned_to', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
        
        // Get recent campaigns
        $recentCampaigns = EmailCampaign::orderBy('created_at', 'desc')
            ->limit(5)
            ->get();
        
        return view('dashboard.general', compact('stats', 'myLeads', 'recentCampaigns'));
    }

    protected function getRecentActivities($user)
    {
        $activities = collect();
        
        // Recent leads assigned to user
        $recentLeads = Lead::where('assigned_to', $user->id)
            ->whereDate('created_at', '>=', now()->subDays(7))
            ->get()
            ->map(function ($lead) {
                return [
                    'type' => 'lead_assigned',
                    'title' => "New lead assigned: {$lead->name}",
                    'description' => "Lead from {$lead->source} - {$lead->service_interest}",
                    'timestamp' => $lead->created_at,
                    'url' => "/admin/leads/{$lead->id}",
                    'icon' => 'user-plus',
                    'color' => 'blue',
                ];
            });
        
        $activities = $activities->merge($recentLeads);
        
        // Recent campaign activities
        $recentCampaigns = EmailCampaign::whereDate('created_at', '>=', now()->subDays(7))
            ->get()
            ->map(function ($campaign) {
                return [
                    'type' => 'campaign_created',
                    'title' => "Email campaign created: {$campaign->name}",
                    'description' => "Status: {$campaign->status}",
                    'timestamp' => $campaign->created_at,
                    'url' => "/admin/email-campaigns/{$campaign->id}",
                    'icon' => 'mail',
                    'color' => 'green',
                ];
            });
        
        $activities = $activities->merge($recentCampaigns);
        
        // Sort by timestamp and limit
        return $activities->sortByDesc('timestamp')->take(10)->values();
    }
}
