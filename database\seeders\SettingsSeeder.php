<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Setting;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            // General Application Settings
            [
                'key' => 'app.name',
                'value' => '1 Crore Email Management System',
                'category' => 'general',
                'type' => 'string',
                'description' => 'Application name displayed throughout the system',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'app.description',
                'value' => 'Professional email management system for handling 1 crore (10 million) emails with geographic filtering',
                'category' => 'general',
                'type' => 'string',
                'description' => 'Application description for meta tags and documentation',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'app.timezone',
                'value' => 'Asia/Kolkata',
                'category' => 'general',
                'type' => 'string',
                'description' => 'Default application timezone',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'app.locale',
                'value' => 'en',
                'category' => 'general',
                'type' => 'string',
                'description' => 'Default application language',
                'is_public' => true,
                'sort_order' => 4,
            ],

            // Email Configuration Settings
            [
                'key' => 'email.default_provider',
                'value' => 'smtp',
                'category' => 'email',
                'type' => 'string',
                'description' => 'Default email provider (smtp, ses, mailgun, etc.)',
                'options' => [
                    'smtp' => 'SMTP',
                    'ses' => 'Amazon SES',
                    'mailgun' => 'Mailgun',
                    'postmark' => 'Postmark',
                    'sendgrid' => 'SendGrid',
                ],
                'sort_order' => 1,
            ],
            [
                'key' => 'email.daily_send_limit',
                'value' => '500',
                'category' => 'email',
                'type' => 'integer',
                'description' => 'Maximum emails to send per day per account',
                'validation_rules' => ['min:1', 'max:10000'],
                'sort_order' => 2,
            ],
            [
                'key' => 'email.batch_size',
                'value' => '100',
                'category' => 'email',
                'type' => 'integer',
                'description' => 'Number of emails to process in each batch',
                'validation_rules' => ['min:1', 'max:1000'],
                'sort_order' => 3,
            ],
            [
                'key' => 'email.retry_attempts',
                'value' => '3',
                'category' => 'email',
                'type' => 'integer',
                'description' => 'Number of retry attempts for failed emails',
                'validation_rules' => ['min:1', 'max:10'],
                'sort_order' => 4,
            ],
            [
                'key' => 'email.bounce_threshold',
                'value' => '5',
                'category' => 'email',
                'type' => 'integer',
                'description' => 'Bounce rate percentage threshold for account suspension',
                'validation_rules' => ['min:1', 'max:50'],
                'sort_order' => 5,
            ],

            // SMTP Settings
            [
                'key' => 'smtp.host',
                'value' => 'smtp.gmail.com',
                'category' => 'smtp',
                'group' => 'connection',
                'type' => 'string',
                'description' => 'SMTP server hostname',
                'sort_order' => 1,
            ],
            [
                'key' => 'smtp.port',
                'value' => '587',
                'category' => 'smtp',
                'group' => 'connection',
                'type' => 'integer',
                'description' => 'SMTP server port',
                'validation_rules' => ['min:1', 'max:65535'],
                'sort_order' => 2,
            ],
            [
                'key' => 'smtp.encryption',
                'value' => 'tls',
                'category' => 'smtp',
                'group' => 'connection',
                'type' => 'string',
                'description' => 'SMTP encryption method',
                'options' => [
                    'tls' => 'TLS',
                    'ssl' => 'SSL',
                    'none' => 'None',
                ],
                'sort_order' => 3,
            ],
            [
                'key' => 'smtp.username',
                'value' => '',
                'category' => 'smtp',
                'group' => 'authentication',
                'type' => 'email',
                'description' => 'SMTP username (usually email address)',
                'sort_order' => 4,
            ],
            [
                'key' => 'smtp.password',
                'value' => '',
                'category' => 'smtp',
                'group' => 'authentication',
                'type' => 'password',
                'description' => 'SMTP password or app password',
                'is_encrypted' => true,
                'sort_order' => 5,
            ],

            // Performance Settings
            [
                'key' => 'performance.import_chunk_size',
                'value' => '1000',
                'category' => 'performance',
                'group' => 'import',
                'type' => 'integer',
                'description' => 'Number of contacts to import in each chunk',
                'validation_rules' => ['min:100', 'max:10000'],
                'sort_order' => 1,
            ],
            [
                'key' => 'performance.query_cache_ttl',
                'value' => '3600',
                'category' => 'performance',
                'group' => 'caching',
                'type' => 'integer',
                'description' => 'Query cache time-to-live in seconds',
                'validation_rules' => ['min:60', 'max:86400'],
                'sort_order' => 2,
            ],
            [
                'key' => 'performance.max_memory_usage',
                'value' => '512',
                'category' => 'performance',
                'group' => 'memory',
                'type' => 'integer',
                'description' => 'Maximum memory usage in MB for bulk operations',
                'validation_rules' => ['min:128', 'max:2048'],
                'sort_order' => 3,
            ],
            [
                'key' => 'performance.enable_query_optimization',
                'value' => 'true',
                'category' => 'performance',
                'group' => 'optimization',
                'type' => 'boolean',
                'description' => 'Enable automatic query optimization for large datasets',
                'sort_order' => 4,
            ],

            // Security Settings
            [
                'key' => 'security.enable_2fa',
                'value' => 'false',
                'category' => 'security',
                'group' => 'authentication',
                'type' => 'boolean',
                'description' => 'Enable two-factor authentication for admin users',
                'sort_order' => 1,
            ],
            [
                'key' => 'security.session_timeout',
                'value' => '7200',
                'category' => 'security',
                'group' => 'session',
                'type' => 'integer',
                'description' => 'Session timeout in seconds',
                'validation_rules' => ['min:300', 'max:86400'],
                'sort_order' => 2,
            ],
            [
                'key' => 'security.api_rate_limit',
                'value' => '1000',
                'category' => 'security',
                'group' => 'api',
                'type' => 'integer',
                'description' => 'API requests per hour limit',
                'validation_rules' => ['min:100', 'max:10000'],
                'sort_order' => 3,
            ],
            [
                'key' => 'security.encrypt_sensitive_data',
                'value' => 'true',
                'category' => 'security',
                'group' => 'encryption',
                'type' => 'boolean',
                'description' => 'Encrypt sensitive data in database',
                'sort_order' => 4,
            ],

            // Geographic Settings
            [
                'key' => 'geographic.default_country',
                'value' => 'India',
                'category' => 'geographic',
                'group' => 'defaults',
                'type' => 'string',
                'description' => 'Default country for new contacts',
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'geographic.enable_state_filtering',
                'value' => 'true',
                'category' => 'geographic',
                'group' => 'filtering',
                'type' => 'boolean',
                'description' => 'Enable state-wise contact filtering',
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'geographic.enable_city_filtering',
                'value' => 'true',
                'category' => 'geographic',
                'group' => 'filtering',
                'type' => 'boolean',
                'description' => 'Enable city-wise contact filtering',
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'geographic.supported_states',
                'value' => json_encode([
                    'Tamil Nadu', 'Karnataka', 'Kerala', 'Andhra Pradesh', 'Telangana',
                    'Maharashtra', 'Gujarat', 'Rajasthan', 'Uttar Pradesh', 'Bihar',
                    'West Bengal', 'Odisha', 'Madhya Pradesh', 'Chhattisgarh', 'Jharkhand'
                ]),
                'category' => 'geographic',
                'group' => 'data',
                'type' => 'array',
                'description' => 'List of supported states for filtering',
                'is_public' => true,
                'sort_order' => 4,
            ],

            // API Configuration
            [
                'key' => 'api.enable_public_api',
                'value' => 'true',
                'category' => 'api',
                'group' => 'access',
                'type' => 'boolean',
                'description' => 'Enable public API access',
                'sort_order' => 1,
            ],
            [
                'key' => 'api.require_authentication',
                'value' => 'true',
                'category' => 'api',
                'group' => 'security',
                'type' => 'boolean',
                'description' => 'Require API authentication',
                'sort_order' => 2,
            ],
            [
                'key' => 'api.default_response_format',
                'value' => 'json',
                'category' => 'api',
                'group' => 'format',
                'type' => 'string',
                'description' => 'Default API response format',
                'options' => [
                    'json' => 'JSON',
                    'xml' => 'XML',
                ],
                'sort_order' => 3,
            ],

            // WhatsApp Integration
            [
                'key' => 'whatsapp.enable_integration',
                'value' => 'true',
                'category' => 'whatsapp',
                'group' => 'general',
                'type' => 'boolean',
                'description' => 'Enable WhatsApp integration features',
                'sort_order' => 1,
            ],
            [
                'key' => 'whatsapp.api_endpoint',
                'value' => '',
                'category' => 'whatsapp',
                'group' => 'api',
                'type' => 'url',
                'description' => 'WhatsApp API endpoint URL',
                'sort_order' => 2,
            ],
            [
                'key' => 'whatsapp.api_token',
                'value' => '',
                'category' => 'whatsapp',
                'group' => 'api',
                'type' => 'password',
                'description' => 'WhatsApp API authentication token',
                'is_encrypted' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'whatsapp.daily_message_limit',
                'value' => '1000',
                'category' => 'whatsapp',
                'group' => 'limits',
                'type' => 'integer',
                'description' => 'Daily WhatsApp message sending limit',
                'validation_rules' => ['min:1', 'max:10000'],
                'sort_order' => 4,
            ],

            // Backup & Recovery
            [
                'key' => 'backup.enable_auto_backup',
                'value' => 'true',
                'category' => 'backup',
                'group' => 'automation',
                'type' => 'boolean',
                'description' => 'Enable automatic database backups',
                'sort_order' => 1,
            ],
            [
                'key' => 'backup.backup_frequency',
                'value' => 'daily',
                'category' => 'backup',
                'group' => 'schedule',
                'type' => 'string',
                'description' => 'Backup frequency',
                'options' => [
                    'hourly' => 'Hourly',
                    'daily' => 'Daily',
                    'weekly' => 'Weekly',
                    'monthly' => 'Monthly',
                ],
                'sort_order' => 2,
            ],
            [
                'key' => 'backup.retention_days',
                'value' => '30',
                'category' => 'backup',
                'group' => 'retention',
                'type' => 'integer',
                'description' => 'Number of days to retain backups',
                'validation_rules' => ['min:1', 'max:365'],
                'sort_order' => 3,
            ],

            // Monitoring
            [
                'key' => 'monitoring.enable_performance_monitoring',
                'value' => 'true',
                'category' => 'monitoring',
                'group' => 'performance',
                'type' => 'boolean',
                'description' => 'Enable performance monitoring and logging',
                'sort_order' => 1,
            ],
            [
                'key' => 'monitoring.log_slow_queries',
                'value' => 'true',
                'category' => 'monitoring',
                'group' => 'database',
                'type' => 'boolean',
                'description' => 'Log slow database queries for optimization',
                'sort_order' => 2,
            ],
            [
                'key' => 'monitoring.slow_query_threshold',
                'value' => '1000',
                'category' => 'monitoring',
                'group' => 'database',
                'type' => 'integer',
                'description' => 'Slow query threshold in milliseconds',
                'validation_rules' => ['min:100', 'max:10000'],
                'sort_order' => 3,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                array_merge($setting, [
                    'last_modified_at' => now(),
                    'modified_by' => 1, // Assuming admin user ID is 1
                ])
            );
        }
    }
}
