<nav x-data="navbar" class="bg-white shadow-lg sticky top-0 z-50 transition-all duration-300">
    <div class="container-custom">
        <div class="flex justify-between items-center py-4">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="{{ route('home') }}" class="flex items-center space-x-3">
                    <img src="{{ asset('images/logo.svg') }}" alt="Bhavitech Logo" class="h-10 w-auto">
                    <span class="text-2xl font-bold text-gradient">Bhavitech</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex items-center space-x-8">
                <a href="{{ route('home') }}" class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}">
                    Home
                </a>
                
                <!-- Services Dropdown -->
                <div class="relative group">
                    <button class="nav-link flex items-center space-x-1">
                        <span>Services</span>
                        <svg class="w-4 h-4 transition-transform group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div class="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                        <div class="py-4">
                            <a href="{{ route('services.web-development') }}" class="dropdown-link">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-medium">Web Development</div>
                                        <div class="text-sm text-gray-500">Custom websites & CMS</div>
                                    </div>
                                </div>
                            </a>
                            <a href="{{ route('services.mobile-development') }}" class="dropdown-link">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-medium">Mobile Apps</div>
                                        <div class="text-sm text-gray-500">iOS & Android development</div>
                                    </div>
                                </div>
                            </a>
                            <a href="{{ route('services.digital-marketing') }}" class="dropdown-link">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-medium">Digital Marketing</div>
                                        <div class="text-sm text-gray-500">SEO, SEM & Social Media</div>
                                    </div>
                                </div>
                            </a>
                            <a href="{{ route('services.graphic-design') }}" class="dropdown-link">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-medium">Graphic Design</div>
                                        <div class="text-sm text-gray-500">Branding & Visual Identity</div>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>

                <a href="{{ route('portfolio') }}" class="nav-link {{ request()->routeIs('portfolio') ? 'active' : '' }}">
                    Portfolio
                </a>
                <a href="{{ route('about') }}" class="nav-link {{ request()->routeIs('about') ? 'active' : '' }}">
                    About
                </a>
                <a href="{{ route('contact') }}" class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}">
                    Contact
                </a>
            </div>

            <!-- Auth Links -->
            <div class="hidden lg:flex items-center space-x-4">
                <a href="tel:+917010860889" class="text-primary-600 hover:text-primary-700 font-medium">
                    +91 7010860889
                </a>

                @auth
                    @if(Auth::user()->role === 'customer')
                        <a href="{{ route('customer.dashboard') }}" class="text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">
                            Dashboard
                        </a>
                    @elseif(Auth::user()->role === 'admin')
                        <a href="/admin" class="text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">
                            Admin Panel
                        </a>
                    @endif

                    <!-- User Menu -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" class="flex items-center text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">
                            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center mr-2">
                                <span class="text-sm font-medium text-primary-600">{{ substr(Auth::user()->name, 0, 1) }}</span>
                            </div>
                            {{ Auth::user()->name }}
                            <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>

                        <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200">
                            @if(Auth::user()->role === 'customer')
                                <a href="{{ route('customer.dashboard') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Dashboard</a>
                                <a href="{{ route('customer.profile') }}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                            @endif
                            <form method="POST" action="{{ route('logout') }}">
                                @csrf
                                <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    Sign out
                                </button>
                            </form>
                        </div>
                    </div>
                @else
                    <a href="{{ route('login') }}" class="text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">
                        Sign In
                    </a>
                    <a href="{{ route('register') }}" class="text-gray-700 hover:text-primary-600 px-3 py-2 text-sm font-medium">
                        Register
                    </a>
                    <a href="{{ route('quote') }}" class="btn-primary">
                        Get Quote
                    </a>
                @endauth
            </div>

            <!-- Mobile menu button -->
            <div class="lg:hidden">
                <button @click="toggle()" class="text-gray-600 hover:text-gray-900 focus:outline-none focus:text-gray-900">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path x-show="!open" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        <path x-show="open" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div x-show="open" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 transform scale-100" x-transition:leave-end="opacity-0 transform scale-95" class="lg:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
                <a href="{{ route('home') }}" class="mobile-nav-link {{ request()->routeIs('home') ? 'active' : '' }}" @click="close()">Home</a>
                <a href="{{ route('services.web-development') }}" class="mobile-nav-link" @click="close()">Web Development</a>
                <a href="{{ route('services.mobile-development') }}" class="mobile-nav-link" @click="close()">Mobile Apps</a>
                <a href="{{ route('services.digital-marketing') }}" class="mobile-nav-link" @click="close()">Digital Marketing</a>
                <a href="{{ route('services.graphic-design') }}" class="mobile-nav-link" @click="close()">Graphic Design</a>
                <a href="{{ route('portfolio') }}" class="mobile-nav-link {{ request()->routeIs('portfolio') ? 'active' : '' }}" @click="close()">Portfolio</a>
                <a href="{{ route('about') }}" class="mobile-nav-link {{ request()->routeIs('about') ? 'active' : '' }}" @click="close()">About</a>
                <a href="{{ route('contact') }}" class="mobile-nav-link {{ request()->routeIs('contact') ? 'active' : '' }}" @click="close()">Contact</a>
                <div class="pt-4 border-t border-gray-200">
                    <a href="tel:+917010860889" class="mobile-nav-link text-primary-600">+91 7010860889</a>
                    <a href="{{ route('quote') }}" class="block w-full text-center btn-primary mt-2" @click="close()">Get Quote</a>
                </div>
            </div>
        </div>
    </div>
</nav>

<style>
.nav-link {
    @apply text-gray-700 hover:text-primary-600 font-medium transition-colors duration-200 relative;
}

.nav-link.active {
    @apply text-primary-600;
}

.nav-link.active::after {
    content: '';
    @apply absolute bottom-0 left-0 w-full h-0.5 bg-primary-600;
}

.dropdown-link {
    @apply block px-4 py-3 text-gray-700 hover:bg-gray-50 transition-colors duration-200;
}

.mobile-nav-link {
    @apply block px-3 py-2 text-gray-700 hover:text-primary-600 hover:bg-gray-50 rounded-md font-medium transition-colors duration-200;
}

.mobile-nav-link.active {
    @apply text-primary-600 bg-primary-50;
}
</style>
