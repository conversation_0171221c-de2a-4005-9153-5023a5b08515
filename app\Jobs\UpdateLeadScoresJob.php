<?php

namespace App\Jobs;

use App\Models\Lead;
use App\Services\LeadScoringService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdateLeadScoresJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    protected ?int $leadId;
    protected bool $bulkUpdate;

    /**
     * Create a new job instance.
     */
    public function __construct(?int $leadId = null, bool $bulkUpdate = false)
    {
        $this->leadId = $leadId;
        $this->bulkUpdate = $bulkUpdate;
    }

    /**
     * Execute the job.
     */
    public function handle(LeadScoringService $scoringService): void
    {
        try {
            if ($this->bulkUpdate) {
                $this->handleBulkUpdate($scoringService);
            } elseif ($this->leadId) {
                $this->handleSingleUpdate($scoringService);
            }
        } catch (\Exception $e) {
            Log::error('Lead score update job failed', [
                'lead_id' => $this->leadId,
                'bulk_update' => $this->bulkUpdate,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    protected function handleSingleUpdate(LeadScoringService $scoringService): void
    {
        $lead = Lead::find($this->leadId);

        if (!$lead) {
            Log::warning('Lead not found for score update', ['lead_id' => $this->leadId]);
            return;
        }

        $scoringService->updateLeadScore($lead);

        Log::info('Lead score updated via job', [
            'lead_id' => $lead->id,
            'new_score' => $lead->fresh()->score,
        ]);
    }

    protected function handleBulkUpdate(LeadScoringService $scoringService): void
    {
        $updated = $scoringService->bulkUpdateScores();

        Log::info('Bulk lead score update completed', [
            'leads_updated' => $updated,
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Lead score update job failed permanently', [
            'lead_id' => $this->leadId,
            'bulk_update' => $this->bulkUpdate,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
