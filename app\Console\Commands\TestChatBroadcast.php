<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Events\MessageSent;

class TestChatBroadcast extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'chat:test-broadcast {session_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test chat broadcasting functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $sessionId = $this->argument('session_id');

        if (!$sessionId) {
            // Create a test session
            $session = ChatSession::create([
                'visitor_name' => 'Test User',
                'status' => 'active',
                'started_at' => now(),
                'last_activity_at' => now(),
            ]);
            $sessionId = $session->session_id;
            $this->info("Created test session: {$sessionId}");
        } else {
            $session = ChatSession::where('session_id', $sessionId)->first();
            if (!$session) {
                $this->error("Session not found: {$sessionId}");
                return 1;
            }
        }

        // Create a test message
        $message = ChatMessage::create([
            'chat_session_id' => $session->id,
            'sender_type' => 'agent',
            'sender_name' => 'Test Agent',
            'message_type' => 'text',
            'message' => 'This is a test message from the console!',
            'sent_at' => now(),
        ]);

        $this->info("Test message sent successfully!");
        $this->info("Session ID: {$session->session_id}");
        $this->info("Message ID: {$message->message_id}");
        $this->info("Broadcasting should be working if Pusher is configured correctly.");

        return 0;
    }
}
