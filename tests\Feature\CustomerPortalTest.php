<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Project;
use App\Models\Invoice;
use App\Models\SupportTicket;

class CustomerPortalTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $customer;

    protected function setUp(): void
    {
        parent::setUp();
        $this->customer = User::factory()->create(['role' => 'customer']);
    }

    public function test_customer_dashboard_loads()
    {
        $response = $this->actingAs($this->customer)->get('/customer/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('customer.dashboard');
        $response->assertViewHas(['projects', 'invoices', 'tickets', 'stats']);
    }

    public function test_customer_can_view_projects()
    {
        Project::factory()->count(3)->create(['customer_id' => $this->customer->id]);

        $response = $this->actingAs($this->customer)->get('/customer/projects');

        $response->assertStatus(200);
        $response->assertViewIs('customer.projects.index');
    }

    public function test_customer_can_view_specific_project()
    {
        $project = Project::factory()->create(['customer_id' => $this->customer->id]);

        $response = $this->actingAs($this->customer)->get("/customer/projects/{$project->id}");

        $response->assertStatus(200);
        $response->assertViewIs('customer.projects.show');
        $response->assertViewHas('project');
    }

    public function test_customer_cannot_view_other_customer_project()
    {
        $otherCustomer = User::factory()->create(['role' => 'customer']);
        $project = Project::factory()->create(['customer_id' => $otherCustomer->id]);

        $response = $this->actingAs($this->customer)->get("/customer/projects/{$project->id}");

        $response->assertStatus(403);
    }

    public function test_customer_can_view_invoices()
    {
        Invoice::factory()->count(2)->create(['customer_id' => $this->customer->id]);

        $response = $this->actingAs($this->customer)->get('/customer/invoices');

        $response->assertStatus(200);
        $response->assertViewIs('customer.invoices.index');
    }

    public function test_customer_can_view_specific_invoice()
    {
        $invoice = Invoice::factory()->create(['customer_id' => $this->customer->id]);

        $response = $this->actingAs($this->customer)->get("/customer/invoices/{$invoice->id}");

        $response->assertStatus(200);
        $response->assertViewIs('customer.invoices.show');
    }

    public function test_customer_can_view_support_tickets()
    {
        SupportTicket::factory()->count(2)->create(['customer_id' => $this->customer->id]);

        $response = $this->actingAs($this->customer)->get('/customer/support');

        $response->assertStatus(200);
        $response->assertViewIs('customer.support.index');
    }

    public function test_customer_can_create_support_ticket()
    {
        $ticketData = [
            'subject' => 'Test Support Ticket',
            'description' => 'This is a test support ticket description.',
            'priority' => 'medium',
            'category' => 'technical'
        ];

        $response = $this->actingAs($this->customer)->post('/customer/support', $ticketData);

        $response->assertRedirect('/customer/support');
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('support_tickets', [
            'customer_id' => $this->customer->id,
            'title' => 'Test Support Ticket'
        ]);
    }

    public function test_customer_can_view_profile()
    {
        $response = $this->actingAs($this->customer)->get('/customer/profile');

        $response->assertStatus(200);
        $response->assertViewIs('customer.profile');
        $response->assertViewHas('user');
    }

    public function test_customer_can_update_profile()
    {
        $updateData = [
            'name' => 'Updated Name',
            'email' => '<EMAIL>',
            'phone' => '+91 9876543210',
            'company' => 'Updated Company',
            'address' => '123 Updated Street',
            'city' => 'Updated City',
            'state' => 'Updated State',
            'postal_code' => '123456',
            'country' => 'India'
        ];

        $response = $this->actingAs($this->customer)->put('/customer/profile', $updateData);

        $response->assertRedirect('/customer/profile');
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('users', [
            'id' => $this->customer->id,
            'name' => 'Updated Name',
            'email' => '<EMAIL>'
        ]);
    }

    public function test_support_ticket_validation()
    {
        $response = $this->actingAs($this->customer)->post('/customer/support', []);

        $response->assertSessionHasErrors(['subject', 'description', 'priority', 'category']);
    }

    public function test_profile_email_uniqueness_validation()
    {
        $otherUser = User::factory()->create(['email' => '<EMAIL>']);

        $response = $this->actingAs($this->customer)->put('/customer/profile', [
            'name' => $this->customer->name,
            'email' => '<EMAIL>'
        ]);

        $response->assertSessionHasErrors(['email']);
    }
}
