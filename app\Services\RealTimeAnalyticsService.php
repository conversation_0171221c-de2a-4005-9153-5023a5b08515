<?php

namespace App\Services;

use App\Models\AnalyticsEvent;
use App\Models\Lead;
use App\Models\LeadInteraction;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Carbon\Carbon;

class RealTimeAnalyticsService
{
    protected string $cachePrefix = 'realtime_analytics:';
    protected int $cacheTtl = 300; // 5 minutes

    public function getRealTimeDashboard(): array
    {
        return [
            'live_visitors' => $this->getLiveVisitors(),
            'active_sessions' => $this->getActiveSessions(),
            'recent_leads' => $this->getRecentLeads(),
            'live_events' => $this->getLiveEvents(),
            'current_campaigns' => $this->getCurrentCampaigns(),
            'real_time_metrics' => $this->getRealTimeMetrics(),
            'geographic_activity' => $this->getGeographicActivity(),
            'device_breakdown' => $this->getDeviceBreakdown(),
        ];
    }

    public function getLiveVisitors(): array
    {
        $cacheKey = $this->cachePrefix . 'live_visitors';
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () {
            $activeThreshold = now()->subMinutes(5);
            
            $activeVisitors = AnalyticsEvent::where('created_at', '>=', $activeThreshold)
                ->distinct('visitor_id')
                ->count();

            $newVisitors = AnalyticsEvent::where('created_at', '>=', $activeThreshold)
                ->whereNotExists(function ($query) use ($activeThreshold) {
                    $query->select('id')
                        ->from('analytics_events as ae2')
                        ->whereColumn('ae2.visitor_id', 'analytics_events.visitor_id')
                        ->where('ae2.created_at', '<', $activeThreshold);
                })
                ->distinct('visitor_id')
                ->count();

            $returningVisitors = $activeVisitors - $newVisitors;

            return [
                'total_active' => $activeVisitors,
                'new_visitors' => $newVisitors,
                'returning_visitors' => $returningVisitors,
                'visitor_trend' => $this->getVisitorTrend(),
            ];
        });
    }

    public function getActiveSessions(): array
    {
        $cacheKey = $this->cachePrefix . 'active_sessions';
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () {
            $activeThreshold = now()->subMinutes(30);
            
            $sessions = AnalyticsEvent::where('created_at', '>=', $activeThreshold)
                ->select('session_id', 'visitor_id', 'page_url', 'utm_source', 'device_type')
                ->distinct('session_id')
                ->orderBy('created_at', 'desc')
                ->limit(50)
                ->get();

            $sessionDetails = $sessions->map(function ($session) {
                $sessionEvents = AnalyticsEvent::where('session_id', $session->session_id)
                    ->orderBy('created_at', 'desc')
                    ->get();

                return [
                    'session_id' => $session->session_id,
                    'visitor_id' => $session->visitor_id,
                    'current_page' => $sessionEvents->first()->page_url ?? 'Unknown',
                    'entry_page' => $sessionEvents->last()->page_url ?? 'Unknown',
                    'page_views' => $sessionEvents->where('event_name', 'page_view')->count(),
                    'duration' => $sessionEvents->first()->created_at->diffInMinutes($sessionEvents->last()->created_at),
                    'source' => $session->utm_source ?? 'Direct',
                    'device' => $session->device_type ?? 'Unknown',
                    'is_lead' => Lead::where('visitor_id', $session->visitor_id)->exists(),
                ];
            });

            return [
                'total_sessions' => $sessions->count(),
                'session_details' => $sessionDetails,
                'avg_session_duration' => $sessionDetails->avg('duration'),
                'sessions_with_leads' => $sessionDetails->where('is_lead', true)->count(),
            ];
        });
    }

    public function getRecentLeads(int $minutes = 60): array
    {
        $cacheKey = $this->cachePrefix . "recent_leads:{$minutes}";
        
        return Cache::remember($cacheKey, 60, function () use ($minutes) {
            $recentLeads = Lead::where('created_at', '>=', now()->subMinutes($minutes))
                ->with(['interactions' => function ($query) {
                    $query->latest()->limit(3);
                }])
                ->orderBy('created_at', 'desc')
                ->limit(20)
                ->get();

            return [
                'total_recent' => $recentLeads->count(),
                'leads' => $recentLeads->map(function ($lead) {
                    return [
                        'id' => $lead->id,
                        'name' => $lead->name,
                        'email' => $lead->email,
                        'phone' => $lead->phone,
                        'source' => $lead->source,
                        'service_interest' => $lead->service_interest,
                        'score' => $lead->score,
                        'status' => $lead->status,
                        'created_at' => $lead->created_at,
                        'time_ago' => $lead->created_at->diffForHumans(),
                        'recent_interactions' => $lead->interactions->count(),
                    ];
                }),
                'leads_by_source' => $recentLeads->groupBy('source')->map->count(),
                'leads_by_service' => $recentLeads->groupBy('service_interest')->map->count(),
            ];
        });
    }

    public function getLiveEvents(int $minutes = 15): array
    {
        $cacheKey = $this->cachePrefix . "live_events:{$minutes}";
        
        return Cache::remember($cacheKey, 30, function () use ($minutes) {
            $events = AnalyticsEvent::where('created_at', '>=', now()->subMinutes($minutes))
                ->orderBy('created_at', 'desc')
                ->limit(100)
                ->get();

            $eventSummary = [
                'page_views' => $events->where('event_name', 'page_view')->count(),
                'form_submits' => $events->where('event_name', 'form_submit')->count(),
                'button_clicks' => $events->where('event_name', 'button_click')->count(),
                'downloads' => $events->where('event_name', 'download')->count(),
                'email_opens' => $events->where('event_name', 'email_open')->count(),
            ];

            $recentEvents = $events->take(20)->map(function ($event) {
                return [
                    'event_name' => $event->event_name,
                    'page_url' => $event->page_url,
                    'visitor_id' => $event->visitor_id,
                    'created_at' => $event->created_at,
                    'time_ago' => $event->created_at->diffForHumans(),
                    'metadata' => $event->metadata,
                ];
            });

            return [
                'event_summary' => $eventSummary,
                'recent_events' => $recentEvents,
                'events_per_minute' => $this->getEventsPerMinute($minutes),
            ];
        });
    }

    public function getCurrentCampaigns(): array
    {
        $cacheKey = $this->cachePrefix . 'current_campaigns';
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () {
            $emailCampaigns = \App\Models\EmailCampaign::where('status', 'sending')
                ->orWhere(function ($query) {
                    $query->where('status', 'sent')
                        ->where('sent_at', '>=', now()->subHours(24));
                })
                ->get();

            $whatsappCampaigns = \App\Models\WhatsAppCampaign::where('status', 'sending')
                ->orWhere(function ($query) {
                    $query->where('status', 'sent')
                        ->where('sent_at', '>=', now()->subHours(24));
                })
                ->get();

            $socialMediaPosts = \App\Models\SocialMediaPost::where('status', 'published')
                ->where('published_at', '>=', now()->subHours(24))
                ->get();

            return [
                'email_campaigns' => $emailCampaigns->map(function ($campaign) {
                    return [
                        'id' => $campaign->id,
                        'name' => $campaign->name,
                        'status' => $campaign->status,
                        'sent_count' => $campaign->sent_count,
                        'opened_count' => $campaign->opened_count,
                        'clicked_count' => $campaign->clicked_count,
                        'open_rate' => $campaign->sent_count > 0 ? ($campaign->opened_count / $campaign->sent_count) * 100 : 0,
                    ];
                }),
                'whatsapp_campaigns' => $whatsappCampaigns->map(function ($campaign) {
                    return [
                        'id' => $campaign->id,
                        'name' => $campaign->name,
                        'status' => $campaign->status,
                        'sent_count' => $campaign->sent_count,
                        'delivered_count' => $campaign->delivered_count,
                        'read_count' => $campaign->read_count,
                        'delivery_rate' => $campaign->sent_count > 0 ? ($campaign->delivered_count / $campaign->sent_count) * 100 : 0,
                    ];
                }),
                'social_media_posts' => $socialMediaPosts->map(function ($post) {
                    $analytics = $post->analytics ?? [];
                    $engagement = ($analytics['likes'] ?? 0) + ($analytics['comments'] ?? 0) + ($analytics['shares'] ?? 0);
                    
                    return [
                        'id' => $post->id,
                        'title' => $post->title,
                        'platforms' => $post->cross_post_platforms,
                        'engagement' => $engagement,
                        'reach' => $analytics['reach'] ?? 0,
                        'published_at' => $post->published_at,
                    ];
                }),
            ];
        });
    }

    public function getRealTimeMetrics(): array
    {
        $cacheKey = $this->cachePrefix . 'real_time_metrics';
        
        return Cache::remember($cacheKey, 60, function () {
            $last5Minutes = now()->subMinutes(5);
            $last15Minutes = now()->subMinutes(15);
            $lastHour = now()->subHour();

            return [
                'last_5_minutes' => [
                    'page_views' => AnalyticsEvent::where('created_at', '>=', $last5Minutes)
                        ->where('event_name', 'page_view')->count(),
                    'unique_visitors' => AnalyticsEvent::where('created_at', '>=', $last5Minutes)
                        ->distinct('visitor_id')->count(),
                    'new_leads' => Lead::where('created_at', '>=', $last5Minutes)->count(),
                    'form_submissions' => AnalyticsEvent::where('created_at', '>=', $last5Minutes)
                        ->where('event_name', 'form_submit')->count(),
                ],
                'last_15_minutes' => [
                    'page_views' => AnalyticsEvent::where('created_at', '>=', $last15Minutes)
                        ->where('event_name', 'page_view')->count(),
                    'unique_visitors' => AnalyticsEvent::where('created_at', '>=', $last15Minutes)
                        ->distinct('visitor_id')->count(),
                    'new_leads' => Lead::where('created_at', '>=', $last15Minutes)->count(),
                    'form_submissions' => AnalyticsEvent::where('created_at', '>=', $last15Minutes)
                        ->where('event_name', 'form_submit')->count(),
                ],
                'last_hour' => [
                    'page_views' => AnalyticsEvent::where('created_at', '>=', $lastHour)
                        ->where('event_name', 'page_view')->count(),
                    'unique_visitors' => AnalyticsEvent::where('created_at', '>=', $lastHour)
                        ->distinct('visitor_id')->count(),
                    'new_leads' => Lead::where('created_at', '>=', $lastHour)->count(),
                    'form_submissions' => AnalyticsEvent::where('created_at', '>=', $lastHour)
                        ->where('event_name', 'form_submit')->count(),
                ],
            ];
        });
    }

    public function getGeographicActivity(): array
    {
        $cacheKey = $this->cachePrefix . 'geographic_activity';
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () {
            $activeThreshold = now()->subMinutes(30);
            
            $cityActivity = AnalyticsEvent::where('created_at', '>=', $activeThreshold)
                ->where('event_name', 'page_view')
                ->select('city', \DB::raw('count(*) as activity_count'))
                ->groupBy('city')
                ->orderBy('activity_count', 'desc')
                ->limit(10)
                ->get();

            $countryActivity = AnalyticsEvent::where('created_at', '>=', $activeThreshold)
                ->where('event_name', 'page_view')
                ->select('country', \DB::raw('count(*) as activity_count'))
                ->groupBy('country')
                ->orderBy('activity_count', 'desc')
                ->limit(10)
                ->get();

            return [
                'top_cities' => $cityActivity,
                'top_countries' => $countryActivity,
                'total_locations' => AnalyticsEvent::where('created_at', '>=', $activeThreshold)
                    ->distinct('city')->count(),
            ];
        });
    }

    public function getDeviceBreakdown(): array
    {
        $cacheKey = $this->cachePrefix . 'device_breakdown';
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () {
            $activeThreshold = now()->subMinutes(30);
            
            $deviceTypes = AnalyticsEvent::where('created_at', '>=', $activeThreshold)
                ->where('event_name', 'page_view')
                ->select('device_type', \DB::raw('count(*) as count'))
                ->groupBy('device_type')
                ->get();

            $browsers = AnalyticsEvent::where('created_at', '>=', $activeThreshold)
                ->where('event_name', 'page_view')
                ->select('browser', \DB::raw('count(*) as count'))
                ->groupBy('browser')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get();

            return [
                'device_types' => $deviceTypes,
                'top_browsers' => $browsers,
                'mobile_percentage' => $this->getMobilePercentage($deviceTypes),
            ];
        });
    }

    protected function getVisitorTrend(): array
    {
        $trend = [];
        for ($i = 14; $i >= 0; $i--) {
            $date = now()->subMinutes($i * 5);
            $visitors = AnalyticsEvent::where('created_at', '>=', $date->copy()->subMinutes(5))
                ->where('created_at', '<', $date)
                ->distinct('visitor_id')
                ->count();
                
            $trend[] = [
                'time' => $date->format('H:i'),
                'visitors' => $visitors,
            ];
        }
        
        return $trend;
    }

    protected function getEventsPerMinute(int $minutes): array
    {
        $eventsPerMinute = [];
        for ($i = $minutes; $i >= 0; $i--) {
            $minute = now()->subMinutes($i);
            $events = AnalyticsEvent::where('created_at', '>=', $minute->copy()->subMinute())
                ->where('created_at', '<', $minute)
                ->count();
                
            $eventsPerMinute[] = [
                'time' => $minute->format('H:i'),
                'events' => $events,
            ];
        }
        
        return $eventsPerMinute;
    }

    protected function getMobilePercentage($deviceTypes): float
    {
        $total = $deviceTypes->sum('count');
        $mobile = $deviceTypes->where('device_type', 'mobile')->sum('count');
        
        return $total > 0 ? ($mobile / $total) * 100 : 0;
    }

    public function trackRealTimeEvent(array $eventData): void
    {
        // Store event in database
        AnalyticsEvent::create($eventData);
        
        // Update real-time counters in Redis
        $this->updateRealTimeCounters($eventData);
        
        // Clear relevant caches
        $this->clearRelevantCaches($eventData);
    }

    protected function updateRealTimeCounters(array $eventData): void
    {
        $redis = Redis::connection();
        $minute = now()->format('Y-m-d H:i');
        
        // Increment counters
        $redis->incr("events:{$minute}");
        $redis->incr("events:{$eventData['event_name']}:{$minute}");
        $redis->sadd("visitors:{$minute}", $eventData['visitor_id']);
        
        // Set expiration
        $redis->expire("events:{$minute}", 3600); // 1 hour
        $redis->expire("events:{$eventData['event_name']}:{$minute}", 3600);
        $redis->expire("visitors:{$minute}", 3600);
    }

    protected function clearRelevantCaches(array $eventData): void
    {
        $patterns = [
            $this->cachePrefix . 'live_visitors',
            $this->cachePrefix . 'active_sessions',
            $this->cachePrefix . 'live_events:*',
            $this->cachePrefix . 'real_time_metrics',
        ];
        
        foreach ($patterns as $pattern) {
            if (str_contains($pattern, '*')) {
                // Clear pattern-based cache keys
                $keys = Cache::getRedis()->keys($pattern);
                if (!empty($keys)) {
                    Cache::getRedis()->del($keys);
                }
            } else {
                Cache::forget($pattern);
            }
        }
    }

    public function getAlerts(): array
    {
        $alerts = [];
        
        // Check for traffic spikes
        $currentTraffic = AnalyticsEvent::where('created_at', '>=', now()->subMinutes(5))->count();
        $avgTraffic = AnalyticsEvent::where('created_at', '>=', now()->subHour())
            ->where('created_at', '<', now()->subMinutes(5))
            ->count() / 11; // Average per 5-minute period
            
        if ($currentTraffic > $avgTraffic * 2) {
            $alerts[] = [
                'type' => 'traffic_spike',
                'message' => 'Traffic spike detected: ' . $currentTraffic . ' events in last 5 minutes',
                'severity' => 'info',
            ];
        }
        
        // Check for new leads
        $recentLeads = Lead::where('created_at', '>=', now()->subMinutes(5))->count();
        if ($recentLeads > 0) {
            $alerts[] = [
                'type' => 'new_leads',
                'message' => $recentLeads . ' new lead(s) in the last 5 minutes',
                'severity' => 'success',
            ];
        }
        
        // Check for form submission errors
        $formErrors = AnalyticsEvent::where('created_at', '>=', now()->subMinutes(15))
            ->where('event_name', 'form_error')
            ->count();
            
        if ($formErrors > 5) {
            $alerts[] = [
                'type' => 'form_errors',
                'message' => 'High number of form errors: ' . $formErrors . ' in last 15 minutes',
                'severity' => 'warning',
            ];
        }
        
        return $alerts;
    }
}
