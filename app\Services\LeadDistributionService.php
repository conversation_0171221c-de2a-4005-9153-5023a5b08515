<?php

namespace App\Services;

use App\Models\Lead;
use App\Models\User;
use App\Models\BusinessSetting;
use Illuminate\Support\Facades\Log;

class LeadDistributionService
{
    protected array $distributionMethods = [
        'round_robin',
        'load_balanced',
        'skill_based',
        'geographic',
        'random'
    ];

    public function distributeLead(Lead $lead): ?User
    {
        if (!BusinessSetting::get('lead_auto_assignment', true)) {
            return null;
        }

        $method = BusinessSetting::get('lead_distribution_method', 'round_robin');
        $assignedUser = $this->assignByMethod($lead, $method);

        if ($assignedUser) {
            $lead->update(['assigned_to' => $assignedUser->id]);
            
            Log::info('Lead automatically assigned', [
                'lead_id' => $lead->id,
                'assigned_to' => $assignedUser->id,
                'method' => $method,
            ]);

            // Trigger notification to assigned user
            $this->notifyAssignedUser($assignedUser, $lead);
        }

        return $assignedUser;
    }

    protected function assignByMethod(Lead $lead, string $method): ?User
    {
        return match ($method) {
            'round_robin' => $this->roundRobinAssignment(),
            'load_balanced' => $this->loadBalancedAssignment(),
            'skill_based' => $this->skillBasedAssignment($lead),
            'geographic' => $this->geographicAssignment($lead),
            'random' => $this->randomAssignment(),
            default => $this->roundRobinAssignment(),
        };
    }

    protected function roundRobinAssignment(): ?User
    {
        $lastAssignedId = BusinessSetting::get('last_assigned_user_id', 0);
        
        $users = $this->getAvailableUsers();
        if ($users->isEmpty()) {
            return null;
        }

        $currentIndex = $users->search(fn($user) => $user->id == $lastAssignedId);
        $nextIndex = ($currentIndex !== false) ? ($currentIndex + 1) % $users->count() : 0;
        
        $nextUser = $users->get($nextIndex);
        BusinessSetting::set('last_assigned_user_id', $nextUser->id);

        return $nextUser;
    }

    protected function loadBalancedAssignment(): ?User
    {
        $users = $this->getAvailableUsers();
        if ($users->isEmpty()) {
            return null;
        }

        // Get user with least active leads
        $userLoads = $users->map(function ($user) {
            $activeLeads = Lead::where('assigned_to', $user->id)
                ->whereIn('status', ['new', 'contacted', 'qualified'])
                ->count();
            
            return [
                'user' => $user,
                'load' => $activeLeads,
            ];
        });

        $leastLoaded = $userLoads->sortBy('load')->first();
        return $leastLoaded['user'];
    }

    protected function skillBasedAssignment(Lead $lead): ?User
    {
        $serviceInterest = $lead->service_interest;
        if (!$serviceInterest) {
            return $this->roundRobinAssignment();
        }

        // Map services to user skills/specializations
        $skillMapping = [
            'web-development' => ['web_developer', 'full_stack_developer'],
            'mobile-development' => ['mobile_developer', 'app_developer'],
            'digital-marketing' => ['digital_marketer', 'seo_specialist'],
            'graphic-design' => ['graphic_designer', 'ui_designer'],
        ];

        $requiredSkills = $skillMapping[$serviceInterest] ?? [];
        if (empty($requiredSkills)) {
            return $this->roundRobinAssignment();
        }

        $users = $this->getAvailableUsers();
        $skilledUsers = $users->filter(function ($user) use ($requiredSkills) {
            $userSkills = $user->skills ?? [];
            return !empty(array_intersect($requiredSkills, $userSkills));
        });

        if ($skilledUsers->isEmpty()) {
            return $this->roundRobinAssignment();
        }

        return $this->loadBalancedAssignmentFromUsers($skilledUsers);
    }

    protected function geographicAssignment(Lead $lead): ?User
    {
        // For now, return round robin. In future, implement based on lead location
        return $this->roundRobinAssignment();
    }

    protected function randomAssignment(): ?User
    {
        $users = $this->getAvailableUsers();
        if ($users->isEmpty()) {
            return null;
        }

        return $users->random();
    }

    protected function getAvailableUsers(): \Illuminate\Support\Collection
    {
        // Get users who can be assigned leads
        return User::where('is_active', true)
            ->where('can_receive_leads', true)
            ->get();
    }

    protected function loadBalancedAssignmentFromUsers(\Illuminate\Support\Collection $users): ?User
    {
        if ($users->isEmpty()) {
            return null;
        }

        $userLoads = $users->map(function ($user) {
            $activeLeads = Lead::where('assigned_to', $user->id)
                ->whereIn('status', ['new', 'contacted', 'qualified'])
                ->count();
            
            return [
                'user' => $user,
                'load' => $activeLeads,
            ];
        });

        $leastLoaded = $userLoads->sortBy('load')->first();
        return $leastLoaded['user'];
    }

    protected function notifyAssignedUser(User $user, Lead $lead): void
    {
        // Send notification to assigned user
        // This could be email, WhatsApp, or in-app notification
        Log::info('Lead assignment notification sent', [
            'user_id' => $user->id,
            'lead_id' => $lead->id,
        ]);
    }

    public function reassignLead(Lead $lead, ?int $newUserId = null): ?User
    {
        if ($newUserId) {
            $newUser = User::find($newUserId);
            if ($newUser && $newUser->can_receive_leads) {
                $lead->update(['assigned_to' => $newUserId]);
                $this->notifyAssignedUser($newUser, $lead);
                return $newUser;
            }
        }

        // Auto-reassign using current method
        return $this->distributeLead($lead);
    }

    public function getDistributionStats(): array
    {
        $users = $this->getAvailableUsers();
        $stats = [];

        foreach ($users as $user) {
            $stats[] = [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'total_leads' => Lead::where('assigned_to', $user->id)->count(),
                'active_leads' => Lead::where('assigned_to', $user->id)
                    ->whereIn('status', ['new', 'contacted', 'qualified'])
                    ->count(),
                'converted_leads' => Lead::where('assigned_to', $user->id)
                    ->where('status', 'converted')
                    ->count(),
                'conversion_rate' => $this->calculateConversionRate($user->id),
            ];
        }

        return $stats;
    }

    protected function calculateConversionRate(int $userId): float
    {
        $totalLeads = Lead::where('assigned_to', $userId)->count();
        $convertedLeads = Lead::where('assigned_to', $userId)
            ->where('status', 'converted')
            ->count();

        return $totalLeads > 0 ? ($convertedLeads / $totalLeads) * 100 : 0;
    }

    public function rebalanceLeads(): int
    {
        $users = $this->getAvailableUsers();
        if ($users->count() < 2) {
            return 0;
        }

        $stats = collect($this->getDistributionStats());
        $avgLoad = $stats->avg('active_leads');
        $rebalanced = 0;

        foreach ($stats as $stat) {
            if ($stat['active_leads'] > $avgLoad * 1.5) {
                // User is overloaded, redistribute some leads
                $excessLeads = $stat['active_leads'] - $avgLoad;
                $leadsToReassign = Lead::where('assigned_to', $stat['user_id'])
                    ->whereIn('status', ['new', 'contacted'])
                    ->orderBy('created_at', 'desc')
                    ->limit($excessLeads)
                    ->get();

                foreach ($leadsToReassign as $lead) {
                    $this->reassignLead($lead);
                    $rebalanced++;
                }
            }
        }

        return $rebalanced;
    }
}
