<?php

namespace App\Jobs;

use App\Models\SocialMediaPost;
use App\Services\SocialMediaService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessScheduledSocialMediaPostsJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    public $timeout = 600; // 10 minutes
    public $tries = 3;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(SocialMediaService $socialMediaService): void
    {
        try {
            $posts = SocialMediaPost::where('status', 'scheduled')
                ->where('scheduled_at', '<=', now())
                ->get();

            Log::info('Processing scheduled social media posts', [
                'posts_count' => $posts->count(),
            ]);

            foreach ($posts as $post) {
                try {
                    $results = $socialMediaService->publishPost($post);

                    Log::info('Scheduled social media post published successfully', [
                        'post_id' => $post->id,
                        'title' => $post->title,
                        'platforms' => $post->cross_post_platforms,
                        'success_count' => count($results['success']),
                        'failed_count' => count($results['failed']),
                    ]);

                } catch (\Exception $e) {
                    Log::error('Failed to publish scheduled social media post', [
                        'post_id' => $post->id,
                        'title' => $post->title,
                        'error' => $e->getMessage(),
                    ]);

                    // Mark post as failed
                    $post->update([
                        'status' => 'failed',
                        'error_message' => $e->getMessage(),
                    ]);
                }
            }

        } catch (\Exception $e) {
            Log::error('Scheduled social media posts processing job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Scheduled social media posts processing job failed permanently', [
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
