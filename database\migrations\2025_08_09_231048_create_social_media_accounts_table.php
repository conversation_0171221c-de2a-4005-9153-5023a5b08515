<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('social_media_accounts', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Account display name
            $table->enum('platform', ['facebook', 'instagram', 'linkedin', 'twitter', 'youtube', 'tiktok']);
            $table->string('account_id')->nullable(); // Platform-specific account ID
            $table->string('username')->nullable(); // @username or handle
            $table->string('access_token')->nullable(); // OAuth access token
            $table->string('refresh_token')->nullable(); // OAuth refresh token
            $table->timestamp('token_expires_at')->nullable();
            $table->json('account_info')->nullable(); // Additional account details
            $table->json('permissions')->nullable(); // Available permissions/scopes
            $table->boolean('is_active')->default(true);
            $table->boolean('auto_post')->default(false); // Enable automatic posting
            $table->json('posting_schedule')->nullable(); // Preferred posting times
            $table->integer('daily_post_limit')->default(10);
            $table->timestamp('last_posted_at')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();

            $table->unique(['platform', 'account_id']);
            $table->index(['platform', 'is_active']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('social_media_accounts');
    }
};
