name: Email System Comprehensive Tests

on:
  push:
    branches: [ main, development, 'feature/**' ]
  pull_request:
    branches: [ main, development ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PHP_VERSION: '8.2'
  MYSQL_VERSION: '8.0'

jobs:
  # Email Import System Tests
  email-import-tests:
    name: 📧 Email Import System Tests
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: email_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐘 Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}
        extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, mysql, redis

    - name: 📦 Install dependencies
      run: composer install --no-progress --prefer-dist

    - name: 📋 Setup environment
      run: |
        cp .env.example .env
        php artisan key:generate

    - name: 🗄️ Setup database
      run: |
        php artisan migrate --force
        php artisan db:seed --force
      env:
        DB_CONNECTION: mysql
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: email_test
        DB_USERNAME: root
        DB_PASSWORD: password

    - name: 📧 Test massive email import
      run: |
        echo "Testing massive email import functionality..."
        php artisan emails:import-massive storage/app/sample_massive_import.csv "Test Import" --chunk-size=100
        php artisan queue:work --once --verbose

    - name: 🌍 Test geographic filtering
      run: |
        echo "Testing geographic filtering..."
        php artisan tinker --execute="
        \$contacts = App\Models\Contact::where('state', 'Tamil Nadu')->count();
        echo 'Tamil Nadu contacts: ' . \$contacts . PHP_EOL;
        "

    - name: 📊 Test MySQL performance
      run: |
        echo "Testing MySQL performance and maintenance..."
        php artisan mysql:maintenance --stats
        php artisan mysql:maintenance --check

  # Email Campaign Tests
  email-campaign-tests:
    name: 📬 Email Campaign Tests
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: campaign_test
        ports:
          - 3306:3306

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐘 Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}

    - name: 📦 Install dependencies
      run: composer install

    - name: 📋 Setup environment
      run: |
        cp .env.example .env
        php artisan key:generate
        php artisan migrate --force

    - name: 📬 Test email campaign creation
      run: |
        echo "Testing email campaign functionality..."
        php artisan tinker --execute="
        \$campaign = App\Models\EmailCampaign::create([
          'name' => 'Test Campaign',
          'subject' => 'Test Subject',
          'content' => 'Test Content',
          'status' => 'draft',
          'type' => 'newsletter',
          'created_by' => 1
        ]);
        echo 'Campaign created: ' . \$campaign->id . PHP_EOL;
        "

    - name: 📊 Test campaign analytics
      run: |
        echo "Testing campaign analytics..."
        # Add campaign analytics tests here

  # Performance Tests for 1 Crore Emails
  performance-tests:
    name: ⚡ Performance Tests (1 Crore Scale)
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: performance_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐘 Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}

    - name: 📦 Install dependencies
      run: composer install

    - name: 📋 Setup environment
      run: |
        cp .env.example .env
        php artisan key:generate
        php artisan migrate --force

    - name: ⚡ Test database performance
      run: |
        echo "Testing database performance with large datasets..."
        php artisan tinker --execute="
        // Test index performance
        \$start = microtime(true);
        \$count = App\Models\Contact::where('country', 'India')->count();
        \$end = microtime(true);
        echo 'Geographic query time: ' . (\$end - \$start) . 's' . PHP_EOL;
        echo 'Contact count: ' . \$count . PHP_EOL;
        "

    - name: 📊 Test memory usage
      run: |
        echo "Testing memory usage with large operations..."
        php -d memory_limit=512M artisan tinker --execute="
        echo 'Memory usage: ' . memory_get_usage(true) / 1024 / 1024 . ' MB' . PHP_EOL;
        "

  # Email Deliverability Tests
  deliverability-tests:
    name: 📮 Email Deliverability Tests
    runs-on: ubuntu-latest

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐘 Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}

    - name: 📦 Install dependencies
      run: composer install

    - name: 📮 Test email configuration
      run: |
        echo "Testing email configuration and deliverability..."
        # Add email deliverability tests here

    - name: 🔍 Test spam score checking
      run: |
        echo "Testing spam score functionality..."
        # Add spam score tests here

  # WhatsApp Integration Tests
  whatsapp-tests:
    name: 📱 WhatsApp Integration Tests
    runs-on: ubuntu-latest

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐘 Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}

    - name: 📦 Install dependencies
      run: composer install

    - name: 📱 Test WhatsApp integration
      run: |
        echo "Testing WhatsApp integration functionality..."
        # Add WhatsApp tests here

  # Security Tests
  security-tests:
    name: 🔒 Security Tests
    runs-on: ubuntu-latest

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🔒 Test data encryption
      run: |
        echo "Testing data encryption and security..."
        # Add security tests here

    - name: 🛡️ Test access controls
      run: |
        echo "Testing access controls and permissions..."
        # Add access control tests here

  # Integration Tests
  integration-tests:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: integration_test
        ports:
          - 3306:3306

      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379

    steps:
    - name: 📥 Checkout code
      uses: actions/checkout@v4

    - name: 🐘 Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: ${{ env.PHP_VERSION }}

    - name: 📦 Install dependencies
      run: composer install

    - name: 📋 Setup environment
      run: |
        cp .env.example .env
        php artisan key:generate
        php artisan migrate --force

    - name: 🔗 Test full email workflow
      run: |
        echo "Testing complete email management workflow..."
        # Import -> Filter -> Campaign -> Send -> Track
        php artisan emails:import-massive storage/app/sample_massive_import.csv "Integration Test"
        php artisan queue:work --once --verbose

    - name: 📊 Verify results
      run: |
        echo "Verifying integration test results..."
        php artisan mysql:maintenance --stats
