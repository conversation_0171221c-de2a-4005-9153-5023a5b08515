<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SupportTicket extends Model
{
    use HasFactory;

    protected $fillable = [
        'customer_id',
        'assigned_to',
        'ticket_number',
        'title',
        'description',
        'priority',
        'category',
        'status',
        'last_response_at',
        'resolved_at',
        'metadata',
    ];

    protected $casts = [
        'last_response_at' => 'datetime',
        'resolved_at' => 'datetime',
        'metadata' => 'array',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function responses(): HasMany
    {
        return $this->hasMany(SupportTicketResponse::class);
    }

    public function attachments(): HasMany
    {
        return $this->hasMany(SupportTicketAttachment::class);
    }

    public function scopeOpen($query)
    {
        return $query->whereIn('status', ['open', 'in_progress', 'awaiting_response']);
    }

    public function scopeClosed($query)
    {
        return $query->whereIn('status', ['resolved', 'closed']);
    }

    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByCustomer($query, int $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            'open' => 'blue',
            'in_progress' => 'yellow',
            'awaiting_response' => 'orange',
            'resolved' => 'green',
            'closed' => 'gray',
            default => 'gray',
        };
    }

    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'urgent' => 'red',
            default => 'gray',
        };
    }

    public function isOverdue(): bool
    {
        if ($this->status === 'resolved' || $this->status === 'closed') {
            return false;
        }

        $slaHours = match ($this->priority) {
            'urgent' => 4,
            'high' => 24,
            'medium' => 48,
            'low' => 72,
            default => 48,
        };

        return $this->created_at->addHours($slaHours)->isPast();
    }

    public function getResponseTimeAttribute(): ?int
    {
        $firstResponse = $this->responses()
            ->where('is_internal', false)
            ->oldest()
            ->first();

        if (!$firstResponse) {
            return null;
        }

        return $this->created_at->diffInMinutes($firstResponse->created_at);
    }
}
