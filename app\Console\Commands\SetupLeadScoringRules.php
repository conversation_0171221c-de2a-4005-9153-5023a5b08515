<?php

namespace App\Console\Commands;

use App\Models\LeadScoringRule;
use App\Models\BusinessSetting;
use Illuminate\Console\Command;

class SetupLeadScoringRules extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bhavitech:setup-lead-scoring {--force : Force recreation of rules}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup default lead scoring rules and business settings for Bhavitech';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up Bhavitech lead scoring system...');

        if ($this->option('force')) {
            $this->warn('Force mode: Deleting existing rules...');
            LeadScoringRule::truncate();
        }

        $this->setupLeadScoringRules();
        $this->setupBusinessSettings();

        $this->info('✅ Lead scoring system setup completed!');

        return Command::SUCCESS;
    }

    protected function setupLeadScoringRules(): void
    {
        $this->info('Creating lead scoring rules...');

        $rules = LeadScoringRule::getDefaultRules();
        $created = 0;

        foreach ($rules as $ruleData) {
            $existing = LeadScoringRule::where('name', $ruleData['name'])->first();

            if (!$existing) {
                LeadScoringRule::create(array_merge($ruleData, [
                    'is_active' => true,
                ]));
                $created++;
                $this->line("  ✓ Created rule: {$ruleData['name']}");
            } else {
                $this->line("  - Rule already exists: {$ruleData['name']}");
            }
        }

        $this->info("Created {$created} new lead scoring rules.");
    }

    protected function setupBusinessSettings(): void
    {
        $this->info('Setting up business settings...');

        $settings = BusinessSetting::getDefaultSettings();
        $created = 0;

        foreach ($settings as $settingData) {
            $existing = BusinessSetting::where('key', $settingData['key'])->first();

            if (!$existing) {
                BusinessSetting::create($settingData);
                $created++;
                $this->line("  ✓ Created setting: {$settingData['key']}");
            } else {
                $this->line("  - Setting already exists: {$settingData['key']}");
            }
        }

        // Additional Bhavitech-specific settings
        $bhavitechSettings = [
            [
                'key' => 'lead_distribution_method',
                'value' => 'round_robin',
                'type' => 'string',
                'group' => 'lead_management',
                'label' => 'Lead Distribution Method',
                'description' => 'Method for distributing leads to team members',
                'options' => ['round_robin', 'load_balanced', 'skill_based', 'random'],
                'sort_order' => 1,
            ],
            [
                'key' => 'auto_score_update_frequency',
                'value' => 'daily',
                'type' => 'string',
                'group' => 'lead_management',
                'label' => 'Auto Score Update Frequency',
                'description' => 'How often to automatically update lead scores',
                'options' => ['hourly', 'daily', 'weekly'],
                'sort_order' => 2,
            ],
            [
                'key' => 'high_score_threshold',
                'value' => 80,
                'type' => 'integer',
                'group' => 'lead_management',
                'label' => 'High Score Threshold',
                'description' => 'Score threshold for high-quality leads',
                'sort_order' => 3,
            ],
            [
                'key' => 'medium_score_threshold',
                'value' => 60,
                'type' => 'integer',
                'group' => 'lead_management',
                'label' => 'Medium Score Threshold',
                'description' => 'Score threshold for medium-quality leads',
                'sort_order' => 4,
            ],
        ];

        foreach ($bhavitechSettings as $settingData) {
            $existing = BusinessSetting::where('key', $settingData['key'])->first();

            if (!$existing) {
                BusinessSetting::create($settingData);
                $created++;
                $this->line("  ✓ Created setting: {$settingData['key']}");
            }
        }

        $this->info("Created {$created} new business settings.");
    }
}
