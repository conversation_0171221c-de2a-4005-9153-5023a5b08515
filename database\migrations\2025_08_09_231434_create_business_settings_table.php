<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique(); // Setting identifier
            $table->text('value')->nullable(); // Setting value
            $table->string('type')->default('string'); // string, integer, boolean, json, array
            $table->string('group')->default('general'); // Group settings together
            $table->string('label'); // Human-readable label
            $table->text('description')->nullable(); // Setting description
            $table->boolean('is_public')->default(false); // Can be accessed publicly
            $table->boolean('is_encrypted')->default(false); // Should be encrypted
            $table->json('validation_rules')->nullable(); // Validation rules
            $table->json('options')->nullable(); // Available options for select fields
            $table->integer('sort_order')->default(0); // Display order
            $table->timestamps();

            $table->index(['group', 'sort_order']);
            $table->index('is_public');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_settings');
    }
};
