<?php

namespace App\Jobs;

use App\Models\EmailCampaign;
use App\Services\EmailMarketingService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessScheduledCampaignsJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    public $timeout = 600; // 10 minutes
    public $tries = 3;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(EmailMarketingService $emailService): void
    {
        try {
            $campaigns = EmailCampaign::readyToSend()->get();

            Log::info('Processing scheduled email campaigns', [
                'campaigns_count' => $campaigns->count(),
            ]);

            foreach ($campaigns as $campaign) {
                try {
                    $results = $emailService->sendCampaign($campaign);

                    Log::info('Scheduled campaign sent successfully', [
                        'campaign_id' => $campaign->id,
                        'campaign_name' => $campaign->name,
                        'sent_count' => $results['sent'],
                        'failed_count' => $results['failed'],
                    ]);

                } catch (\Exception $e) {
                    Log::error('Failed to send scheduled campaign', [
                        'campaign_id' => $campaign->id,
                        'campaign_name' => $campaign->name,
                        'error' => $e->getMessage(),
                    ]);

                    // Mark campaign as failed
                    $campaign->update([
                        'status' => 'failed',
                        'error_message' => $e->getMessage(),
                    ]);
                }
            }

        } catch (\Exception $e) {
            Log::error('Scheduled campaigns processing job failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Scheduled campaigns processing job failed permanently', [
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
