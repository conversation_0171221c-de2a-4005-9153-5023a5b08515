<?php

namespace App\Services;

use App\Models\Lead;
use App\Models\AnalyticsEvent;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ForecastingService
{
    public function forecastLeads(string $period = 'month'): array
    {
        $historicalData = $this->getHistoricalLeadData($period);
        $forecast = $this->calculateLinearTrend($historicalData);
        
        return [
            'historical_data' => $historicalData,
            'forecast' => $forecast,
            'confidence_interval' => $this->calculateConfidenceInterval($historicalData),
            'trend_analysis' => $this->analyzeTrend($historicalData),
            'seasonal_patterns' => $this->detectSeasonalPatterns($historicalData),
            'recommendations' => $this->generateLeadForecastRecommendations($forecast),
        ];
    }

    public function projectRevenue(string $period = 'month'): array
    {
        $historicalRevenue = $this->getHistoricalRevenueData($period);
        $projection = $this->calculateRevenueProjection($historicalRevenue);
        
        return [
            'historical_revenue' => $historicalRevenue,
            'projection' => $projection,
            'growth_rate' => $this->calculateGrowthRate($historicalRevenue),
            'revenue_by_service' => $this->projectRevenueByService($period),
            'break_even_analysis' => $this->calculateBreakEvenAnalysis(),
            'recommendations' => $this->generateRevenueRecommendations($projection),
        ];
    }

    public function predictGrowth(string $period = 'month'): array
    {
        $growthMetrics = $this->calculateGrowthMetrics($period);
        $prediction = $this->predictFutureGrowth($growthMetrics, $period);
        
        return [
            'current_metrics' => $growthMetrics,
            'growth_prediction' => $prediction,
            'key_drivers' => $this->identifyGrowthDrivers(),
            'bottlenecks' => $this->identifyGrowthBottlenecks(),
            'optimization_opportunities' => $this->identifyOptimizationOpportunities(),
            'action_plan' => $this->generateGrowthActionPlan($prediction),
        ];
    }

    protected function getHistoricalLeadData(string $period): array
    {
        $periods = $this->getPeriodRange($period, 12); // Last 12 periods
        $data = [];
        
        foreach ($periods as $periodData) {
            $leadCount = Lead::whereBetween('created_at', [
                $periodData['start'],
                $periodData['end']
            ])->count();
            
            $conversionCount = Lead::whereBetween('created_at', [
                $periodData['start'],
                $periodData['end']
            ])->where('status', 'converted')->count();
            
            $data[] = [
                'period' => $periodData['label'],
                'start_date' => $periodData['start']->format('Y-m-d'),
                'end_date' => $periodData['end']->format('Y-m-d'),
                'leads' => $leadCount,
                'conversions' => $conversionCount,
                'conversion_rate' => $leadCount > 0 ? ($conversionCount / $leadCount) * 100 : 0,
            ];
        }
        
        return $data;
    }

    protected function getHistoricalRevenueData(string $period): array
    {
        $periods = $this->getPeriodRange($period, 12);
        $data = [];
        
        foreach ($periods as $periodData) {
            $convertedLeads = Lead::whereBetween('created_at', [
                $periodData['start'],
                $periodData['end']
            ])->where('status', 'converted')->get();
            
            $revenue = $convertedLeads->sum(function ($lead) {
                return $this->estimateLeadValue($lead);
            });
            
            $data[] = [
                'period' => $periodData['label'],
                'start_date' => $periodData['start']->format('Y-m-d'),
                'end_date' => $periodData['end']->format('Y-m-d'),
                'revenue' => $revenue,
                'deals' => $convertedLeads->count(),
                'avg_deal_size' => $convertedLeads->count() > 0 ? $revenue / $convertedLeads->count() : 0,
            ];
        }
        
        return $data;
    }

    protected function calculateLinearTrend(array $data): array
    {
        $n = count($data);
        if ($n < 2) {
            return ['slope' => 0, 'intercept' => 0, 'forecast' => []];
        }
        
        $x = range(1, $n);
        $y = array_column($data, 'leads');
        
        $sumX = array_sum($x);
        $sumY = array_sum($y);
        $sumXY = 0;
        $sumX2 = 0;
        
        for ($i = 0; $i < $n; $i++) {
            $sumXY += $x[$i] * $y[$i];
            $sumX2 += $x[$i] * $x[$i];
        }
        
        $slope = ($n * $sumXY - $sumX * $sumY) / ($n * $sumX2 - $sumX * $sumX);
        $intercept = ($sumY - $slope * $sumX) / $n;
        
        // Generate forecast for next 6 periods
        $forecast = [];
        for ($i = 1; $i <= 6; $i++) {
            $nextPeriod = $n + $i;
            $predictedValue = $slope * $nextPeriod + $intercept;
            
            $forecast[] = [
                'period' => $nextPeriod,
                'predicted_leads' => max(0, round($predictedValue)),
                'confidence' => $this->calculatePredictionConfidence($i),
            ];
        }
        
        return [
            'slope' => $slope,
            'intercept' => $intercept,
            'r_squared' => $this->calculateRSquared($x, $y, $slope, $intercept),
            'forecast' => $forecast,
        ];
    }

    protected function calculateRevenueProjection(array $data): array
    {
        $revenues = array_column($data, 'revenue');
        $n = count($revenues);
        
        if ($n < 2) {
            return ['projected_revenue' => 0, 'growth_rate' => 0];
        }
        
        // Calculate average growth rate
        $growthRates = [];
        for ($i = 1; $i < $n; $i++) {
            if ($revenues[$i - 1] > 0) {
                $growthRates[] = ($revenues[$i] - $revenues[$i - 1]) / $revenues[$i - 1];
            }
        }
        
        $avgGrowthRate = count($growthRates) > 0 ? array_sum($growthRates) / count($growthRates) : 0;
        $lastRevenue = end($revenues);
        
        // Project next 6 periods
        $projections = [];
        $currentRevenue = $lastRevenue;
        
        for ($i = 1; $i <= 6; $i++) {
            $currentRevenue = $currentRevenue * (1 + $avgGrowthRate);
            $projections[] = [
                'period' => $i,
                'projected_revenue' => round($currentRevenue),
                'growth_rate' => $avgGrowthRate * 100,
            ];
        }
        
        return [
            'avg_growth_rate' => $avgGrowthRate * 100,
            'projections' => $projections,
            'total_projected' => array_sum(array_column($projections, 'projected_revenue')),
        ];
    }

    protected function calculateGrowthMetrics(string $period): array
    {
        $currentPeriodStart = $this->getCurrentPeriodStart($period);
        $previousPeriodStart = $this->getPreviousPeriodStart($period);
        
        $currentLeads = Lead::where('created_at', '>=', $currentPeriodStart)->count();
        $previousLeads = Lead::whereBetween('created_at', [
            $previousPeriodStart,
            $currentPeriodStart
        ])->count();
        
        $currentTraffic = AnalyticsEvent::where('created_at', '>=', $currentPeriodStart)
            ->where('event_name', 'page_view')
            ->distinct('visitor_id')
            ->count();
            
        $previousTraffic = AnalyticsEvent::whereBetween('created_at', [
            $previousPeriodStart,
            $currentPeriodStart
        ])->where('event_name', 'page_view')
            ->distinct('visitor_id')
            ->count();
        
        return [
            'lead_growth' => $this->calculatePercentageChange($previousLeads, $currentLeads),
            'traffic_growth' => $this->calculatePercentageChange($previousTraffic, $currentTraffic),
            'conversion_rate_change' => $this->calculateConversionRateChange($period),
            'customer_acquisition_cost' => $this->calculateCAC($period),
            'customer_lifetime_value' => $this->calculateCLV(),
        ];
    }

    protected function predictFutureGrowth(array $metrics, string $period): array
    {
        $leadGrowth = $metrics['lead_growth'];
        $trafficGrowth = $metrics['traffic_growth'];
        
        // Simple prediction based on current trends
        $predictions = [];
        for ($i = 1; $i <= 6; $i++) {
            $predictions[] = [
                'period' => $i,
                'predicted_lead_growth' => $leadGrowth * (1 - ($i * 0.1)), // Diminishing returns
                'predicted_traffic_growth' => $trafficGrowth * (1 - ($i * 0.05)),
                'confidence' => max(50, 90 - ($i * 10)), // Decreasing confidence
            ];
        }
        
        return $predictions;
    }

    protected function identifyGrowthDrivers(): array
    {
        return [
            [
                'driver' => 'Organic Search Traffic',
                'impact_score' => 85,
                'trend' => 'increasing',
                'recommendation' => 'Continue SEO optimization efforts',
            ],
            [
                'driver' => 'Social Media Engagement',
                'impact_score' => 72,
                'trend' => 'stable',
                'recommendation' => 'Increase social media content frequency',
            ],
            [
                'driver' => 'Email Marketing',
                'impact_score' => 68,
                'trend' => 'increasing',
                'recommendation' => 'Expand email automation sequences',
            ],
            [
                'driver' => 'WhatsApp Marketing',
                'impact_score' => 78,
                'trend' => 'increasing',
                'recommendation' => 'Scale WhatsApp automation',
            ],
        ];
    }

    protected function identifyGrowthBottlenecks(): array
    {
        return [
            [
                'bottleneck' => 'Lead Response Time',
                'severity' => 'high',
                'impact' => 'Delayed responses reduce conversion by 15%',
                'solution' => 'Implement automated lead routing',
            ],
            [
                'bottleneck' => 'Mobile Website Performance',
                'severity' => 'medium',
                'impact' => 'Slow mobile loading affects 25% of visitors',
                'solution' => 'Optimize mobile page speed',
            ],
        ];
    }

    protected function identifyOptimizationOpportunities(): array
    {
        return [
            [
                'opportunity' => 'A/B Test Landing Pages',
                'potential_impact' => '20% increase in conversions',
                'effort_required' => 'medium',
                'timeline' => '2-4 weeks',
            ],
            [
                'opportunity' => 'Implement Chatbot',
                'potential_impact' => '30% faster lead response',
                'effort_required' => 'high',
                'timeline' => '4-6 weeks',
            ],
        ];
    }

    // Helper methods
    protected function getPeriodRange(string $period, int $count): array
    {
        $periods = [];
        $current = now();
        
        for ($i = $count - 1; $i >= 0; $i--) {
            switch ($period) {
                case 'week':
                    $start = $current->copy()->subWeeks($i)->startOfWeek();
                    $end = $start->copy()->endOfWeek();
                    $label = $start->format('M d');
                    break;
                case 'quarter':
                    $start = $current->copy()->subQuarters($i)->startOfQuarter();
                    $end = $start->copy()->endOfQuarter();
                    $label = 'Q' . $start->quarter . ' ' . $start->year;
                    break;
                case 'year':
                    $start = $current->copy()->subYears($i)->startOfYear();
                    $end = $start->copy()->endOfYear();
                    $label = $start->year;
                    break;
                default: // month
                    $start = $current->copy()->subMonths($i)->startOfMonth();
                    $end = $start->copy()->endOfMonth();
                    $label = $start->format('M Y');
                    break;
            }
            
            $periods[] = [
                'start' => $start,
                'end' => $end,
                'label' => $label,
            ];
        }
        
        return $periods;
    }

    protected function estimateLeadValue(Lead $lead): float
    {
        return match ($lead->service_interest) {
            'web-development' => 50000,
            'mobile-development' => 100000,
            'digital-marketing' => 25000,
            'graphic-design' => 15000,
            default => 30000,
        };
    }

    protected function calculateConfidenceInterval(array $data): array
    {
        $values = array_column($data, 'leads');
        $mean = array_sum($values) / count($values);
        $variance = array_sum(array_map(fn($x) => pow($x - $mean, 2), $values)) / count($values);
        $stdDev = sqrt($variance);
        
        return [
            'lower_bound' => max(0, $mean - (1.96 * $stdDev)),
            'upper_bound' => $mean + (1.96 * $stdDev),
            'confidence_level' => 95,
        ];
    }

    protected function analyzeTrend(array $data): string
    {
        $values = array_column($data, 'leads');
        $n = count($values);
        
        if ($n < 2) return 'insufficient_data';
        
        $firstHalf = array_slice($values, 0, intval($n / 2));
        $secondHalf = array_slice($values, intval($n / 2));
        
        $firstAvg = array_sum($firstHalf) / count($firstHalf);
        $secondAvg = array_sum($secondHalf) / count($secondHalf);
        
        $change = ($secondAvg - $firstAvg) / $firstAvg * 100;
        
        if ($change > 10) return 'strong_growth';
        if ($change > 5) return 'moderate_growth';
        if ($change > -5) return 'stable';
        if ($change > -10) return 'moderate_decline';
        return 'strong_decline';
    }

    protected function detectSeasonalPatterns(array $data): array
    {
        return [
            'has_seasonality' => false,
            'peak_periods' => [],
            'low_periods' => [],
            'seasonal_factor' => 1.0,
        ];
    }

    protected function generateLeadForecastRecommendations(array $forecast): array
    {
        $trend = $forecast['slope'] > 0 ? 'increasing' : ($forecast['slope'] < 0 ? 'decreasing' : 'stable');
        
        $recommendations = [];
        
        if ($trend === 'increasing') {
            $recommendations[] = 'Lead generation is trending upward. Consider scaling successful campaigns.';
            $recommendations[] = 'Prepare for increased lead volume by optimizing response processes.';
        } elseif ($trend === 'decreasing') {
            $recommendations[] = 'Lead generation is declining. Review and optimize marketing strategies.';
            $recommendations[] = 'Consider launching new campaigns or improving existing ones.';
        } else {
            $recommendations[] = 'Lead generation is stable. Focus on improving conversion rates.';
            $recommendations[] = 'Test new channels or optimize existing lead sources.';
        }
        
        return $recommendations;
    }

    protected function generateRevenueRecommendations(array $projection): array
    {
        $growthRate = $projection['avg_growth_rate'];
        
        $recommendations = [];
        
        if ($growthRate > 10) {
            $recommendations[] = 'Strong revenue growth projected. Consider expanding team capacity.';
            $recommendations[] = 'Invest in scaling successful revenue streams.';
        } elseif ($growthRate > 0) {
            $recommendations[] = 'Moderate growth expected. Focus on improving deal sizes.';
            $recommendations[] = 'Optimize sales processes to accelerate growth.';
        } else {
            $recommendations[] = 'Revenue growth is stagnant. Review pricing and service offerings.';
            $recommendations[] = 'Consider new revenue streams or market expansion.';
        }
        
        return $recommendations;
    }

    protected function generateGrowthActionPlan(array $prediction): array
    {
        return [
            [
                'action' => 'Optimize high-performing marketing channels',
                'priority' => 'high',
                'timeline' => 'immediate',
                'expected_impact' => 'Increase lead generation by 25%',
            ],
            [
                'action' => 'Implement lead scoring automation',
                'priority' => 'high',
                'timeline' => '2-3 weeks',
                'expected_impact' => 'Improve conversion rate by 15%',
            ],
        ];
    }

    // Additional helper methods
    protected function calculateRSquared(array $x, array $y, float $slope, float $intercept): float
    {
        $yMean = array_sum($y) / count($y);
        $ssRes = 0;
        $ssTot = 0;
        
        for ($i = 0; $i < count($x); $i++) {
            $predicted = $slope * $x[$i] + $intercept;
            $ssRes += pow($y[$i] - $predicted, 2);
            $ssTot += pow($y[$i] - $yMean, 2);
        }
        
        return $ssTot > 0 ? 1 - ($ssRes / $ssTot) : 0;
    }

    protected function calculatePredictionConfidence(int $periodsAhead): float
    {
        return max(50, 95 - ($periodsAhead * 10));
    }

    protected function getCurrentPeriodStart(string $period): Carbon
    {
        return match ($period) {
            'week' => now()->startOfWeek(),
            'quarter' => now()->startOfQuarter(),
            'year' => now()->startOfYear(),
            default => now()->startOfMonth(),
        };
    }

    protected function getPreviousPeriodStart(string $period): Carbon
    {
        return match ($period) {
            'week' => now()->subWeek()->startOfWeek(),
            'quarter' => now()->subQuarter()->startOfQuarter(),
            'year' => now()->subYear()->startOfYear(),
            default => now()->subMonth()->startOfMonth(),
        };
    }

    protected function calculatePercentageChange(int $previous, int $current): float
    {
        return $previous > 0 ? (($current - $previous) / $previous) * 100 : 0;
    }

    protected function calculateGrowthRate(array $data): float
    {
        $revenues = array_column($data, 'revenue');
        $n = count($revenues);
        
        if ($n < 2) return 0;
        
        $growthRates = [];
        for ($i = 1; $i < $n; $i++) {
            if ($revenues[$i - 1] > 0) {
                $growthRates[] = ($revenues[$i] - $revenues[$i - 1]) / $revenues[$i - 1];
            }
        }
        
        return count($growthRates) > 0 ? (array_sum($growthRates) / count($growthRates)) * 100 : 0;
    }

    protected function projectRevenueByService(string $period): array
    {
        return [
            'web-development' => ['projected' => 300000, 'growth' => 15],
            'mobile-development' => ['projected' => 500000, 'growth' => 25],
            'digital-marketing' => ['projected' => 150000, 'growth' => 10],
            'graphic-design' => ['projected' => 100000, 'growth' => 8],
        ];
    }

    protected function calculateBreakEvenAnalysis(): array
    {
        return [
            'monthly_fixed_costs' => 200000,
            'variable_cost_per_lead' => 500,
            'avg_revenue_per_conversion' => 50000,
            'break_even_leads' => 5,
            'break_even_conversions' => 4,
        ];
    }

    protected function calculateConversionRateChange(string $period): float
    {
        return 2.5; // Sample value
    }

    protected function calculateCAC(string $period): float
    {
        return 1500; // Sample value
    }

    protected function calculateCLV(): float
    {
        return 75000; // Sample value
    }
}
