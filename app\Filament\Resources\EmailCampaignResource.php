<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EmailCampaignResource\Pages;
use App\Models\EmailCampaign;
use App\Models\EmailTemplate;
use App\Models\ContactList;
use App\Models\Contact;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;

class EmailCampaignResource extends Resource
{
    protected static ?string $model = EmailCampaign::class;
    protected static ?string $navigationIcon = 'heroicon-o-envelope';
    protected static ?string $navigationGroup = 'Email Marketing';
    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Campaign Details')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\Select::make('template_id')
                            ->label('Email Template')
                            ->relationship('template', 'name')
                            ->searchable()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('subject')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\RichEditor::make('content')
                                    ->required(),
                            ])
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state) {
                                    $template = EmailTemplate::find($state);
                                    if ($template) {
                                        $set('subject', $template->subject);
                                        $set('content', $template->content);
                                    }
                                }
                            }),
                        Forms\Components\TextInput::make('subject')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Use {{first_name}}, {{company}}, etc. for personalization'),
                        Forms\Components\Select::make('status')
                            ->options([
                                'draft' => 'Draft',
                                'scheduled' => 'Scheduled',
                                'sending' => 'Sending',
                                'sent' => 'Sent',
                                'paused' => 'Paused',
                                'cancelled' => 'Cancelled',
                            ])
                            ->required(),
                    ])->columns(2),

                Forms\Components\Section::make('Content')
                    ->schema([
                        Forms\Components\RichEditor::make('content')
                            ->required()
                            ->columnSpanFull(),
                        Forms\Components\FileUpload::make('attachments')
                            ->multiple()
                            ->directory('email-attachments')
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Targeting')
                    ->schema([
                        Forms\Components\TagsInput::make('target_lists')
                            ->label('Target Contact Lists')
                            ->placeholder('Add contact list names or IDs')
                            ->helperText('Specify which contact lists to target for this campaign'),
                        Forms\Components\TagsInput::make('tags')
                            ->placeholder('Add targeting tags'),
                        Forms\Components\TextInput::make('recipient_count')
                            ->label('Estimated Recipients')
                            ->numeric()
                            ->disabled()
                            ->helperText('Will be calculated based on selected lists'),
                    ])->columns(2),

                Forms\Components\Section::make('Scheduling')
                    ->schema([
                        Forms\Components\DateTimePicker::make('scheduled_at')
                            ->label('Schedule Send Time'),
                        Forms\Components\Select::make('timezone')
                            ->options([
                                'Asia/Kolkata' => 'India (IST)',
                                'UTC' => 'UTC',
                                'America/New_York' => 'Eastern Time',
                                'Europe/London' => 'London Time',
                            ])
                            ->default('Asia/Kolkata'),
                    ])->columns(2),

                Forms\Components\Section::make('A/B Testing')
                    ->schema([
                        Forms\Components\Toggle::make('is_ab_test')
                            ->label('Enable A/B Testing')
                            ->reactive(),
                        Forms\Components\TextInput::make('ab_test_percentage')
                            ->label('Test Percentage')
                            ->numeric()
                            ->minValue(10)
                            ->maxValue(50)
                            ->default(20)
                            ->visible(fn ($get) => $get('is_ab_test')),
                        Forms\Components\TextInput::make('ab_subject_variant')
                            ->label('Subject Line Variant')
                            ->visible(fn ($get) => $get('is_ab_test')),
                    ])->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('subject')
                    ->searchable()
                    ->limit(50),
                Tables\Columns\BadgeColumn::make('type')
                    ->colors([
                        'primary' => 'newsletter',
                        'success' => 'promotional',
                        'warning' => 'transactional',
                        'info' => 'automated',
                    ]),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'secondary' => 'draft',
                        'warning' => 'scheduled',
                        'info' => 'sending',
                        'success' => 'sent',
                        'danger' => 'paused',
                        'gray' => 'cancelled',
                    ]),
                Tables\Columns\TextColumn::make('recipients_count')
                    ->label('Recipients')
                    ->numeric(),
                Tables\Columns\TextColumn::make('open_rate')
                    ->label('Open Rate')
                    ->formatStateUsing(fn ($state) => $state ? number_format($state, 2) . '%' : 'N/A'),
                Tables\Columns\TextColumn::make('click_rate')
                    ->label('Click Rate')
                    ->formatStateUsing(fn ($state) => $state ? number_format($state, 2) . '%' : 'N/A'),
                Tables\Columns\TextColumn::make('scheduled_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'newsletter' => 'Newsletter',
                        'promotional' => 'Promotional',
                        'transactional' => 'Transactional',
                        'automated' => 'Automated',
                    ]),
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'draft' => 'Draft',
                        'scheduled' => 'Scheduled',
                        'sending' => 'Sending',
                        'sent' => 'Sent',
                        'paused' => 'Paused',
                        'cancelled' => 'Cancelled',
                    ]),
                Tables\Filters\Filter::make('scheduled')
                    ->query(fn (Builder $query): Builder => $query->whereNotNull('scheduled_at')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->action(function (EmailCampaign $record) {
                        $newCampaign = $record->replicate();
                        $newCampaign->name = $record->name . ' (Copy)';
                        $newCampaign->status = 'draft';
                        $newCampaign->scheduled_at = null;
                        $newCampaign->save();
                        
                        return redirect()->route('filament.admin.resources.email-campaigns.edit', $newCampaign);
                    }),
                Tables\Actions\Action::make('send_test')
                    ->icon('heroicon-o-paper-airplane')
                    ->form([
                        Forms\Components\TextInput::make('test_email')
                            ->email()
                            ->required()
                            ->label('Test Email Address'),
                    ])
                    ->action(function (EmailCampaign $record, array $data) {
                        // Send test email logic here
                        \Filament\Notifications\Notification::make()
                            ->title('Test email sent successfully')
                            ->success()
                            ->send();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('pause')
                        ->label('Pause Campaigns')
                        ->icon('heroicon-o-pause')
                        ->color('warning')
                        ->action(function (Collection $records): void {
                            $records->each->update(['status' => 'paused']);
                            Notification::make()
                                ->title('Campaigns paused successfully')
                                ->success()
                                ->send();
                        })
                        ->requiresConfirmation(),
                    BulkAction::make('resume')
                        ->label('Resume Campaigns')
                        ->icon('heroicon-o-play')
                        ->color('success')
                        ->action(function (Collection $records): void {
                            $records->each->update(['status' => 'scheduled']);
                            Notification::make()
                                ->title('Campaigns resumed successfully')
                                ->success()
                                ->send();
                        }),
                    BulkAction::make('duplicate')
                        ->label('Duplicate Campaigns')
                        ->icon('heroicon-o-document-duplicate')
                        ->color('gray')
                        ->action(function (Collection $records): void {
                            foreach ($records as $record) {
                                $duplicate = $record->replicate();
                                $duplicate->name = $record->name . ' (Copy)';
                                $duplicate->status = 'draft';
                                $duplicate->scheduled_at = null;
                                $duplicate->sent_at = null;
                                $duplicate->save();
                            }
                            Notification::make()
                                ->title('Campaigns duplicated successfully')
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Campaign Overview')
                    ->schema([
                        Infolists\Components\TextEntry::make('name'),
                        Infolists\Components\TextEntry::make('subject'),
                        Infolists\Components\TextEntry::make('type')
                            ->badge(),
                        Infolists\Components\TextEntry::make('status')
                            ->badge(),
                        Infolists\Components\TextEntry::make('scheduled_at')
                            ->dateTime(),
                    ])->columns(3),

                Infolists\Components\Section::make('Performance Metrics')
                    ->schema([
                        Infolists\Components\TextEntry::make('recipients_count')
                            ->label('Total Recipients'),
                        Infolists\Components\TextEntry::make('delivered_count')
                            ->label('Delivered'),
                        Infolists\Components\TextEntry::make('opened_count')
                            ->label('Opened'),
                        Infolists\Components\TextEntry::make('clicked_count')
                            ->label('Clicked'),
                        Infolists\Components\TextEntry::make('open_rate')
                            ->label('Open Rate')
                            ->formatStateUsing(fn ($state) => $state ? number_format($state, 2) . '%' : 'N/A'),
                        Infolists\Components\TextEntry::make('click_rate')
                            ->label('Click Rate')
                            ->formatStateUsing(fn ($state) => $state ? number_format($state, 2) . '%' : 'N/A'),
                    ])->columns(3),

                Infolists\Components\Section::make('Content')
                    ->schema([
                        Infolists\Components\TextEntry::make('content')
                            ->html()
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmailCampaigns::route('/'),
            'create' => Pages\CreateEmailCampaign::route('/create'),
            'view' => Pages\ViewEmailCampaign::route('/{record}'),
            'edit' => Pages\EditEmailCampaign::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('status', 'sending')->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'info';
    }
}
