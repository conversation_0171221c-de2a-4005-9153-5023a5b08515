<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;

class AnalyticsDashboard extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Analytics Dashboard';

    protected static ?string $title = 'Advanced Analytics';

    protected static ?string $navigationGroup = 'Analytics & Reports';

    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.analytics-dashboard';

    protected function getHeaderWidgets(): array
    {
        return [
            \App\Filament\Widgets\AdvancedAnalyticsWidget::class,
            \App\Filament\Widgets\MassiveEmailAnalyticsWidget::class,
        ];
    }
}
