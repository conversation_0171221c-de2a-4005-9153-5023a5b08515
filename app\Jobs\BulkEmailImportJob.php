<?php

namespace App\Jobs;

use App\Models\Contact;
use App\Models\ContactList;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use League\Csv\Reader;
use League\Csv\Statement;

class BulkEmailImportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 3600; // 1 hour timeout
    public $tries = 3;

    protected $filePath;
    protected $contactListId;
    protected $chunkSize;
    protected $skipDuplicates;
    protected $validateEmails;

    public function __construct(
        string $filePath,
        int $contactListId,
        int $chunkSize = 1000,
        bool $skipDuplicates = true,
        bool $validateEmails = true
    ) {
        $this->filePath = $filePath;
        $this->contactListId = $contactListId;
        $this->chunkSize = $chunkSize;
        $this->skipDuplicates = $skipDuplicates;
        $this->validateEmails = $validateEmails;
    }

    public function handle(): void
    {
        Log::info("Starting bulk email import", [
            'file' => $this->filePath,
            'list_id' => $this->contactListId,
            'chunk_size' => $this->chunkSize
        ]);

        $contactList = ContactList::findOrFail($this->contactListId);
        
        try {
            $csv = Reader::createFromPath(storage_path('app/' . $this->filePath), 'r');
            $csv->setHeaderOffset(0);
            
            $header = $csv->getHeader();
            $this->validateCsvHeader($header);
            
            $totalRecords = iterator_count($csv->getRecords());
            Log::info("Total records to import: {$totalRecords}");
            
            $stmt = Statement::create();
            $chunks = $stmt->process($csv);
            
            $processedCount = 0;
            $errorCount = 0;
            $duplicateCount = 0;
            
            foreach ($chunks as $offset => $record) {
                if ($offset % $this->chunkSize === 0) {
                    Log::info("Processing chunk starting at offset: {$offset}");
                }
                
                try {
                    $result = $this->processRecord($record, $contactList);
                    
                    switch ($result) {
                        case 'processed':
                            $processedCount++;
                            break;
                        case 'duplicate':
                            $duplicateCount++;
                            break;
                        case 'error':
                            $errorCount++;
                            break;
                    }
                    
                } catch (\Exception $e) {
                    Log::error("Error processing record", [
                        'record' => $record,
                        'error' => $e->getMessage()
                    ]);
                    $errorCount++;
                }
                
                // Memory management - clear cache every 1000 records
                if ($processedCount % 1000 === 0) {
                    gc_collect_cycles();
                }
            }
            
            Log::info("Bulk import completed", [
                'processed' => $processedCount,
                'duplicates' => $duplicateCount,
                'errors' => $errorCount,
                'total' => $totalRecords
            ]);
            
        } catch (\Exception $e) {
            Log::error("Bulk import failed", [
                'file' => $this->filePath,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function validateCsvHeader(array $header): void
    {
        $requiredColumns = ['email'];
        $optionalColumns = [
            'first_name', 'last_name', 'phone', 'company', 'industry',
            'city', 'state', 'country', 'source', 'tags'
        ];
        
        $missingRequired = array_diff($requiredColumns, $header);
        if (!empty($missingRequired)) {
            throw new \InvalidArgumentException(
                'Missing required columns: ' . implode(', ', $missingRequired)
            );
        }
    }

    private function processRecord(array $record, ContactList $contactList): string
    {
        // Validate email
        if ($this->validateEmails && !filter_var($record['email'], FILTER_VALIDATE_EMAIL)) {
            return 'error';
        }
        
        // Check for duplicates
        if ($this->skipDuplicates && Contact::where('email', $record['email'])->exists()) {
            return 'duplicate';
        }
        
        // Prepare contact data
        $contactData = [
            'email' => $record['email'],
            'first_name' => $record['first_name'] ?? '',
            'last_name' => $record['last_name'] ?? '',
            'phone' => $record['phone'] ?? null,
            'company' => $record['company'] ?? null,
            'industry' => $record['industry'] ?? null,
            'city' => $record['city'] ?? null,
            'state' => $record['state'] ?? null,
            'country' => $record['country'] ?? 'India',
            'source' => $record['source'] ?? 'bulk_import',
            'engagement_score' => rand(30, 100),
            'is_subscribed' => true,
            'is_active' => true,
        ];
        
        // Handle tags
        if (!empty($record['tags'])) {
            $tags = is_string($record['tags']) 
                ? explode(',', $record['tags']) 
                : $record['tags'];
            $contactData['tags'] = json_encode(array_map('trim', $tags));
        }
        
        // Create or update contact
        $contact = Contact::updateOrCreate(
            ['email' => $record['email']],
            $contactData
        );
        
        // Add to contact list
        $contactList->addContact($contact);
        
        return 'processed';
    }

    public function failed(\Throwable $exception): void
    {
        Log::error("Bulk import job failed", [
            'file' => $this->filePath,
            'list_id' => $this->contactListId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}
