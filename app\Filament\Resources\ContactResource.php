<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContactResource\Pages;
use App\Models\Contact;
use App\Models\ContactList;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;

class ContactResource extends Resource
{
    protected static ?string $model = Contact::class;
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationGroup = 'Contact Management';
    protected static ?string $navigationLabel = 'Contacts';
    protected static ?int $navigationSort = 1;
    protected static ?string $recordTitleAttribute = 'email';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Contact Information')
                    ->schema([
                        Forms\Components\TextInput::make('first_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('last_name')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Forms\Components\TextInput::make('phone')
                            ->tel()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('company')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('job_title')
                            ->maxLength(255),
                    ])->columns(2),

                Forms\Components\Section::make('Location & Demographics')
                    ->schema([
                        Forms\Components\TextInput::make('city')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('state')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('country')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('postal_code')
                            ->maxLength(20),
                        Forms\Components\Select::make('timezone')
                            ->options([
                                'Asia/Kolkata' => 'Asia/Kolkata (IST)',
                                'America/New_York' => 'America/New_York (EST)',
                                'Europe/London' => 'Europe/London (GMT)',
                                'Asia/Dubai' => 'Asia/Dubai (GST)',
                                'Australia/Sydney' => 'Australia/Sydney (AEST)',
                            ])
                            ->default('Asia/Kolkata'),
                        Forms\Components\Select::make('language')
                            ->options([
                                'en' => 'English',
                                'hi' => 'Hindi',
                                'ta' => 'Tamil',
                                'te' => 'Telugu',
                                'kn' => 'Kannada',
                            ])
                            ->default('en'),
                    ])->columns(3),

                Forms\Components\Section::make('Marketing Preferences')
                    ->schema([
                        Forms\Components\Select::make('status')
                            ->options([
                                'subscribed' => 'Subscribed',
                                'unsubscribed' => 'Unsubscribed',
                                'bounced' => 'Bounced',
                                'complained' => 'Complained',
                                'pending' => 'Pending Confirmation',
                            ])
                            ->default('subscribed')
                            ->required(),
                        Forms\Components\Select::make('source')
                            ->options([
                                'website' => 'Website Form',
                                'import' => 'Manual Import',
                                'api' => 'API Integration',
                                'social' => 'Social Media',
                                'referral' => 'Referral',
                                'event' => 'Event/Webinar',
                                'purchase' => 'Purchase',
                            ])
                            ->default('website'),
                        Forms\Components\TagsInput::make('tags')
                            ->placeholder('Add tags...'),
                        Forms\Components\Select::make('contact_lists')
                            ->relationship('contactLists', 'name')
                            ->multiple()
                            ->preload()
                            ->createOptionForm([
                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\Textarea::make('description')
                                    ->maxLength(500),
                            ]),
                    ])->columns(2),

                Forms\Components\Section::make('Engagement Data')
                    ->schema([
                        Forms\Components\TextInput::make('engagement_score')
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->default(50)
                            ->suffix('%'),
                        Forms\Components\DateTimePicker::make('last_opened_at')
                            ->displayFormat('M j, Y g:i A'),
                        Forms\Components\DateTimePicker::make('last_clicked_at')
                            ->displayFormat('M j, Y g:i A'),
                        Forms\Components\DateTimePicker::make('subscribed_at')
                            ->displayFormat('M j, Y g:i A')
                            ->default(now()),
                    ])->columns(2),

                Forms\Components\Section::make('Custom Fields')
                    ->schema([
                        Forms\Components\KeyValue::make('custom_fields')
                            ->keyLabel('Field Name')
                            ->valueLabel('Value')
                            ->addActionLabel('Add Custom Field'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('first_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->icon('heroicon-m-envelope'),
                Tables\Columns\TextColumn::make('phone')
                    ->searchable()
                    ->toggleable()
                    ->icon('heroicon-m-phone'),
                Tables\Columns\TextColumn::make('company')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->colors([
                        'success' => 'subscribed',
                        'danger' => 'unsubscribed',
                        'warning' => 'bounced',
                        'secondary' => 'complained',
                        'primary' => 'pending',
                    ]),
                Tables\Columns\TextColumn::make('engagement_score')
                    ->sortable()
                    ->badge()
                    ->color(fn (string $state): string => match (true) {
                        $state >= 80 => 'success',
                        $state >= 60 => 'warning',
                        $state >= 40 => 'primary',
                        default => 'secondary',
                    })
                    ->suffix('%'),
                Tables\Columns\TextColumn::make('source')
                    ->badge()
                    ->toggleable(),
                Tables\Columns\TagsColumn::make('tags')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('contactLists.name')
                    ->label('Lists')
                    ->badge()
                    ->separator(',')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('last_opened_at')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'subscribed' => 'Subscribed',
                        'unsubscribed' => 'Unsubscribed',
                        'bounced' => 'Bounced',
                        'complained' => 'Complained',
                        'pending' => 'Pending',
                    ]),
                Tables\Filters\SelectFilter::make('source')
                    ->options([
                        'website' => 'Website',
                        'import' => 'Import',
                        'api' => 'API',
                        'social' => 'Social Media',
                        'referral' => 'Referral',
                        'event' => 'Event',
                        'purchase' => 'Purchase',
                    ]),
                Tables\Filters\Filter::make('high_engagement')
                    ->query(fn (Builder $query): Builder => $query->where('engagement_score', '>=', 70))
                    ->label('High Engagement (70%+)'),
                Tables\Filters\Filter::make('recent_activity')
                    ->query(fn (Builder $query): Builder => $query->where('last_opened_at', '>=', now()->subDays(30)))
                    ->label('Active in Last 30 Days'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('send_email')
                    ->icon('heroicon-o-envelope')
                    ->color('primary')
                    ->url(fn (Contact $record): string => route('filament.admin.resources.email-campaigns.create', ['contact' => $record->id])),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('add_to_list')
                        ->label('Add to List')
                        ->icon('heroicon-o-plus')
                        ->form([
                            Forms\Components\Select::make('contact_list_id')
                                ->label('Contact List')
                                ->options(ContactList::pluck('name', 'id'))
                                ->required(),
                        ])
                        ->action(function (Collection $records, array $data): void {
                            $list = ContactList::find($data['contact_list_id']);
                            foreach ($records as $record) {
                                $record->contactLists()->syncWithoutDetaching([$list->id]);
                            }
                            Notification::make()
                                ->title('Contacts added to list successfully')
                                ->success()
                                ->send();
                        }),
                    BulkAction::make('update_status')
                        ->label('Update Status')
                        ->icon('heroicon-o-pencil')
                        ->form([
                            Forms\Components\Select::make('status')
                                ->options([
                                    'subscribed' => 'Subscribed',
                                    'unsubscribed' => 'Unsubscribed',
                                    'bounced' => 'Bounced',
                                    'complained' => 'Complained',
                                ])
                                ->required(),
                        ])
                        ->action(function (Collection $records, array $data): void {
                            $records->each->update(['status' => $data['status']]);
                            Notification::make()
                                ->title('Contact status updated successfully')
                                ->success()
                                ->send();
                        }),
                    BulkAction::make('export')
                        ->label('Export Selected')
                        ->icon('heroicon-o-arrow-down-tray')
                        ->action(function (Collection $records): void {
                            // Export functionality will be implemented
                            Notification::make()
                                ->title('Export started')
                                ->body('You will receive an email when the export is ready.')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContacts::route('/'),
            'create' => Pages\CreateContact::route('/create'),
            'view' => Pages\ViewContact::route('/{record}'),
            'edit' => Pages\EditContact::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_subscribed', true)->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }
}
