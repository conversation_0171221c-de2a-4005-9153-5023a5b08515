<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use App\Events\MessageSent;

class ChatMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'chat_session_id',
        'sender_type',
        'sender_id',
        'sender_name',
        'message_type',
        'message',
        'metadata',
        'is_read',
        'read_at',
        'is_internal',
        'message_id',
        'sent_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_read' => 'boolean',
        'is_internal' => 'boolean',
        'read_at' => 'datetime',
        'sent_at' => 'datetime',
    ];

    // Relationships
    public function chatSession(): BelongsTo
    {
        return $this->belongsTo(ChatSession::class);
    }

    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    // Scopes
    public function scopeFromVisitor($query)
    {
        return $query->where('sender_type', 'visitor');
    }

    public function scopeFromAgent($query)
    {
        return $query->where('sender_type', 'agent');
    }

    public function scopeFromBot($query)
    {
        return $query->where('sender_type', 'bot');
    }

    public function scopeSystemMessages($query)
    {
        return $query->where('sender_type', 'system');
    }

    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopePublic($query)
    {
        return $query->where('is_internal', false);
    }

    public function scopeInternal($query)
    {
        return $query->where('is_internal', true);
    }

    public function scopeTextMessages($query)
    {
        return $query->where('message_type', 'text');
    }

    // Boot method to generate message_id
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($message) {
            if (empty($message->message_id)) {
                $message->message_id = 'msg_' . Str::random(12) . '_' . time();
            }

            // Update session activity and message count
            if ($message->chatSession) {
                $message->chatSession->incrementMessageCount();
            }
        });

        static::created(function ($message) {
            // Broadcast the message after it's created
            broadcast(new MessageSent($message))->toOthers();
        });
    }

    // Business Logic Methods
    public function isFromVisitor(): bool
    {
        return $this->sender_type === 'visitor';
    }

    public function isFromAgent(): bool
    {
        return $this->sender_type === 'agent';
    }

    public function isFromBot(): bool
    {
        return $this->sender_type === 'bot';
    }

    public function isSystemMessage(): bool
    {
        return $this->sender_type === 'system';
    }

    public function markAsRead(): void
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }
    }

    public function getFormattedTimeAttribute(): string
    {
        return $this->sent_at->format('H:i');
    }

    public function getFormattedDateAttribute(): string
    {
        return $this->sent_at->format('M j, Y');
    }

    public function getSenderDisplayNameAttribute(): string
    {
        if ($this->sender_name) {
            return $this->sender_name;
        }

        return match ($this->sender_type) {
            'visitor' => 'Visitor',
            'agent' => $this->sender?->name ?? 'Agent',
            'bot' => 'Bhavitech Bot',
            'system' => 'System',
            default => 'Unknown',
        };
    }

    public function getMessagePreviewAttribute(): string
    {
        return Str::limit($this->message, 50);
    }

    public function hasAttachment(): bool
    {
        return in_array($this->message_type, ['file', 'image']) && !empty($this->metadata);
    }

    public function getAttachmentUrl(): ?string
    {
        if (!$this->hasAttachment()) {
            return null;
        }

        return $this->metadata['url'] ?? null;
    }
}
