<?php

namespace App\Console\Commands;

use App\Models\Lead;
use App\Models\EmailCampaign;
use App\Models\WhatsAppCampaign;
use App\Models\SocialMediaPost;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class SystemStatusReport extends Command
{
    protected $signature = 'bhavitech:status-report';
    protected $description = 'Generate comprehensive system status report';

    public function handle()
    {
        $this->info('🚀 Bhavitech Enterprise CRM & Marketing Platform - System Status Report');
        $this->info('═══════════════════════════════════════════════════════════════════════');
        $this->newLine();

        // Database Status
        $this->checkDatabaseStatus();
        $this->newLine();

        // Models Status
        $this->checkModelsStatus();
        $this->newLine();

        // Services Status
        $this->checkServicesStatus();
        $this->newLine();

        // Data Summary
        $this->showDataSummary();
        $this->newLine();

        // System Health
        $this->checkSystemHealth();
        $this->newLine();

        $this->info('✅ System Status Report Complete!');
        return Command::SUCCESS;
    }

    protected function checkDatabaseStatus(): void
    {
        $this->info('📊 Database Status');
        $this->line('─────────────────');

        try {
            DB::connection()->getPdo();
            $this->line('✅ Database Connection: OK');

            // Check critical tables
            $tables = [
                'users' => 'User Management',
                'leads' => 'Lead Management',
                'email_campaigns' => 'Email Marketing',
                'whatsapp_campaigns' => 'WhatsApp Marketing',
                'social_media_posts' => 'Social Media',
                'metal_rates' => 'Metal Rates API',
                'projects' => 'Customer Portal',
                'analytics_events' => 'Analytics System',
            ];

            foreach ($tables as $table => $description) {
                if (Schema::hasTable($table)) {
                    $count = DB::table($table)->count();
                    $this->line("✅ {$description}: {$count} records");
                } else {
                    $this->line("❌ {$description}: Table missing");
                }
            }

        } catch (\Exception $e) {
            $this->line('❌ Database Connection: FAILED - ' . $e->getMessage());
        }
    }

    protected function checkModelsStatus(): void
    {
        $this->info('🏗️ Models Status');
        $this->line('─────────────────');

        $models = [
            'App\Models\Lead' => 'Lead Model',
            'App\Models\User' => 'User Model',
            'App\Models\EmailCampaign' => 'Email Campaign Model',
            'App\Models\WhatsAppCampaign' => 'WhatsApp Campaign Model',
            'App\Models\SocialMediaPost' => 'Social Media Post Model',
            'App\Models\MetalRate' => 'Metal Rate Model',
            'App\Models\Project' => 'Project Model',
            'App\Models\AnalyticsEvent' => 'Analytics Event Model',
        ];

        foreach ($models as $class => $description) {
            try {
                if (class_exists($class)) {
                    $model = new $class();
                    $this->line("✅ {$description}: Available");
                } else {
                    $this->line("❌ {$description}: Not found");
                }
            } catch (\Exception $e) {
                $this->line("⚠️ {$description}: Error - " . $e->getMessage());
            }
        }
    }

    protected function checkServicesStatus(): void
    {
        $this->info('⚙️ Services Status');
        $this->line('─────────────────');

        $services = [
            'App\Services\LeadScoringService' => 'Lead Scoring Service',
            'App\Services\EmailMarketingService' => 'Email Marketing Service',
            'App\Services\WhatsAppMarketingService' => 'WhatsApp Marketing Service',
            'App\Services\SocialMediaService' => 'Social Media Service',
            'App\Services\AnalyticsService' => 'Analytics Service',
            'App\Services\CustomerPortalService' => 'Customer Portal Service',
            'App\Services\MetalRatesService' => 'Metal Rates Service',
        ];

        foreach ($services as $class => $description) {
            try {
                if (class_exists($class)) {
                    $service = app($class);
                    $this->line("✅ {$description}: Available");
                } else {
                    $this->line("❌ {$description}: Not found");
                }
            } catch (\Exception $e) {
                $this->line("⚠️ {$description}: Error - " . $e->getMessage());
            }
        }
    }

    protected function showDataSummary(): void
    {
        $this->info('📈 Data Summary');
        $this->line('─────────────────');

        try {
            // Lead Statistics
            $totalLeads = Lead::count();
            $newLeads = Lead::where('status', 'new')->count();
            $qualifiedLeads = Lead::where('status', 'qualified')->count();
            $convertedLeads = Lead::where('status', 'converted')->count();
            $avgScore = Lead::avg('score') ?? 0;

            $this->line("📋 Leads:");
            $this->line("   Total: {$totalLeads}");
            $this->line("   New: {$newLeads}");
            $this->line("   Qualified: {$qualifiedLeads}");
            $this->line("   Converted: {$convertedLeads}");
            $this->line("   Avg Score: " . number_format($avgScore, 1));

            // Campaign Statistics
            if (Schema::hasTable('email_campaigns')) {
                $emailCampaigns = EmailCampaign::count();
                $activeEmailCampaigns = EmailCampaign::whereIn('status', ['sending', 'scheduled'])->count();
                $this->line("📧 Email Campaigns: {$emailCampaigns} total, {$activeEmailCampaigns} active");
            }

            if (Schema::hasTable('whatsapp_campaigns')) {
                $whatsappCampaigns = WhatsAppCampaign::count();
                $activeWhatsappCampaigns = WhatsAppCampaign::whereIn('status', ['sending', 'scheduled'])->count();
                $this->line("📱 WhatsApp Campaigns: {$whatsappCampaigns} total, {$activeWhatsappCampaigns} active");
            }

            if (Schema::hasTable('social_media_posts')) {
                $socialPosts = SocialMediaPost::count();
                $publishedPosts = SocialMediaPost::where('status', 'published')->count();
                $this->line("📱 Social Media Posts: {$socialPosts} total, {$publishedPosts} published");
            }

            // User Statistics
            $totalUsers = User::count();
            $adminUsers = User::where('role', 'admin')->count();
            $customerUsers = User::where('role', 'customer')->count();

            $this->line("👥 Users:");
            $this->line("   Total: {$totalUsers}");
            $this->line("   Admins: {$adminUsers}");
            $this->line("   Customers: {$customerUsers}");

        } catch (\Exception $e) {
            $this->line("❌ Error generating data summary: " . $e->getMessage());
        }
    }

    protected function checkSystemHealth(): void
    {
        $this->info('🏥 System Health');
        $this->line('─────────────────');

        // Memory Usage
        $memoryUsage = memory_get_usage(true) / 1024 / 1024;
        $memoryLimit = ini_get('memory_limit');
        $this->line("💾 Memory Usage: " . number_format($memoryUsage, 2) . " MB (Limit: {$memoryLimit})");

        // Database Performance
        $start = microtime(true);
        try {
            Lead::limit(10)->get();
            $queryTime = (microtime(true) - $start) * 1000;
            $this->line("⚡ Database Query Time: " . number_format($queryTime, 2) . " ms");
        } catch (\Exception $e) {
            $this->line("❌ Database Query: FAILED");
        }

        // Disk Space (if available)
        if (function_exists('disk_free_space')) {
            $freeBytes = disk_free_space('.');
            $freeGB = $freeBytes / 1024 / 1024 / 1024;
            $this->line("💽 Free Disk Space: " . number_format($freeGB, 2) . " GB");
        }

        // PHP Version
        $phpVersion = PHP_VERSION;
        $this->line("🐘 PHP Version: {$phpVersion}");

        // Laravel Version
        $laravelVersion = app()->version();
        $this->line("🔧 Laravel Version: {$laravelVersion}");

        // Environment
        $environment = app()->environment();
        $this->line("🌍 Environment: {$environment}");

        // Cache Status
        try {
            cache()->put('health_check', 'ok', 60);
            $cacheTest = cache()->get('health_check');
            if ($cacheTest === 'ok') {
                $this->line("🗄️ Cache: OK");
            } else {
                $this->line("⚠️ Cache: Not working properly");
            }
        } catch (\Exception $e) {
            $this->line("❌ Cache: FAILED");
        }

        // Queue Status
        try {
            $queueSize = DB::table('jobs')->count();
            $failedJobs = DB::table('failed_jobs')->count();
            $this->line("📋 Queue: {$queueSize} pending, {$failedJobs} failed");
        } catch (\Exception $e) {
            $this->line("⚠️ Queue: Cannot check status");
        }
    }
}
