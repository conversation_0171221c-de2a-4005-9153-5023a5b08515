<?php

namespace App\Filament\Widgets;

use App\Models\Lead;
use App\Models\EmailCampaign;
use App\Models\WhatsAppCampaign;
use App\Models\SocialMediaPost;
use App\Models\User;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class AdvancedAnalyticsWidget extends BaseWidget
{
    protected static ?int $sort = 1;
    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        // Get date ranges
        $today = now()->startOfDay();
        $yesterday = now()->subDay()->startOfDay();
        $thisMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        // Lead Analytics
        $totalLeads = Lead::count();
        $leadsToday = Lead::whereDate('created_at', $today)->count();
        $leadsYesterday = Lead::whereDate('created_at', $yesterday)->count();
        $leadsThisMonth = Lead::where('created_at', '>=', $thisMonth)->count();
        $leadsLastMonth = Lead::whereBetween('created_at', [$lastMonth, $thisMonth])->count();

        $leadsTodayChange = $leadsYesterday > 0 ? (($leadsToday - $leadsYesterday) / $leadsYesterday) * 100 : 0;
        $leadsMonthChange = $leadsLastMonth > 0 ? (($leadsThisMonth - $leadsLastMonth) / $leadsLastMonth) * 100 : 0;

        // Conversion Analytics
        $convertedLeads = Lead::where('status', 'converted')->count();
        $conversionRate = $totalLeads > 0 ? ($convertedLeads / $totalLeads) * 100 : 0;
        $qualifiedLeads = Lead::where('status', 'qualified')->count();
        $qualificationRate = $totalLeads > 0 ? ($qualifiedLeads / $totalLeads) * 100 : 0;

        // Lead Score Analytics
        $avgLeadScore = Lead::avg('score') ?? 0;
        $highQualityLeads = Lead::where('score', '>=', 80)->count();
        $mediumQualityLeads = Lead::whereBetween('score', [60, 79])->count();
        $lowQualityLeads = Lead::where('score', '<', 60)->count();

        // Email Campaign Analytics
        $totalEmailCampaigns = EmailCampaign::count();
        $activeEmailCampaigns = EmailCampaign::whereIn('status', ['sending', 'scheduled'])->count();
        $avgOpenRate = EmailCampaign::where('status', 'sent')->avg('open_rate') ?? 0;
        $avgClickRate = EmailCampaign::where('status', 'sent')->avg('click_rate') ?? 0;

        // WhatsApp Campaign Analytics
        $totalWhatsAppCampaigns = WhatsAppCampaign::count();
        $activeWhatsAppCampaigns = WhatsAppCampaign::whereIn('status', ['sending', 'scheduled'])->count();
        $avgDeliveryRate = WhatsAppCampaign::where('status', 'sent')->avg('delivery_rate') ?? 0;
        $avgReadRate = WhatsAppCampaign::where('status', 'sent')->avg('read_rate') ?? 0;

        // Social Media Analytics
        $totalSocialPosts = SocialMediaPost::count();
        $publishedPosts = SocialMediaPost::where('status', 'published')->count();
        $scheduledPosts = SocialMediaPost::where('status', 'scheduled')->count();

        // Calculate average engagement from analytics JSON data
        $publishedPostsWithAnalytics = SocialMediaPost::where('status', 'published')
            ->whereNotNull('analytics')
            ->get();

        $totalEngagement = $publishedPostsWithAnalytics->sum(function ($post) {
            return $post->getTotalEngagement();
        });

        $avgEngagement = $publishedPostsWithAnalytics->count() > 0
            ? $totalEngagement / $publishedPostsWithAnalytics->count()
            : 0;

        // Customer Analytics
        $totalCustomers = User::where('role', 'customer')->count();
        $newCustomersThisMonth = User::where('role', 'customer')
            ->where('created_at', '>=', $thisMonth)
            ->count();

        return [
            // Lead Statistics
            Stat::make('Total Leads', number_format($totalLeads))
                ->description($leadsTodayChange >= 0 ? "+{$leadsToday} today" : "{$leadsToday} today")
                ->descriptionIcon($leadsTodayChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($leadsTodayChange >= 0 ? 'success' : 'danger')
                ->chart($this->getLeadChart()),

            Stat::make('Conversion Rate', number_format($conversionRate, 1) . '%')
                ->description("{$convertedLeads} converted leads")
                ->descriptionIcon('heroicon-m-trophy')
                ->color($conversionRate >= 20 ? 'success' : ($conversionRate >= 10 ? 'warning' : 'danger')),

            Stat::make('Avg Lead Score', number_format($avgLeadScore, 1))
                ->description("High: {$highQualityLeads} | Med: {$mediumQualityLeads} | Low: {$lowQualityLeads}")
                ->descriptionIcon('heroicon-m-star')
                ->color($avgLeadScore >= 70 ? 'success' : ($avgLeadScore >= 50 ? 'warning' : 'danger')),

            // Email Marketing Statistics
            Stat::make('Email Campaigns', $totalEmailCampaigns)
                ->description("{$activeEmailCampaigns} active campaigns")
                ->descriptionIcon('heroicon-m-envelope')
                ->color('primary'),

            Stat::make('Email Open Rate', number_format($avgOpenRate, 1) . '%')
                ->description("Click Rate: " . number_format($avgClickRate, 1) . '%')
                ->descriptionIcon('heroicon-m-envelope-open')
                ->color($avgOpenRate >= 25 ? 'success' : ($avgOpenRate >= 15 ? 'warning' : 'danger')),

            // WhatsApp Marketing Statistics
            Stat::make('WhatsApp Campaigns', $totalWhatsAppCampaigns)
                ->description("{$activeWhatsAppCampaigns} active campaigns")
                ->descriptionIcon('heroicon-m-chat-bubble-left-right')
                ->color('success'),

            Stat::make('WhatsApp Delivery', number_format($avgDeliveryRate, 1) . '%')
                ->description("Read Rate: " . number_format($avgReadRate, 1) . '%')
                ->descriptionIcon('heroicon-m-check-circle')
                ->color($avgDeliveryRate >= 90 ? 'success' : ($avgDeliveryRate >= 80 ? 'warning' : 'danger')),

            // Social Media Statistics
            Stat::make('Social Posts', $totalSocialPosts)
                ->description("{$publishedPosts} published | {$scheduledPosts} scheduled")
                ->descriptionIcon('heroicon-m-megaphone')
                ->color('info'),

            Stat::make('Avg Engagement', number_format($avgEngagement))
                ->description('Per social media post')
                ->descriptionIcon('heroicon-m-heart')
                ->color($avgEngagement >= 100 ? 'success' : ($avgEngagement >= 50 ? 'warning' : 'danger')),

            // Customer Statistics
            Stat::make('Total Customers', number_format($totalCustomers))
                ->description("+{$newCustomersThisMonth} this month")
                ->descriptionIcon('heroicon-m-users')
                ->color('primary'),

            // Monthly Performance
            Stat::make('Monthly Growth', number_format($leadsMonthChange, 1) . '%')
                ->description("Leads: {$leadsThisMonth} vs {$leadsLastMonth}")
                ->descriptionIcon($leadsMonthChange >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down')
                ->color($leadsMonthChange >= 0 ? 'success' : 'danger')
                ->chart($this->getMonthlyChart()),

            // Revenue Metrics (if available)
            Stat::make('Pipeline Value', '₹' . number_format($this->getPipelineValue()))
                ->description('Estimated revenue from active leads')
                ->descriptionIcon('heroicon-m-currency-rupee')
                ->color('warning'),
        ];
    }

    protected function getLeadChart(): array
    {
        // Get last 7 days lead data
        $data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i)->startOfDay();
            $count = Lead::whereDate('created_at', $date)->count();
            $data[] = $count;
        }
        return $data;
    }

    protected function getMonthlyChart(): array
    {
        // Get last 12 months lead data
        $data = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i)->startOfMonth();
            $endDate = now()->subMonths($i)->endOfMonth();
            $count = Lead::whereBetween('created_at', [$date, $endDate])->count();
            $data[] = $count;
        }
        return $data;
    }

    protected function getPipelineValue(): float
    {
        // Calculate estimated pipeline value based on lead scores and estimated values
        $activeLeads = Lead::whereIn('status', ['new', 'contacted', 'qualified', 'proposal_sent', 'negotiation'])
            ->get();

        $totalValue = 0;
        foreach ($activeLeads as $lead) {
            $estimatedValue = $this->getEstimatedLeadValue($lead);
            $conversionProbability = $this->getConversionProbability($lead);
            $totalValue += $estimatedValue * ($conversionProbability / 100);
        }

        return $totalValue;
    }

    protected function getEstimatedLeadValue(Lead $lead): float
    {
        // Estimate lead value based on service interest and company size
        $baseValues = [
            'web_development' => 150000,
            'digital_marketing' => 100000,
            'mobile_development' => 200000,
            'ai_crm' => 300000,
            'business_intelligence' => 250000,
            'graphic_design' => 50000,
            'consultation' => 25000,
            'other' => 75000,
        ];

        $baseValue = $baseValues[$lead->service_interest] ?? 75000;

        // Adjust based on company size (if company name is provided, assume larger budget)
        if ($lead->company) {
            $baseValue *= 1.5;
        }

        // Adjust based on lead score
        $scoreMultiplier = 1 + ($lead->score / 100);
        
        return $baseValue * $scoreMultiplier;
    }

    protected function getConversionProbability(Lead $lead): float
    {
        // Calculate conversion probability based on lead score and status
        $baseProbability = match ($lead->status) {
            'new' => 10,
            'contacted' => 25,
            'qualified' => 50,
            'proposal_sent' => 70,
            'negotiation' => 85,
            default => 5,
        };

        // Adjust based on lead score
        $scoreAdjustment = ($lead->score - 50) * 0.5; // Each point above 50 adds 0.5%
        
        return max(5, min(95, $baseProbability + $scoreAdjustment));
    }
}
