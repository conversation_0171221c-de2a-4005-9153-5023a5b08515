<?php

namespace App\Console\Commands;

use App\Models\AutomationWorkflow;
use App\Models\BusinessSetting;
use App\Models\User;
use Illuminate\Console\Command;

class SetupWhatsAppAutomation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bhavitech:setup-whatsapp-automation {--force : Force recreation of workflows}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Setup WhatsApp automation workflows and settings for Bhavitech';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Setting up WhatsApp automation for Bhavitech...');

        if ($this->option('force')) {
            $this->warn('Force mode: Deleting existing WhatsApp workflows...');
            AutomationWorkflow::where('trigger_type', 'whatsapp_reply')->delete();
        }

        $this->setupWhatsAppWorkflows();
        $this->setupWhatsAppSettings();

        $this->info('✅ WhatsApp automation setup completed!');

        return Command::SUCCESS;
    }

    protected function setupWhatsAppWorkflows(): void
    {
        $this->info('Creating WhatsApp automation workflows...');

        $creator = User::first();
        if (!$creator) {
            $this->error('No users found. Please create a user first.');
            return;
        }

        $workflows = [
            [
                'name' => 'WhatsApp Welcome Message',
                'description' => 'Send welcome message when new lead submits form',
                'trigger_type' => 'form_submit',
                'trigger_conditions' => [
                    [
                        'field' => 'form_type',
                        'operator' => 'equals',
                        'value' => 'contact_form',
                    ]
                ],
                'workflow_steps' => [
                    [
                        'action' => 'send_whatsapp',
                        'delay' => 5, // 5 minutes delay
                        'config' => [
                            'message_type' => 'template',
                            'template' => 'welcome',
                            'message' => 'Welcome to Bhavitech! Thank you for your interest in our {{service_interest}} services.',
                        ]
                    ]
                ],
            ],
            [
                'name' => 'WhatsApp Follow-up Sequence',
                'description' => 'Follow-up with leads who haven\'t responded',
                'trigger_type' => 'time_based',
                'trigger_conditions' => [
                    [
                        'field' => 'days_since_contact',
                        'operator' => 'equals',
                        'value' => '3',
                    ]
                ],
                'workflow_steps' => [
                    [
                        'action' => 'send_whatsapp',
                        'delay' => 0,
                        'config' => [
                            'message_type' => 'text',
                            'message' => 'Hi {{name}}! Just checking in about your {{service_interest}} project. Do you have any questions?',
                        ]
                    ]
                ],
            ],
            [
                'name' => 'WhatsApp Quote Ready Notification',
                'description' => 'Notify when quote is ready',
                'trigger_type' => 'manual',
                'trigger_conditions' => [],
                'workflow_steps' => [
                    [
                        'action' => 'send_whatsapp',
                        'delay' => 0,
                        'config' => [
                            'message_type' => 'template',
                            'template' => 'quote_ready',
                            'message' => 'Great news {{name}}! Your {{project_type}} quote is ready. Total: ₹{{quote_amount}}. Shall we discuss?',
                        ]
                    ]
                ],
            ],
        ];

        $created = 0;
        foreach ($workflows as $workflowData) {
            $existing = AutomationWorkflow::where('name', $workflowData['name'])->first();

            if (!$existing) {
                AutomationWorkflow::create(array_merge($workflowData, [
                    'is_active' => true,
                    'status' => 'active',
                    'created_by' => $creator->id,
                ]));
                $created++;
                $this->line("  ✓ Created workflow: {$workflowData['name']}");
            } else {
                $this->line("  - Workflow already exists: {$workflowData['name']}");
            }
        }

        $this->info("Created {$created} new WhatsApp workflows.");
    }

    protected function setupWhatsAppSettings(): void
    {
        $this->info('Setting up WhatsApp settings...');

        $settings = [
            [
                'key' => 'whatsapp_auto_responses',
                'value' => true,
                'type' => 'boolean',
                'group' => 'whatsapp',
                'label' => 'Enable Auto Responses',
                'description' => 'Automatically respond to incoming WhatsApp messages',
                'is_public' => false,
                'sort_order' => 1,
            ],
            [
                'key' => 'whatsapp_business_hours_only',
                'value' => false,
                'type' => 'boolean',
                'group' => 'whatsapp',
                'label' => 'Business Hours Only',
                'description' => 'Only send auto responses during business hours',
                'is_public' => false,
                'sort_order' => 2,
            ],
            [
                'key' => 'whatsapp_welcome_message',
                'value' => 'Welcome to Bhavitech! How can we help you today?',
                'type' => 'string',
                'group' => 'whatsapp',
                'label' => 'Welcome Message',
                'description' => 'Default welcome message for new contacts',
                'is_public' => false,
                'sort_order' => 3,
            ],
            [
                'key' => 'whatsapp_business_hours_message',
                'value' => 'Thank you for contacting Bhavitech! We\'re currently outside business hours. We\'ll respond during business hours (Mon-Sat, 9 AM - 6 PM).',
                'type' => 'string',
                'group' => 'whatsapp',
                'label' => 'Business Hours Message',
                'description' => 'Message sent outside business hours',
                'is_public' => false,
                'sort_order' => 4,
            ],
            [
                'key' => 'whatsapp_daily_limit',
                'value' => 1000,
                'type' => 'integer',
                'group' => 'whatsapp',
                'label' => 'Daily Message Limit',
                'description' => 'Maximum messages per day per number',
                'is_public' => false,
                'sort_order' => 5,
            ],
        ];

        $created = 0;
        foreach ($settings as $settingData) {
            $existing = BusinessSetting::where('key', $settingData['key'])->first();

            if (!$existing) {
                BusinessSetting::create($settingData);
                $created++;
                $this->line("  ✓ Created setting: {$settingData['key']}");
            } else {
                $this->line("  - Setting already exists: {$settingData['key']}");
            }
        }

        $this->info("Created {$created} new WhatsApp settings.");
    }
}
