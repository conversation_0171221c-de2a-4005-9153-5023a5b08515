<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WhatsAppNumberResource\Pages;
use App\Filament\Resources\WhatsAppNumberResource\RelationManagers;
use App\Models\WhatsAppNumber;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WhatsAppNumberResource extends Resource
{
    protected static ?string $model = WhatsAppNumber::class;

    protected static ?string $navigationIcon = 'heroicon-o-device-phone-mobile';

    protected static ?string $navigationGroup = 'WhatsApp Marketing';

    protected static ?string $navigationLabel = 'WhatsApp Numbers';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('phone_number')
                    ->tel()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('phone_number_id')
                    ->tel()
                    ->required()
                    ->maxLength(255),
                Forms\Components\Toggle::make('is_active')
                    ->required(),
                Forms\Components\Toggle::make('is_primary')
                    ->required(),
                Forms\Components\TextInput::make('daily_message_limit')
                    ->required()
                    ->numeric()
                    ->default(1000),
                Forms\Components\TextInput::make('messages_sent_today')
                    ->required()
                    ->numeric()
                    ->default(0),
                Forms\Components\DateTimePicker::make('last_message_sent_at'),
                Forms\Components\DateTimePicker::make('last_reset_at'),
                Forms\Components\TextInput::make('business_profile'),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\Textarea::make('status_message')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('capabilities'),
                Forms\Components\TextInput::make('cost_per_message')
                    ->required()
                    ->numeric()
                    ->default(0.0055),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone_number_id')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_primary')
                    ->boolean(),
                Tables\Columns\TextColumn::make('daily_message_limit')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('messages_sent_today')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_message_sent_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_reset_at')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('cost_per_message')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWhatsAppNumbers::route('/'),
            'create' => Pages\CreateWhatsAppNumber::route('/create'),
            'edit' => Pages\EditWhatsAppNumber::route('/{record}/edit'),
        ];
    }
}
