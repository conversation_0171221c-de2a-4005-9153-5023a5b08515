<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class CheckMySQLConfiguration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mysql:check-config {--fix : Automatically fix SQL mode issues}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check and optionally fix MySQL configuration for optimal performance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Checking MySQL Configuration...');
        $this->newLine();

        $this->checkSQLMode();
        $this->checkCharacterSet();
        $this->checkEngineSupport();
        $this->checkPerformanceSettings();

        $this->newLine();
        $this->info('✅ MySQL configuration check completed!');
    }

    private function checkSQLMode(): void
    {
        $this->info('📋 Checking SQL Mode...');

        try {
            $result = DB::select("SELECT @@sql_mode as sql_mode");
            $currentMode = $result[0]->sql_mode ?? '';

            $this->line("Current SQL Mode: {$currentMode}");

            $problematicModes = ['ONLY_FULL_GROUP_BY'];
            $hasProblems = false;

            foreach ($problematicModes as $mode) {
                if (str_contains($currentMode, $mode)) {
                    $this->warn("⚠️  Found problematic mode: {$mode}");
                    $hasProblems = true;
                }
            }

            if ($hasProblems && $this->option('fix')) {
                $this->info('🔧 Fixing SQL mode...');
                $newMode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION';
                DB::statement("SET SESSION sql_mode = '{$newMode}'");
                $this->info("✅ SQL mode updated to: {$newMode}");
            } elseif ($hasProblems) {
                $this->warn('💡 Run with --fix to automatically correct SQL mode issues');
            } else {
                $this->info('✅ SQL mode is compatible');
            }

        } catch (\Exception $e) {
            $this->error("❌ Error checking SQL mode: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function checkCharacterSet(): void
    {
        $this->info('🔤 Checking Character Set...');

        try {
            $charset = DB::select("SELECT @@character_set_database as charset")[0]->charset ?? '';
            $collation = DB::select("SELECT @@collation_database as collation")[0]->collation ?? '';

            $this->line("Database Charset: {$charset}");
            $this->line("Database Collation: {$collation}");

            if ($charset === 'utf8mb4') {
                $this->info('✅ Character set is optimal for international content');
            } else {
                $this->warn("⚠️  Consider using utf8mb4 instead of {$charset}");
            }

        } catch (\Exception $e) {
            $this->error("❌ Error checking character set: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function checkEngineSupport(): void
    {
        $this->info('🏗️  Checking Storage Engine Support...');

        try {
            $engines = DB::select("SHOW ENGINES");
            $innodbSupported = false;

            foreach ($engines as $engine) {
                if ($engine->Engine === 'InnoDB' && in_array($engine->Support, ['YES', 'DEFAULT'])) {
                    $innodbSupported = true;
                    $this->info('✅ InnoDB engine is available and supported');
                    break;
                }
            }

            if (!$innodbSupported) {
                $this->error('❌ InnoDB engine is not available - this is required for optimal performance');
            }

        } catch (\Exception $e) {
            $this->error("❌ Error checking engine support: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function checkPerformanceSettings(): void
    {
        $this->info('⚡ Checking Performance Settings...');

        try {
            $settings = [
                'innodb_buffer_pool_size' => '128M',
                'max_connections' => '100',
                'query_cache_size' => '0',
                'innodb_log_file_size' => '48M',
            ];

            foreach ($settings as $setting => $recommendedMin) {
                $result = DB::select("SHOW VARIABLES LIKE '{$setting}'");
                if (!empty($result)) {
                    $currentValue = $result[0]->Value;
                    $this->line("{$setting}: {$currentValue}");

                    // Add specific recommendations based on setting
                    if ($setting === 'innodb_buffer_pool_size') {
                        $this->line("  💡 Recommended: 70-80% of available RAM for dedicated MySQL server");
                    }
                }
            }

        } catch (\Exception $e) {
            $this->error("❌ Error checking performance settings: " . $e->getMessage());
        }

        $this->newLine();
    }
}
