<?php

namespace Tests\Feature;

use App\Models\Lead;
use App\Models\User;
use App\Models\EmailCampaign;
use App\Models\WhatsAppCampaign;
use App\Models\SocialMediaPost;
use App\Services\LeadScoringService;
use App\Services\EmailMarketingService;
use App\Services\WhatsAppMarketingService;
use App\Services\SocialMediaService;
use App\Services\AnalyticsService;
use App\Services\CustomerPortalService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SystemIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);
        
        // Create test customer
        $this->customer = User::factory()->create([
            'role' => 'customer',
            'email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function it_can_create_and_score_leads()
    {
        $leadData = [
            'name' => '<PERSON> Doe',
            'email' => '<EMAIL>',
            'phone' => '+919876543210',
            'company' => 'Example Corp',
            'source' => 'website_contact_form',
            'service_interest' => 'web_development',
            'message' => 'I need a new website for my business',
            'status' => 'new',
        ];

        $lead = Lead::create($leadData);
        
        $this->assertDatabaseHas('leads', [
            'email' => '<EMAIL>',
            'status' => 'new',
        ]);

        // Test lead scoring
        $scoringService = app(LeadScoringService::class);
        $score = $scoringService->calculateScore($lead);
        
        $this->assertIsNumeric($score);
        $this->assertGreaterThanOrEqual(0, $score);
        $this->assertLessThanOrEqual(100, $score);

        // Test lead intelligence
        $intelligence = $scoringService->getLeadIntelligence($lead);
        $this->assertIsArray($intelligence);
        $this->assertArrayHasKey('score_breakdown', $intelligence);
    }

    /** @test */
    public function it_can_create_and_manage_email_campaigns()
    {
        $campaignData = [
            'name' => 'Test Email Campaign',
            'subject' => 'Welcome to Bhavitech',
            'content' => '<h1>Welcome!</h1><p>Thank you for your interest.</p>',
            'type' => 'newsletter',
            'status' => 'draft',
        ];

        $campaign = EmailCampaign::create($campaignData);
        
        $this->assertDatabaseHas('email_campaigns', [
            'name' => 'Test Email Campaign',
            'status' => 'draft',
        ]);

        // Test email service integration
        $emailService = app(EmailMarketingService::class);
        
        // Test template rendering
        $renderedContent = $emailService->renderTemplate('welcome', [
            'name' => 'John Doe',
            'company' => 'Example Corp',
        ]);
        
        $this->assertIsString($renderedContent);
        $this->assertStringContainsString('John Doe', $renderedContent);
    }

    /** @test */
    public function it_can_create_and_manage_whatsapp_campaigns()
    {
        $campaignData = [
            'name' => 'Test WhatsApp Campaign',
            'message' => 'Hello! Thank you for your interest in our services.',
            'type' => 'broadcast',
            'status' => 'draft',
            'sender_number' => '+919876543210',
        ];

        $campaign = WhatsAppCampaign::create($campaignData);
        
        $this->assertDatabaseHas('whatsapp_campaigns', [
            'name' => 'Test WhatsApp Campaign',
            'status' => 'draft',
        ]);

        // Test WhatsApp service integration
        $whatsappService = app(WhatsAppMarketingService::class);
        
        // Test message formatting
        $formattedMessage = $whatsappService->formatMessage($campaign->message, [
            'name' => 'John Doe',
            'company' => 'Example Corp',
        ]);
        
        $this->assertIsString($formattedMessage);
    }

    /** @test */
    public function it_can_create_and_manage_social_media_posts()
    {
        $postData = [
            'title' => 'Test Social Media Post',
            'content' => 'Check out our latest web development services! #webdev #bhavitech',
            'post_type' => 'text',
            'platforms' => ['facebook', 'instagram', 'linkedin'],
            'status' => 'draft',
        ];

        $post = SocialMediaPost::create($postData);
        
        $this->assertDatabaseHas('social_media_posts', [
            'title' => 'Test Social Media Post',
            'status' => 'draft',
        ]);

        // Test social media service integration
        $socialService = app(SocialMediaService::class);
        
        // Test content optimization
        $optimizedContent = $socialService->optimizeContentForPlatform($post->content, 'twitter');
        $this->assertIsString($optimizedContent);
        $this->assertLessThanOrEqual(280, strlen($optimizedContent));
    }

    /** @test */
    public function it_can_generate_analytics_data()
    {
        // Create test data
        Lead::factory()->count(10)->create();
        EmailCampaign::factory()->count(5)->create();
        WhatsAppCampaign::factory()->count(3)->create();
        SocialMediaPost::factory()->count(8)->create();

        $analyticsService = app(AnalyticsService::class);
        
        // Test dashboard data
        $dashboardData = $analyticsService->getDashboardData();
        $this->assertIsArray($dashboardData);
        $this->assertArrayHasKey('total_leads', $dashboardData);
        $this->assertArrayHasKey('conversion_rate', $dashboardData);
        $this->assertArrayHasKey('email_performance', $dashboardData);

        // Test performance metrics
        $metrics = $analyticsService->getPerformanceMetrics();
        $this->assertIsArray($metrics);
        $this->assertArrayHasKey('lead_generation', $metrics);
        $this->assertArrayHasKey('marketing_performance', $metrics);

        // Test trend analysis
        $trends = $analyticsService->getTrendAnalysis('leads', 30);
        $this->assertIsArray($trends);
        $this->assertArrayHasKey('data', $trends);
        $this->assertArrayHasKey('labels', $trends);
    }

    /** @test */
    public function it_can_handle_customer_portal_functionality()
    {
        $portalService = app(CustomerPortalService::class);
        
        // Test customer dashboard data
        $dashboardData = $portalService->getDashboardData($this->customer);
        $this->assertIsArray($dashboardData);
        $this->assertArrayHasKey('projects', $dashboardData);
        $this->assertArrayHasKey('invoices', $dashboardData);
        $this->assertArrayHasKey('support_tickets', $dashboardData);

        // Test project creation
        $projectData = [
            'name' => 'Test Website Project',
            'description' => 'A new website for the customer',
            'status' => 'in_progress',
            'customer_id' => $this->customer->id,
        ];

        $project = $portalService->createProject($projectData);
        $this->assertNotNull($project);
        $this->assertEquals('Test Website Project', $project->name);
    }

    /** @test */
    public function it_can_handle_lead_workflow_automation()
    {
        $lead = Lead::factory()->create([
            'status' => 'new',
            'service_interest' => 'web_development',
        ]);

        $scoringService = app(LeadScoringService::class);
        
        // Test automatic lead scoring
        $score = $scoringService->calculateScore($lead);
        $lead->update(['score' => $score]);
        
        // Test workflow triggers
        if ($score >= 80) {
            $lead->update(['status' => 'qualified']);
            $this->assertEquals('qualified', $lead->fresh()->status);
        }

        // Test lead assignment
        $assignedUser = $scoringService->assignLead($lead);
        if ($assignedUser) {
            $this->assertInstanceOf(User::class, $assignedUser);
            $this->assertEquals($assignedUser->id, $lead->fresh()->assigned_to);
        }
    }

    /** @test */
    public function it_can_handle_api_endpoints()
    {
        $this->actingAs($this->admin);

        // Test leads API
        $response = $this->getJson('/api/leads');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'email',
                    'status',
                    'score',
                    'created_at',
                ]
            ]
        ]);

        // Test analytics API
        $response = $this->getJson('/api/analytics/dashboard');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total_leads',
            'conversion_rate',
            'email_performance',
            'social_performance',
        ]);

        // Test campaign API
        $response = $this->getJson('/api/email-campaigns');
        $response->assertStatus(200);
    }

    /** @test */
    public function it_can_handle_contact_form_submissions()
    {
        $formData = [
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'phone' => '+919876543211',
            'company' => 'Smith Industries',
            'service_interest' => 'digital_marketing',
            'budget' => '1l_3l',
            'timeline' => '3_months',
            'message' => 'I need help with digital marketing for my business.',
        ];

        $response = $this->post('/contact', $formData);
        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify lead was created
        $this->assertDatabaseHas('leads', [
            'email' => '<EMAIL>',
            'service_interest' => 'digital_marketing',
            'status' => 'new',
        ]);

        // Verify lead was scored
        $lead = Lead::where('email', '<EMAIL>')->first();
        $this->assertNotNull($lead->score);
        $this->assertGreaterThan(0, $lead->score);
    }

    /** @test */
    public function it_can_handle_metal_rates_integration()
    {
        // Test metal rates API integration
        $response = $this->getJson('/api/metal-rates');
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'gold',
            'silver',
            'platinum',
            'last_updated',
        ]);

        // Test rate alerts
        $alertData = [
            'metal' => 'gold',
            'target_price' => 50000,
            'email' => '<EMAIL>',
            'phone' => '+919876543210',
        ];

        $response = $this->post('/api/metal-rates/alerts', $alertData);
        $response->assertStatus(201);
    }

    /** @test */
    public function it_maintains_data_integrity()
    {
        // Test foreign key constraints
        $lead = Lead::factory()->create();
        $user = User::factory()->create(['role' => 'admin']);
        
        $lead->update(['assigned_to' => $user->id]);
        $this->assertEquals($user->id, $lead->fresh()->assigned_to);

        // Test cascade deletes work properly
        $campaign = EmailCampaign::factory()->create();
        $campaignId = $campaign->id;
        
        $campaign->delete();
        $this->assertDatabaseMissing('email_campaigns', ['id' => $campaignId]);
    }

    /** @test */
    public function it_handles_performance_requirements()
    {
        // Create large dataset
        Lead::factory()->count(1000)->create();
        
        // Test query performance
        $start = microtime(true);
        $leads = Lead::with(['assignedTo'])->paginate(50);
        $queryTime = (microtime(true) - $start) * 1000;
        
        $this->assertLessThan(1000, $queryTime); // Should be under 1 second
        $this->assertCount(50, $leads);

        // Test memory usage
        $memoryBefore = memory_get_usage();
        $analyticsService = app(AnalyticsService::class);
        $dashboardData = $analyticsService->getDashboardData();
        $memoryAfter = memory_get_usage();
        $memoryUsed = ($memoryAfter - $memoryBefore) / 1024 / 1024; // MB
        
        $this->assertLessThan(50, $memoryUsed); // Should use less than 50MB
    }

    /** @test */
    public function it_handles_error_scenarios_gracefully()
    {
        // Test invalid lead data
        $response = $this->post('/contact', [
            'name' => '',
            'email' => 'invalid-email',
            'message' => '',
        ]);
        
        $response->assertSessionHasErrors(['name', 'email', 'message']);

        // Test service failures
        $scoringService = app(LeadScoringService::class);
        
        // Test with invalid lead
        $invalidLead = new Lead();
        $score = $scoringService->calculateScore($invalidLead);
        $this->assertEquals(0, $score); // Should return default score

        // Test API error handling
        $response = $this->getJson('/api/nonexistent-endpoint');
        $response->assertStatus(404);
    }
}
