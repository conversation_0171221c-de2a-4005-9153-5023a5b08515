<!-- <PERSON><PERSON> -->
<div x-data="{ show: !localStorage.getItem('cookie-consent') }" 
     x-show="show" 
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-y-full"
     x-transition:enter-end="opacity-100 transform translate-y-0"
     x-transition:leave="transition ease-in duration-300"
     x-transition:leave-start="opacity-100 transform translate-y-0"
     x-transition:leave-end="opacity-0 transform translate-y-full"
     class="fixed bottom-0 left-0 right-0 bg-gray-900 text-white p-4 z-50">
    <div class="container-custom">
        <div class="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            <div class="flex-1">
                <p class="text-sm">
                    We use cookies to enhance your browsing experience and provide personalized content. 
                    By continuing to use our site, you consent to our use of cookies.
                    <a href="/privacy-policy" class="text-primary-400 hover:text-primary-300 underline">Learn more</a>
                </p>
            </div>
            <div class="flex space-x-4">
                <button @click="localStorage.setItem('cookie-consent', 'true'); show = false" 
                        class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg transition-colors">
                    Accept
                </button>
                <button @click="show = false" 
                        class="bg-gray-700 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                    Decline
                </button>
            </div>
        </div>
    </div>
</div>
