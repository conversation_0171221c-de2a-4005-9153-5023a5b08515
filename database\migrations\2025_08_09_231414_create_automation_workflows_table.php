<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('automation_workflows', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('trigger_type', ['form_submit', 'lead_score', 'time_based', 'behavior', 'manual']);
            $table->json('trigger_conditions'); // Conditions that start the workflow
            $table->json('workflow_steps'); // Array of workflow actions
            $table->boolean('is_active')->default(true);
            $table->integer('execution_count')->default(0);
            $table->timestamp('last_executed_at')->nullable();
            $table->enum('status', ['active', 'paused', 'draft', 'archived'])->default('draft');
            $table->json('settings')->nullable(); // Additional workflow settings
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();

            $table->index(['trigger_type', 'is_active']);
            $table->index(['status', 'is_active']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('automation_workflows');
    }
};
