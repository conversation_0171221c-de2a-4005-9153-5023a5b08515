<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_numbers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Display name for the number
            $table->string('phone_number')->unique(); // WhatsApp Business number
            $table->string('phone_number_id')->unique(); // WhatsApp API phone number ID
            $table->string('access_token'); // WhatsApp Business API access token
            $table->string('webhook_verify_token')->nullable();
            $table->boolean('is_active')->default(true);
            $table->boolean('is_primary')->default(false); // Primary number for rotation
            $table->integer('daily_message_limit')->default(1000);
            $table->integer('messages_sent_today')->default(0);
            $table->timestamp('last_message_sent_at')->nullable();
            $table->timestamp('last_reset_at')->nullable(); // Daily counter reset
            $table->json('business_profile')->nullable(); // Business profile info
            $table->enum('status', ['active', 'suspended', 'rate_limited', 'error'])->default('active');
            $table->text('status_message')->nullable();
            $table->json('capabilities')->nullable(); // Available message types
            $table->decimal('cost_per_message', 8, 4)->default(0.0055); // Cost tracking
            $table->timestamps();

            $table->index(['is_active', 'status']);
            $table->index('is_primary');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_numbers');
    }
};
