<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Project;
use App\Models\Invoice;
use App\Models\SupportTicket;
use App\Models\Lead;
use App\Models\User;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample projects
        $customers = User::where('role', 'customer')->get();
        $projectManager = User::where('role', 'user')->first();

        foreach ($customers as $customer) {
            // Create 2-3 projects per customer
            $projectCount = rand(2, 3);
            for ($i = 0; $i < $projectCount; $i++) {
                $project = Project::factory()->create([
                    'customer_id' => $customer->id,
                    'project_manager_id' => $projectManager?->id,
                ]);

                // Create invoices for each project
                $invoiceCount = rand(1, 3);
                for ($j = 0; $j < $invoiceCount; $j++) {
                    Invoice::create([
                        'customer_id' => $customer->id,
                        'project_id' => $project->id,
                        'number' => 'INV-' . date('Y') . '-' . str_pad(($project->id * 10) + $j, 4, '0', STR_PAD_LEFT),
                        'description' => "Invoice for {$project->name} - Phase " . ($j + 1),
                        'amount' => rand(10000, 100000),
                        'tax_amount' => rand(1800, 18000),
                        'total_amount' => rand(11800, 118000),
                        'status' => collect(['pending', 'paid', 'overdue'])->random(),
                        'due_date' => now()->addDays(rand(7, 30)),
                        'paid_at' => rand(0, 1) ? now()->subDays(rand(1, 30)) : null,
                        'payment_method' => collect(['bank_transfer', 'upi', 'cash', 'cheque'])->random(),
                    ]);
                }

                // Create support tickets
                if (rand(0, 1)) {
                    SupportTicket::create([
                        'customer_id' => $customer->id,
                        'assigned_to' => $projectManager?->id,
                        'ticket_number' => 'TKT-' . date('Y') . '-' . str_pad($project->id * 100 + rand(1, 99), 6, '0', STR_PAD_LEFT),
                        'title' => collect([
                            'Website loading issue',
                            'Feature request for mobile app',
                            'Payment gateway integration',
                            'SEO optimization query',
                            'Design modification request',
                            'Bug report - contact form',
                            'Performance optimization',
                            'Content update request'
                        ])->random(),
                        'description' => 'Sample support ticket description for testing purposes.',
                        'priority' => collect(['low', 'medium', 'high', 'urgent'])->random(),
                        'category' => collect(['technical', 'billing', 'general', 'feature_request'])->random(),
                        'status' => collect(['open', 'in_progress', 'resolved', 'closed'])->random(),
                        'last_response_at' => now()->subDays(rand(1, 7)),
                        'metadata' => [
                            'browser' => collect(['Chrome', 'Firefox', 'Safari', 'Edge'])->random(),
                            'os' => collect(['Windows', 'macOS', 'Linux', 'iOS', 'Android'])->random(),
                        ],
                    ]);
                }
            }
        }

        // Create sample leads
        for ($i = 0; $i < 20; $i++) {
            Lead::create([
                'name' => fake()->name(),
                'email' => fake()->unique()->safeEmail(),
                'phone' => '+91' . fake()->numerify('##########'),
                'company' => fake()->company(),
                'source' => collect(['website', 'social', 'referral', 'advertisement', 'cold_call'])->random(),
                'status' => collect(['new', 'contacted', 'qualified', 'converted', 'lost'])->random(),
                'score' => rand(0, 100),
                'service_interest' => collect(['web_development', 'mobile_development', 'digital_marketing', 'graphic_design', 'consultation'])->random(),
                'message' => fake()->paragraph(),
                'assigned_to' => $projectManager?->id,
                'metadata' => [
                    'budget_range' => collect(['under_50k', '50k_1l', '1l_3l', '3l_5l', 'above_5l'])->random(),
                    'timeline' => collect(['asap', '1_month', '3_months', '6_months', 'flexible'])->random(),
                    'city' => fake()->city(),
                    'state' => collect(['Tamil Nadu', 'Karnataka', 'Kerala', 'Andhra Pradesh', 'Telangana'])->random(),
                    'country' => 'India',
                ],
                'last_contacted_at' => rand(0, 1) ? fake()->dateTimeBetween('-30 days', 'now') : null,
                'qualified_at' => rand(0, 1) ? fake()->dateTimeBetween('-20 days', 'now') : null,
                'converted_at' => rand(0, 1) ? fake()->dateTimeBetween('-10 days', 'now') : null,
            ]);
        }
    }
}
