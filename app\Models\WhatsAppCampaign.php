<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WhatsAppCampaign extends Model
{
    use HasFactory;

    protected $table = 'whatsapp_campaigns';

    protected $fillable = [
        'name',
        'message',
        'type',
        'status',
        'recipients',
        'phone_numbers',
        'recipient_notes',
        'sender_number',
        'media',
        'scheduled_at',
        'sent_at',
        'total_recipients',
        'delivered_count',
        'read_count',
        'replied_count',
        'failed_count',
        'delivery_rate',
        'read_rate',
        'created_by',
    ];

    protected $casts = [
        'template_data' => 'array',
        'media_data' => 'array',
        'target_audience' => 'array',
        'phone_numbers' => 'array',
        'recipients' => 'array',
        'scheduled_at' => 'datetime',
        'sent_at' => 'datetime',
    ];

    // Relationships
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function messages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class, 'campaign_id');
    }

    // Scopes
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    public function scopeScheduled($query)
    {
        return $query->where('status', 'scheduled');
    }

    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    public function scopeReadyToSend($query)
    {
        return $query->where('status', 'scheduled')
            ->where('scheduled_at', '<=', now());
    }

    // Methods
    public function isReadyToSend(): bool
    {
        return $this->status === 'scheduled' && $this->scheduled_at <= now();
    }

    public function markAsSent(): void
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    }

    public function getDeliveryRate(): float
    {
        return $this->sent_count > 0 ? ($this->delivered_count / $this->sent_count) * 100 : 0;
    }

    public function getReadRate(): float
    {
        return $this->sent_count > 0 ? ($this->read_count / $this->sent_count) * 100 : 0;
    }

    public function getResponseRate(): float
    {
        return $this->sent_count > 0 ? ($this->response_count / $this->sent_count) * 100 : 0;
    }

    public function getFailureRate(): float
    {
        return $this->sent_count > 0 ? ($this->failed_count / $this->sent_count) * 100 : 0;
    }

    public function updateStats(array $stats): void
    {
        $this->update([
            'delivered_count' => $stats['delivered'] ?? $this->delivered_count,
            'read_count' => $stats['read'] ?? $this->read_count,
            'failed_count' => $stats['failed'] ?? $this->failed_count,
            'response_count' => $stats['response'] ?? $this->response_count,
        ]);
    }

    public function getPerformanceMetrics(): array
    {
        return [
            'sent_count' => $this->sent_count,
            'delivered_count' => $this->delivered_count,
            'read_count' => $this->read_count,
            'failed_count' => $this->failed_count,
            'response_count' => $this->response_count,
            'delivery_rate' => round($this->getDeliveryRate(), 2),
            'read_rate' => round($this->getReadRate(), 2),
            'response_rate' => round($this->getResponseRate(), 2),
            'failure_rate' => round($this->getFailureRate(), 2),
        ];
    }

    public function getTargetAudienceSize(): int
    {
        $audience = $this->target_audience;
        $size = 0;

        if (isset($audience['leads'])) {
            $size += count($audience['leads']);
        }

        if (isset($audience['contacts'])) {
            $size += count($audience['contacts']);
        }

        if (isset($audience['phone_numbers'])) {
            $size += count($audience['phone_numbers']);
        }

        return $size;
    }
}
