# Bhavitech Platform - Production Deployment Guide

🚀 **Complete guide for deploying the Bhavitech Enterprise CRM & Marketing Platform to production environments.**

## 📋 Pre-Deployment Checklist

### Server Requirements
- [ ] **Operating System**: Ubuntu 20.04+ or CentOS 8+
- [ ] **PHP**: Version 8.2 or higher with required extensions
- [ ] **Web Server**: Nginx 1.18+ or Apache 2.4+
- [ ] **Database**: MySQL 8.0+ or PostgreSQL 13+
- [ ] **Cache**: Redis 6.0+ for caching and queues
- [ ] **SSL Certificate**: Valid SSL certificate for HTTPS
- [ ] **Domain**: Configured domain pointing to server
- [ ] **Backup Solution**: Automated backup system in place

### PHP Extensions Required
```bash
# Check required PHP extensions
php -m | grep -E "(mbstring|openssl|PDO|tokenizer|XML|ctype|JSON|BCMath|fileinfo|gd|curl|zip)"
```

Required extensions:
- mbstring, openssl, PDO, tokenizer, XML, ctype, JSON, BCMath, fileinfo, gd, curl, zip, redis

## 🔧 Server Setup

### 1. System Updates
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### 2. Install PHP 8.2
```bash
# Ubuntu/Debian
sudo apt install software-properties-common
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install php8.2 php8.2-fpm php8.2-mysql php8.2-redis php8.2-mbstring php8.2-xml php8.2-curl php8.2-zip php8.2-gd php8.2-bcmath

# CentOS/RHEL
sudo dnf install epel-release
sudo dnf install https://rpms.remirepo.net/enterprise/remi-release-8.rpm
sudo dnf module enable php:remi-8.2
sudo dnf install php php-fpm php-mysql php-redis php-mbstring php-xml php-curl php-zip php-gd php-bcmath
```

### 3. Install Composer
```bash
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer
```

### 4. Install Node.js & NPM
```bash
# Using NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

### 5. Install MySQL
```bash
# Ubuntu/Debian
sudo apt install mysql-server mysql-client

# CentOS/RHEL
sudo dnf install mysql-server mysql

# Secure MySQL installation
sudo mysql_secure_installation
```

### 6. Install Redis
```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo dnf install redis

# Start and enable Redis
sudo systemctl start redis
sudo systemctl enable redis
```

### 7. Install Nginx
```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL
sudo dnf install nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

## 📦 Application Deployment

### 1. Clone Repository
```bash
# Create application directory
sudo mkdir -p /var/www/bhavitech-platform
cd /var/www/bhavitech-platform

# Clone repository (replace with your actual repository)
sudo git clone https://github.com/bhavitech/enterprise-platform.git .

# Set ownership
sudo chown -R www-data:www-data /var/www/bhavitech-platform
```

### 2. Install Dependencies
```bash
# Install PHP dependencies
composer install --optimize-autoloader --no-dev

# Install Node.js dependencies
npm ci --only=production

# Build production assets
npm run build
```

### 3. Environment Configuration
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Edit environment file
nano .env
```

### 4. Environment Variables for Production
```env
# Application
APP_NAME="Bhavitech Enterprise Platform"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=bhavitech_production
DB_USERNAME=bhavitech_user
DB_PASSWORD=secure_password_here

# Cache & Sessions
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Email (Production SMTP)
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Bhavitech Platform"

# File Storage (S3 for production)
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=bhavitech-platform-files

# Security
SESSION_LIFETIME=120
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# Logging
LOG_CHANNEL=daily
LOG_LEVEL=warning
LOG_DAYS=14

# API Rate Limiting
API_RATE_LIMIT=1000
API_RATE_LIMIT_GUEST=60
```

### 5. Database Setup
```bash
# Create database and user
mysql -u root -p
```

```sql
CREATE DATABASE bhavitech_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'bhavitech_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON bhavitech_production.* TO 'bhavitech_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

```bash
# Run migrations
php artisan migrate --force

# Seed initial data (optional)
php artisan db:seed --class=ProductionSeeder
```

### 6. Optimize for Production
```bash
# Cache configuration
php artisan config:cache

# Cache routes
php artisan route:cache

# Cache views
php artisan view:cache

# Cache events
php artisan event:cache

# Optimize autoloader
composer dump-autoload --optimize
```

### 7. Set Permissions
```bash
# Set proper ownership
sudo chown -R www-data:www-data /var/www/bhavitech-platform

# Set directory permissions
sudo find /var/www/bhavitech-platform -type d -exec chmod 755 {} \;

# Set file permissions
sudo find /var/www/bhavitech-platform -type f -exec chmod 644 {} \;

# Set storage and cache permissions
sudo chmod -R 775 /var/www/bhavitech-platform/storage
sudo chmod -R 775 /var/www/bhavitech-platform/bootstrap/cache
```

## 🌐 Web Server Configuration

### Nginx Configuration
```nginx
# /etc/nginx/sites-available/bhavitech-platform
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    root /var/www/bhavitech-platform/public;
    index index.php index.html;

    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss;

    # File Upload Size
    client_max_body_size 100M;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|woff|woff2|ttf|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### Enable Site
```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/bhavitech-platform /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Reload Nginx
sudo systemctl reload nginx
```

## 🔄 Process Management

### 1. Queue Worker Setup
```bash
# Create systemd service for queue worker
sudo nano /etc/systemd/system/bhavitech-worker.service
```

```ini
[Unit]
Description=Bhavitech Queue Worker
After=redis.service

[Service]
User=www-data
Group=www-data
Restart=always
ExecStart=/usr/bin/php /var/www/bhavitech-platform/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start the service
sudo systemctl daemon-reload
sudo systemctl enable bhavitech-worker
sudo systemctl start bhavitech-worker
```

### 2. Scheduler Setup
```bash
# Add to crontab
sudo crontab -e

# Add this line
* * * * * cd /var/www/bhavitech-platform && php artisan schedule:run >> /dev/null 2>&1
```

### 3. Supervisor Configuration (Alternative)
```bash
# Install Supervisor
sudo apt install supervisor

# Create configuration
sudo nano /etc/supervisor/conf.d/bhavitech-worker.conf
```

```ini
[program:bhavitech-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /var/www/bhavitech-platform/artisan queue:work redis --sleep=3 --tries=3 --max-time=3600
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/var/www/bhavitech-platform/storage/logs/worker.log
stopwaitsecs=3600
```

```bash
# Update Supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start bhavitech-worker:*
```

## 🔒 Security Hardening

### 1. Firewall Configuration
```bash
# Install UFW
sudo apt install ufw

# Default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH, HTTP, HTTPS
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# Enable firewall
sudo ufw enable
```

### 2. Fail2Ban Setup
```bash
# Install Fail2Ban
sudo apt install fail2ban

# Create custom configuration
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
findtime = 600
bantime = 7200
maxretry = 10
```

### 3. SSL Certificate (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 Monitoring & Logging

### 1. Log Rotation
```bash
# Create logrotate configuration
sudo nano /etc/logrotate.d/bhavitech-platform
```

```
/var/www/bhavitech-platform/storage/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload php8.2-fpm
    endscript
}
```

### 2. Health Check Script
```bash
# Create health check script
nano /var/www/bhavitech-platform/health-check.sh
```

```bash
#!/bin/bash
# Health check script for Bhavitech Platform

# Check if application is responding
if curl -f -s http://localhost/health > /dev/null; then
    echo "✅ Application is healthy"
else
    echo "❌ Application health check failed"
    # Send alert notification
    # systemctl restart php8.2-fpm
fi

# Check queue worker
if systemctl is-active --quiet bhavitech-worker; then
    echo "✅ Queue worker is running"
else
    echo "❌ Queue worker is not running"
    systemctl start bhavitech-worker
fi

# Check Redis
if redis-cli ping > /dev/null; then
    echo "✅ Redis is responding"
else
    echo "❌ Redis is not responding"
fi

# Check MySQL
if mysqladmin ping -h localhost > /dev/null; then
    echo "✅ MySQL is responding"
else
    echo "❌ MySQL is not responding"
fi
```

```bash
# Make executable and add to cron
chmod +x /var/www/bhavitech-platform/health-check.sh

# Add to crontab (every 5 minutes)
*/5 * * * * /var/www/bhavitech-platform/health-check.sh >> /var/log/bhavitech-health.log 2>&1
```

## 🔄 Backup Strategy

### 1. Database Backup
```bash
# Create backup script
nano /var/www/bhavitech-platform/backup-db.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/bhavitech"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="bhavitech_production"
DB_USER="bhavitech_user"
DB_PASS="secure_password_here"

mkdir -p $BACKUP_DIR

# Create database backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Keep only last 7 days of backups
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

echo "Database backup completed: db_backup_$DATE.sql.gz"
```

### 2. File Backup
```bash
# Create file backup script
nano /var/www/bhavitech-platform/backup-files.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/bhavitech"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/var/www/bhavitech-platform"

mkdir -p $BACKUP_DIR

# Backup storage directory
tar -czf $BACKUP_DIR/storage_backup_$DATE.tar.gz -C $APP_DIR storage/

# Backup uploaded files
tar -czf $BACKUP_DIR/public_backup_$DATE.tar.gz -C $APP_DIR/public uploads/

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*_backup_*.tar.gz" -mtime +7 -delete

echo "File backup completed: storage_backup_$DATE.tar.gz"
```

### 3. Automated Backup Schedule
```bash
# Add to crontab
sudo crontab -e

# Daily database backup at 2 AM
0 2 * * * /var/www/bhavitech-platform/backup-db.sh

# Daily file backup at 3 AM
0 3 * * * /var/www/bhavitech-platform/backup-files.sh
```

## 🚀 Deployment Automation

### 1. Deployment Script
```bash
# Create deployment script
nano /var/www/bhavitech-platform/deploy.sh
```

```bash
#!/bin/bash
set -e

echo "🚀 Starting deployment..."

# Pull latest changes
git pull origin main

# Install/update dependencies
composer install --optimize-autoloader --no-dev
npm ci --only=production

# Build assets
npm run build

# Run migrations
php artisan migrate --force

# Clear and cache
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Restart services
sudo systemctl restart php8.2-fpm
sudo systemctl restart bhavitech-worker

echo "✅ Deployment completed successfully!"
```

### 2. Zero-Downtime Deployment
```bash
# Advanced deployment with zero downtime
nano /var/www/bhavitech-platform/zero-downtime-deploy.sh
```

```bash
#!/bin/bash
set -e

DEPLOY_DIR="/var/www/bhavitech-platform"
RELEASES_DIR="$DEPLOY_DIR/releases"
CURRENT_LINK="$DEPLOY_DIR/current"
SHARED_DIR="$DEPLOY_DIR/shared"
TIMESTAMP=$(date +%Y%m%d%H%M%S)
RELEASE_DIR="$RELEASES_DIR/$TIMESTAMP"

echo "🚀 Starting zero-downtime deployment..."

# Create directories
mkdir -p $RELEASES_DIR $SHARED_DIR

# Clone to new release directory
git clone https://github.com/bhavitech/enterprise-platform.git $RELEASE_DIR

cd $RELEASE_DIR

# Install dependencies
composer install --optimize-autoloader --no-dev
npm ci --only=production
npm run build

# Link shared directories
ln -nfs $SHARED_DIR/storage $RELEASE_DIR/storage
ln -nfs $SHARED_DIR/.env $RELEASE_DIR/.env

# Run migrations
php artisan migrate --force

# Cache everything
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Switch to new release
ln -nfs $RELEASE_DIR $CURRENT_LINK

# Restart services
sudo systemctl restart php8.2-fpm
sudo systemctl restart bhavitech-worker

# Clean up old releases (keep last 3)
cd $RELEASES_DIR && ls -t | tail -n +4 | xargs rm -rf

echo "✅ Zero-downtime deployment completed!"
```

## 📞 Post-Deployment Verification

### 1. Health Checks
```bash
# Run system status report
cd /var/www/bhavitech-platform
php artisan bhavitech:status-report

# Test API endpoints
curl -I https://your-domain.com/api/health
curl -I https://your-domain.com/admin
curl -I https://your-domain.com/customer

# Check queue processing
php artisan queue:monitor

# Verify scheduled tasks
php artisan schedule:list
```

### 2. Performance Testing
```bash
# Install Apache Bench for basic testing
sudo apt install apache2-utils

# Test homepage
ab -n 100 -c 10 https://your-domain.com/

# Test API endpoint
ab -n 100 -c 10 https://your-domain.com/api/leads
```

### 3. Security Verification
```bash
# SSL test
curl -I https://your-domain.com/

# Security headers check
curl -I https://your-domain.com/ | grep -E "(X-Frame-Options|X-XSS-Protection|X-Content-Type-Options)"

# Check for exposed files
curl -I https://your-domain.com/.env
curl -I https://your-domain.com/.git/
```

## 🆘 Troubleshooting

### Common Issues

1. **Permission Errors**
   ```bash
   sudo chown -R www-data:www-data /var/www/bhavitech-platform
   sudo chmod -R 775 storage bootstrap/cache
   ```

2. **Queue Not Processing**
   ```bash
   sudo systemctl status bhavitech-worker
   sudo systemctl restart bhavitech-worker
   php artisan queue:restart
   ```

3. **Cache Issues**
   ```bash
   php artisan cache:clear
   php artisan config:clear
   php artisan route:clear
   php artisan view:clear
   ```

4. **Database Connection Issues**
   ```bash
   # Test database connection
   php artisan tinker
   DB::connection()->getPdo();
   ```

### Log Locations
- **Application Logs**: `/var/www/bhavitech-platform/storage/logs/`
- **Nginx Logs**: `/var/log/nginx/`
- **PHP-FPM Logs**: `/var/log/php8.2-fpm.log`
- **MySQL Logs**: `/var/log/mysql/`
- **System Logs**: `/var/log/syslog`

---

**Deployment Guide Version**: 1.0  
**Last Updated**: August 2025  
**Platform Version**: 1.0.0

For support during deployment, contact: <EMAIL>
