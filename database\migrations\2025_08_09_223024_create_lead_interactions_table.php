<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_interactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lead_id')->constrained()->onDelete('cascade');
            $table->string('type'); // email, call, meeting, note, website_visit, form_submission
            $table->string('channel')->nullable(); // email, phone, whatsapp, website, social
            $table->text('description')->nullable();
            $table->json('metadata')->nullable(); // Additional data like email subject, call duration, etc.
            $table->timestamp('interaction_date');
            $table->foreignId('user_id')->nullable()->constrained(); // Who performed the interaction
            $table->boolean('is_automated')->default(false);
            $table->timestamps();

            $table->index(['lead_id', 'interaction_date']);
            $table->index(['type', 'interaction_date']);
            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_interactions');
    }
};
