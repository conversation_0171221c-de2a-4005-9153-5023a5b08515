@extends('layouts.app')

@section('title', 'Mobile App Development Services - iOS & Android Apps | Bhavitech')
@section('description', 'Professional mobile app development services in Salem. Native iOS, Android apps, and cross-platform solutions that engage users and drive business growth.')

@section('content')
<!-- Hero Section -->
<section class="relative py-20 lg:py-32 gradient-bg overflow-hidden">
    <div class="absolute inset-0">
        <div class="absolute inset-0 bg-black opacity-40"></div>
        <div class="absolute top-20 right-10 w-72 h-72 bg-green-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow"></div>
        <div class="absolute bottom-20 left-10 w-72 h-72 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse-slow animation-delay-200"></div>
    </div>
    
    <div class="relative z-10 container-custom">
        <div class="max-w-4xl mx-auto text-center text-white">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in">
                Expert <span class="text-gradient bg-gradient-to-r from-green-400 to-blue-500 bg-clip-text text-transparent">Mobile App</span> Development
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-gray-200 animate-slide-up animation-delay-200">
                Native iOS and Android apps that deliver exceptional user experiences and drive business success
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center animate-slide-up animation-delay-400">
                <a href="{{ route('quote') }}" class="btn-primary text-lg px-8 py-4">Get Free Quote</a>
                <a href="#portfolio" class="btn-secondary bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 text-lg px-8 py-4">View Our Apps</a>
            </div>
        </div>
    </div>
</section>

<!-- Services Overview -->
<section class="section-padding bg-white">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold mb-4">Mobile App Development Services</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                From concept to deployment, we create mobile apps that users love and businesses rely on
            </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Native iOS Development -->
            <div class="card group hover:scale-105 transition-transform duration-300">
                <div class="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-primary-600 transition-colors duration-300">
                    <svg class="w-8 h-8 text-primary-600 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-3">Native iOS Development</h3>
                <p class="text-gray-600 mb-4">High-performance iOS apps built with Swift and Objective-C for optimal user experience.</p>
                <ul class="text-sm text-gray-500 space-y-1">
                    <li>• Swift & Objective-C</li>
                    <li>• App Store Optimization</li>
                    <li>• iOS Design Guidelines</li>
                    <li>• Core Data Integration</li>
                </ul>
            </div>

            <!-- Native Android Development -->
            <div class="card group hover:scale-105 transition-transform duration-300">
                <div class="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-primary-600 transition-colors duration-300">
                    <svg class="w-8 h-8 text-primary-600 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.523 15.3414c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993.0001.5511-.4482.9997-.9993.9997m-11.046 0c-.5511 0-.9993-.4486-.9993-.9997s.4482-.9993.9993-.9993c.5511 0 .9993.4482.9993.9993 0 .5511-.4482.9997-.9993.9997m11.4045-6.02l1.9973-3.4592a.416.416 0 00-.1521-.5676.416.416 0 00-.5676.1521l-2.0223 3.503C15.5902 8.2439 13.8533 7.8508 12 7.8508s-3.5902.3931-5.1367 1.0989L4.841 5.4467a.4161.4161 0 00-.5677-.1521.4157.4157 0 00-.1521.5676l1.9973 3.4592C2.6889 11.1867.3432 14.6589 0 18.761h24c-.3435-4.1021-2.6892-7.5743-6.1185-9.4396"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-3">Native Android Development</h3>
                <p class="text-gray-600 mb-4">Robust Android apps using Kotlin and Java with Material Design principles.</p>
                <ul class="text-sm text-gray-500 space-y-1">
                    <li>• Kotlin & Java</li>
                    <li>• Google Play Store</li>
                    <li>• Material Design</li>
                    <li>• Firebase Integration</li>
                </ul>
            </div>

            <!-- Cross-Platform Development -->
            <div class="card group hover:scale-105 transition-transform duration-300">
                <div class="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-primary-600 transition-colors duration-300">
                    <svg class="w-8 h-8 text-primary-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-3">Cross-Platform Development</h3>
                <p class="text-gray-600 mb-4">Cost-effective solutions using React Native and Flutter for both platforms.</p>
                <ul class="text-sm text-gray-500 space-y-1">
                    <li>• React Native</li>
                    <li>• Flutter Development</li>
                    <li>• Code Reusability</li>
                    <li>• Faster Time to Market</li>
                </ul>
            </div>

            <!-- UI/UX Design -->
            <div class="card group hover:scale-105 transition-transform duration-300">
                <div class="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-primary-600 transition-colors duration-300">
                    <svg class="w-8 h-8 text-primary-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-3">Mobile UI/UX Design</h3>
                <p class="text-gray-600 mb-4">Intuitive and engaging mobile interfaces that provide exceptional user experiences.</p>
                <ul class="text-sm text-gray-500 space-y-1">
                    <li>• User Research</li>
                    <li>• Wireframing & Prototyping</li>
                    <li>• Usability Testing</li>
                    <li>• Accessibility Standards</li>
                </ul>
            </div>

            <!-- App Store Optimization -->
            <div class="card group hover:scale-105 transition-transform duration-300">
                <div class="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-primary-600 transition-colors duration-300">
                    <svg class="w-8 h-8 text-primary-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-3">App Store Optimization</h3>
                <p class="text-gray-600 mb-4">Maximize your app's visibility and downloads with strategic ASO techniques.</p>
                <ul class="text-sm text-gray-500 space-y-1">
                    <li>• Keyword Optimization</li>
                    <li>• App Store Listings</li>
                    <li>• Review Management</li>
                    <li>• Performance Analytics</li>
                </ul>
            </div>

            <!-- App Maintenance -->
            <div class="card group hover:scale-105 transition-transform duration-300">
                <div class="w-16 h-16 bg-primary-100 rounded-lg flex items-center justify-center mb-6 group-hover:bg-primary-600 transition-colors duration-300">
                    <svg class="w-8 h-8 text-primary-600 group-hover:text-white transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold mb-3">App Maintenance & Support</h3>
                <p class="text-gray-600 mb-4">Ongoing support to keep your app updated, secure, and performing at its best.</p>
                <ul class="text-sm text-gray-500 space-y-1">
                    <li>• Regular Updates</li>
                    <li>• Bug Fixes</li>
                    <li>• Performance Monitoring</li>
                    <li>• Security Patches</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Development Process -->
<section class="section-padding bg-gray-50">
    <div class="container-custom">
        <div class="text-center mb-16">
            <h2 class="text-4xl md:text-5xl font-bold mb-4">Our Development Process</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                A proven methodology that ensures your app is delivered on time, within budget, and exceeds expectations
            </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Discovery & Planning -->
            <div class="text-center">
                <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">1</div>
                <h3 class="text-xl font-semibold mb-3">Discovery & Planning</h3>
                <p class="text-gray-600">Understanding your requirements, target audience, and business goals to create a comprehensive project roadmap.</p>
            </div>

            <!-- Design & Prototyping -->
            <div class="text-center">
                <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">2</div>
                <h3 class="text-xl font-semibold mb-3">Design & Prototyping</h3>
                <p class="text-gray-600">Creating wireframes, mockups, and interactive prototypes to visualize the user experience and interface design.</p>
            </div>

            <!-- Development -->
            <div class="text-center">
                <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">3</div>
                <h3 class="text-xl font-semibold mb-3">Development</h3>
                <p class="text-gray-600">Building your app using agile methodology with regular updates and feedback incorporation throughout the process.</p>
            </div>

            <!-- Testing & Launch -->
            <div class="text-center">
                <div class="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">4</div>
                <h3 class="text-xl font-semibold mb-3">Testing & Launch</h3>
                <p class="text-gray-600">Comprehensive testing, app store submission, and post-launch support to ensure a successful market entry.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding gradient-bg">
    <div class="container-custom text-center">
        <div class="max-w-3xl mx-auto text-white">
            <h2 class="text-4xl md:text-5xl font-bold mb-6">Ready to Build Your Mobile App?</h2>
            <p class="text-xl mb-8 text-gray-200">
                Transform your idea into a powerful mobile app that engages users and drives business growth
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{{ route('contact') }}" class="bg-white text-primary-600 hover:bg-gray-100 font-semibold py-3 px-6 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    Start Your App Project
                </a>
                <a href="tel:+917010860889" class="btn-secondary border-white text-white hover:bg-white hover:text-primary-600">
                    Call Now: +91 7010860889
                </a>
            </div>
        </div>
    </div>
</section>
@endsection
