@echo off
REM MySQL Setup Script for 1 Crore Email Management System (Windows)
REM This script sets up MySQL with optimizations for massive email datasets

echo.
echo 🗄️ Setting up MySQL for 1 Crore Email Management System...
echo.

REM Configuration
set DB_NAME=bhavitech_emails
set DB_USER=bhavitech_user
set DB_PASSWORD=secure_password_2024
set ROOT_PASSWORD=root_password_2024

echo 📋 MySQL Configuration:
echo Database Name: %DB_NAME%
echo Database User: %DB_USER%
echo Root Password: [HIDDEN]
echo.

REM Check if MySQL is installed
echo 🔍 Checking MySQL installation...
mysql --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MySQL is already installed
    mysql --version
) else (
    echo ❌ MySQL is not installed
    echo Please install MySQL Server from: https://dev.mysql.com/downloads/mysql/
    echo After installation, run this script again.
    pause
    exit /b 1
)

echo.
echo 🗄️ Creating database and user...

REM Create database
mysql -u root -p%ROOT_PASSWORD% -e "CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;"
if %errorlevel% neq 0 (
    echo ❌ Failed to create database. Please check your root password.
    pause
    exit /b 1
)

REM Create user with proper privileges
mysql -u root -p%ROOT_PASSWORD% -e "CREATE USER IF NOT EXISTS '%DB_USER%'@'localhost' IDENTIFIED BY '%DB_PASSWORD%';"
mysql -u root -p%ROOT_PASSWORD% -e "GRANT ALL PRIVILEGES ON %DB_NAME%.* TO '%DB_USER%'@'localhost';"
mysql -u root -p%ROOT_PASSWORD% -e "FLUSH PRIVILEGES;"

echo ✅ Database and user created successfully
echo.

REM Test connection
echo 🔍 Testing database connection...
mysql -u %DB_USER% -p%DB_PASSWORD% -h localhost %DB_NAME% -e "SELECT 1;" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Database connection successful
) else (
    echo ❌ Database connection failed
    pause
    exit /b 1
)

echo.
echo 📋 Creating initial database tables...

REM Run Laravel migrations
php artisan migrate --force
if %errorlevel% equ 0 (
    echo ✅ Database tables created successfully
) else (
    echo ❌ Failed to create tables
    pause
    exit /b 1
)

echo.
echo 📊 Database Information:
echo Host: localhost
echo Port: 3306
echo Database: %DB_NAME%
echo Username: %DB_USER%
echo Password: %DB_PASSWORD%
echo.
echo 📝 Connection String for .env:
echo DB_CONNECTION=mysql
echo DB_HOST=127.0.0.1
echo DB_PORT=3306
echo DB_DATABASE=%DB_NAME%
echo DB_USERNAME=%DB_USER%
echo DB_PASSWORD=%DB_PASSWORD%
echo.
echo ✅ MySQL setup completed! Your system is ready to handle 1 crore emails.
echo.
pause
