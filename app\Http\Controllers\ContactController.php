<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Lead;
use App\Models\LeadInteraction;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class ContactController extends Controller
{
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'service_interest' => 'required|string|max:100',
            'budget' => 'nullable|string|max:50',
            'timeline' => 'nullable|string|max:50',
            'message' => 'required|string|max:2000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create or update lead
            $lead = Lead::updateOrCreate(
                ['email' => $request->email],
                [
                    'name' => $request->name,
                    'phone' => $request->phone,
                    'company' => $request->company,
                    'service_interest' => $request->service_interest,
                    'message' => $request->message,
                    'source' => 'website',
                    'metadata' => [
                        'budget' => $request->budget,
                        'timeline' => $request->timeline,
                        'ip_address' => $request->ip(),
                        'user_agent' => $request->userAgent(),
                        'referrer' => $request->header('referer'),
                        'utm_source' => $request->get('utm_source'),
                        'utm_medium' => $request->get('utm_medium'),
                        'utm_campaign' => $request->get('utm_campaign'),
                    ]
                ]
            );

            // Calculate and update lead score
            $lead->updateScore();

            // Create interaction record
            LeadInteraction::create([
                'lead_id' => $lead->id,
                'type' => 'form_submission',
                'channel' => 'website',
                'description' => 'Contact form submission',
                'metadata' => [
                    'form_type' => 'contact',
                    'service_interest' => $request->service_interest,
                    'budget' => $request->budget,
                    'timeline' => $request->timeline,
                    'message_length' => strlen($request->message),
                ],
                'interaction_date' => now(),
                'is_automated' => false,
            ]);

            // Send notification email to admin
            $this->sendAdminNotification($lead);

            // Send auto-response to lead
            $this->sendAutoResponse($lead);

            return response()->json([
                'success' => true,
                'message' => 'Thank you for your message. We will get back to you soon!'
            ]);

        } catch (\Exception $e) {
            Log::error('Contact form submission error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Sorry, there was an error processing your request. Please try again.'
            ], 500);
        }
    }

    public function storeQuote(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'company' => 'nullable|string|max:255',
            'projectType' => 'required|string|max:100',
            'projectDescription' => 'required|string|max:5000',
            'budget' => 'nullable|string|max:50',
            'timeline' => 'nullable|string|max:50',
            'additionalServices' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create or update lead
            $lead = Lead::updateOrCreate(
                ['email' => $request->email],
                [
                    'name' => $request->name,
                    'phone' => $request->phone,
                    'company' => $request->company,
                    'service_interest' => $request->projectType,
                    'message' => $request->projectDescription,
                    'source' => 'website',
                    'metadata' => [
                        'form_type' => 'quote',
                        'budget' => $request->budget,
                        'timeline' => $request->timeline,
                        'additional_services' => $request->additionalServices,
                        'ip_address' => $request->ip(),
                        'user_agent' => $request->userAgent(),
                        'referrer' => $request->header('referer'),
                    ]
                ]
            );

            // Calculate and update lead score (quote requests get higher scores)
            $score = $lead->calculateScore();
            $score += 20; // Bonus for quote request
            $lead->update(['score' => min($score, 100)]);

            // Create interaction record
            LeadInteraction::create([
                'lead_id' => $lead->id,
                'type' => 'quote_request',
                'channel' => 'website',
                'description' => 'Quote request form submission',
                'metadata' => [
                    'project_type' => $request->projectType,
                    'budget' => $request->budget,
                    'timeline' => $request->timeline,
                    'additional_services' => $request->additionalServices,
                ],
                'interaction_date' => now(),
                'is_automated' => false,
            ]);

            // Send notification email to admin
            $this->sendQuoteNotification($lead);

            // Send auto-response to lead
            $this->sendQuoteAutoResponse($lead);

            return response()->json([
                'success' => true,
                'message' => 'Thank you for your quote request. We will send you a detailed quote within 24 hours!'
            ]);

        } catch (\Exception $e) {
            Log::error('Quote form submission error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Sorry, there was an error processing your request. Please try again.'
            ], 500);
        }
    }

    private function sendAdminNotification(Lead $lead): void
    {
        try {
            Mail::send(new \App\Mail\AdminContactNotification($lead));
            Log::info('Admin notification sent for contact form submission from: ' . $lead->email);
        } catch (\Exception $e) {
            Log::error('Failed to send admin notification for lead: ' . $lead->id . ' - ' . $e->getMessage());
        }
    }

    private function sendAutoResponse(Lead $lead): void
    {
        try {
            Mail::send(new \App\Mail\ContactAutoResponse($lead));
            Log::info('Auto-response sent to: ' . $lead->email);
        } catch (\Exception $e) {
            Log::error('Failed to send auto-response to lead: ' . $lead->email . ' - ' . $e->getMessage());
        }
    }

    private function sendQuoteNotification(Lead $lead): void
    {
        try {
            Mail::send(new \App\Mail\AdminQuoteNotification($lead));
            Log::info('Quote notification sent to admin for: ' . $lead->email);
        } catch (\Exception $e) {
            Log::error('Failed to send quote notification for lead: ' . $lead->id . ' - ' . $e->getMessage());
        }
    }

    private function sendQuoteAutoResponse(Lead $lead): void
    {
        try {
            Mail::send(new \App\Mail\QuoteAutoResponse($lead));
            Log::info('Quote auto-response sent to: ' . $lead->email);
        } catch (\Exception $e) {
            Log::error('Failed to send quote auto-response to lead: ' . $lead->email . ' - ' . $e->getMessage());
        }
    }

    public function submit(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company' => 'nullable|string|max:255',
            'service_interest' => 'required|string',
            'budget' => 'nullable|string',
            'timeline' => 'nullable|string',
            'message' => 'required|string|max:2000',
        ]);

        try {
            // Create lead record
            $lead = Lead::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'phone' => $validated['phone'],
                'company' => $validated['company'],
                'source' => 'website_contact_form',
                'service_interest' => $validated['service_interest'],
                'message' => $validated['message'],
                'status' => 'new',
                'metadata' => [
                    'budget_range' => $validated['budget'],
                    'timeline' => $validated['timeline'],
                    'form_type' => 'contact_form',
                    'user_agent' => $request->userAgent(),
                    'ip_address' => $request->ip(),
                    'referrer' => $request->header('referer'),
                    'submitted_at' => now(),
                ],
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Thank you for your inquiry! We\'ll get back to you within 24 hours.',
                ]);
            }

            return redirect()->back()->with('success', 'Thank you for your inquiry! We\'ll get back to you within 24 hours.');

        } catch (\Exception $e) {
            Log::error('Contact form submission failed', [
                'error' => $e->getMessage(),
                'data' => $validated,
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while processing your request. Please try again.',
                ], 500);
            }

            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => 'An error occurred while processing your request. Please try again.']);
        }
    }
}
