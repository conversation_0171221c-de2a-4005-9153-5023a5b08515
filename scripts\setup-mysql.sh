#!/bin/bash

# MySQL Setup Script for 1 Crore Email Management System
# This script sets up MySQL with optimizations for massive email datasets

set -e

echo "🗄️ Setting up MySQL for 1 Crore Email Management System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_NAME="bhavitech_emails"
DB_USER="bhavitech_user"
DB_PASSWORD="secure_password_2024"
ROOT_PASSWORD="root_password_2024"

echo -e "${BLUE}📋 MySQL Configuration:${NC}"
echo "Database Name: $DB_NAME"
echo "Database User: $DB_USER"
echo "Root Password: [HIDDEN]"
echo ""

# Function to check if MySQL is installed
check_mysql() {
    if command -v mysql &> /dev/null; then
        echo -e "${GREEN}✅ MySQL is already installed${NC}"
        mysql --version
    else
        echo -e "${RED}❌ MySQL is not installed${NC}"
        install_mysql
    fi
}

# Function to install MySQL (Ubuntu/Debian)
install_mysql() {
    echo -e "${YELLOW}📦 Installing MySQL Server...${NC}"
    
    # Update package list
    sudo apt update
    
    # Install MySQL Server
    sudo apt install -y mysql-server mysql-client
    
    # Start MySQL service
    sudo systemctl start mysql
    sudo systemctl enable mysql
    
    echo -e "${GREEN}✅ MySQL installed successfully${NC}"
}

# Function to secure MySQL installation
secure_mysql() {
    echo -e "${YELLOW}🔒 Securing MySQL installation...${NC}"
    
    # Set root password and secure installation
    sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$ROOT_PASSWORD';"
    sudo mysql -u root -p$ROOT_PASSWORD -e "DELETE FROM mysql.user WHERE User='';"
    sudo mysql -u root -p$ROOT_PASSWORD -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');"
    sudo mysql -u root -p$ROOT_PASSWORD -e "DROP DATABASE IF EXISTS test;"
    sudo mysql -u root -p$ROOT_PASSWORD -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';"
    sudo mysql -u root -p$ROOT_PASSWORD -e "FLUSH PRIVILEGES;"
    
    echo -e "${GREEN}✅ MySQL secured successfully${NC}"
}

# Function to create database and user
create_database() {
    echo -e "${YELLOW}🗄️ Creating database and user...${NC}"
    
    # Create database
    mysql -u root -p$ROOT_PASSWORD -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;"
    
    # Create user with proper privileges
    mysql -u root -p$ROOT_PASSWORD -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
    mysql -u root -p$ROOT_PASSWORD -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';"
    mysql -u root -p$ROOT_PASSWORD -e "GRANT SELECT, INSERT, UPDATE, DELETE ON $DB_NAME.* TO '$DB_USER'@'%';"
    mysql -u root -p$ROOT_PASSWORD -e "FLUSH PRIVILEGES;"
    
    echo -e "${GREEN}✅ Database and user created successfully${NC}"
}

# Function to optimize MySQL for massive datasets
optimize_mysql() {
    echo -e "${YELLOW}⚡ Optimizing MySQL for massive email datasets...${NC}"
    
    # Create optimized MySQL configuration
    sudo tee /etc/mysql/mysql.conf.d/massive-email-optimization.cnf > /dev/null <<EOF
[mysqld]
# Basic Settings
default-storage-engine = innodb
sql_mode = NO_ENGINE_SUBSTITUTION,STRICT_TRANS_TABLES

# Connection Settings
max_connections = 1000
max_connect_errors = 1000000
connect_timeout = 60
wait_timeout = 28800
interactive_timeout = 28800

# Buffer Pool Settings (adjust based on available RAM)
innodb_buffer_pool_size = 2G
innodb_buffer_pool_instances = 8
innodb_log_file_size = 512M
innodb_log_buffer_size = 64M

# Performance Settings
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_io_capacity = 1000
innodb_io_capacity_max = 2000
innodb_read_io_threads = 8
innodb_write_io_threads = 8

# Query Cache Settings
query_cache_type = 1
query_cache_size = 256M
query_cache_limit = 64M

# Temporary Tables
tmp_table_size = 512M
max_heap_table_size = 512M

# MyISAM Settings
key_buffer_size = 256M
myisam_sort_buffer_size = 128M

# Logging
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1

# Binary Logging (for replication)
log_bin = /var/log/mysql/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# Character Set
character-set-server = utf8mb4
collation-server = utf8mb4_0900_ai_ci

# Security
local_infile = 0
EOF

    echo -e "${GREEN}✅ MySQL optimization configuration created${NC}"
}

# Function to restart MySQL
restart_mysql() {
    echo -e "${YELLOW}🔄 Restarting MySQL with new configuration...${NC}"
    
    sudo systemctl restart mysql
    
    # Wait for MySQL to start
    sleep 5
    
    # Check if MySQL is running
    if sudo systemctl is-active --quiet mysql; then
        echo -e "${GREEN}✅ MySQL restarted successfully${NC}"
    else
        echo -e "${RED}❌ Failed to restart MySQL${NC}"
        exit 1
    fi
}

# Function to test connection
test_connection() {
    echo -e "${YELLOW}🔍 Testing database connection...${NC}"
    
    if mysql -u $DB_USER -p$DB_PASSWORD -h localhost $DB_NAME -e "SELECT 1;" &> /dev/null; then
        echo -e "${GREEN}✅ Database connection successful${NC}"
    else
        echo -e "${RED}❌ Database connection failed${NC}"
        exit 1
    fi
}

# Function to create initial tables
create_tables() {
    echo -e "${YELLOW}📋 Creating initial database tables...${NC}"
    
    # Navigate to Laravel project directory
    cd "$(dirname "$0")/.."
    
    # Run Laravel migrations
    php artisan migrate --force
    
    echo -e "${GREEN}✅ Database tables created successfully${NC}"
}

# Function to show database info
show_info() {
    echo -e "${BLUE}📊 Database Information:${NC}"
    echo "Host: localhost"
    echo "Port: 3306"
    echo "Database: $DB_NAME"
    echo "Username: $DB_USER"
    echo "Password: $DB_PASSWORD"
    echo ""
    echo -e "${BLUE}📝 Connection String for .env:${NC}"
    echo "DB_CONNECTION=mysql"
    echo "DB_HOST=127.0.0.1"
    echo "DB_PORT=3306"
    echo "DB_DATABASE=$DB_NAME"
    echo "DB_USERNAME=$DB_USER"
    echo "DB_PASSWORD=$DB_PASSWORD"
    echo ""
    echo -e "${GREEN}🎉 MySQL setup completed successfully!${NC}"
}

# Main execution
main() {
    echo -e "${BLUE}🚀 Starting MySQL setup for 1 Crore Email Management System...${NC}"
    echo ""
    
    check_mysql
    secure_mysql
    create_database
    optimize_mysql
    restart_mysql
    test_connection
    create_tables
    show_info
    
    echo ""
    echo -e "${GREEN}✅ MySQL setup completed! Your system is ready to handle 1 crore emails.${NC}"
}

# Run main function
main "$@"
