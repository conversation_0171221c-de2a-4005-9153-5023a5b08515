<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('whatsapp_campaigns', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('message');
            $table->string('type')->default('broadcast'); // broadcast, drip, automated
            $table->string('status')->default('draft'); // draft, scheduled, sending, sent, paused
            $table->json('recipients')->nullable(); // Phone numbers or contact segments
            $table->string('sender_number')->nullable(); // WhatsApp Business number
            $table->json('media')->nullable(); // Images, documents, etc.
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->integer('total_recipients')->default(0);
            $table->integer('delivered_count')->default(0);
            $table->integer('read_count')->default(0);
            $table->integer('replied_count')->default(0);
            $table->integer('failed_count')->default(0);
            $table->decimal('delivery_rate', 5, 2)->default(0);
            $table->decimal('read_rate', 5, 2)->default(0);
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();

            $table->index(['status', 'scheduled_at']);
            $table->index(['type', 'created_at']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('whatsapp_campaigns');
    }
};
