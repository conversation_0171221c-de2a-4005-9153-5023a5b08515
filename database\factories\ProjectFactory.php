<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Project>
 */
class ProjectFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('-6 months', '+1 month');
        $endDate = $this->faker->dateTimeBetween($startDate, '+6 months');

        return [
            'customer_id' => User::where('role', 'customer')->inRandomOrder()->first()?->id ?? User::factory()->create(['role' => 'customer'])->id,
            'project_manager_id' => User::where('role', 'user')->inRandomOrder()->first()?->id,
            'name' => $this->faker->randomElement([
                'E-commerce Website Development',
                'Mobile App Development',
                'Digital Marketing Campaign',
                'Brand Identity Design',
                'CRM System Integration',
                'Website Redesign Project',
                'SEO Optimization',
                'Social Media Management',
            ]),
            'description' => $this->faker->paragraph(3),
            'type' => $this->faker->randomElement(['web_development', 'mobile_development', 'digital_marketing', 'graphic_design', 'consultation']),
            'status' => $this->faker->randomElement(['planning', 'in_progress', 'on_hold', 'completed', 'cancelled']),
            'priority' => $this->faker->randomElement(['low', 'medium', 'high', 'urgent']),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'estimated_hours' => $this->faker->numberBetween(20, 500),
            'actual_hours' => $this->faker->numberBetween(15, 600),
            'budget' => $this->faker->numberBetween(25000, 500000),
            'total_cost' => $this->faker->numberBetween(20000, 550000),
            'progress_percentage' => $this->faker->numberBetween(0, 100),
            'metadata' => [
                'technologies' => $this->faker->randomElements(['Laravel', 'React', 'Vue.js', 'Node.js', 'PHP', 'MySQL', 'MongoDB'], 3),
                'requirements' => $this->faker->sentences(3),
                'deliverables' => $this->faker->sentences(2),
            ],
        ];
    }
}
