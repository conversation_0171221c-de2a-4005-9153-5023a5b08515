<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;

class WhatsAppNumber extends Model
{
    use HasFactory;

    protected $table = 'whatsapp_numbers';

    protected $fillable = [
        'name',
        'phone_number',
        'phone_number_id',
        'access_token',
        'webhook_verify_token',
        'is_active',
        'is_primary',
        'daily_message_limit',
        'messages_sent_today',
        'last_message_sent_at',
        'last_reset_at',
        'business_profile',
        'status',
        'status_message',
        'capabilities',
        'cost_per_message',
    ];

    protected $casts = [
        'business_profile' => 'array',
        'capabilities' => 'array',
        'is_active' => 'boolean',
        'is_primary' => 'boolean',
        'cost_per_message' => 'decimal:4',
        'last_message_sent_at' => 'datetime',
        'last_reset_at' => 'datetime',
    ];

    // Relationships
    public function messages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class, 'whatsapp_number_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('status', 'active');
    }

    public function scopePrimary($query)
    {
        return $query->where('is_primary', true);
    }

    public function scopeAvailable($query)
    {
        return $query->active()->whereRaw('messages_sent_today < daily_message_limit');
    }

    // Accessors & Mutators
    protected function accessToken(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? decrypt($value) : null,
            set: fn ($value) => $value ? encrypt($value) : null,
        );
    }

    // Methods
    public function canSendMessage(): bool
    {
        return $this->is_active &&
               $this->status === 'active' &&
               $this->messages_sent_today < $this->daily_message_limit;
    }

    public function getRemainingMessages(): int
    {
        return max(0, $this->daily_message_limit - $this->messages_sent_today);
    }

    public function incrementMessageCount(): void
    {
        $this->increment('messages_sent_today');
        $this->update(['last_message_sent_at' => now()]);
    }

    public function resetDailyCount(): void
    {
        $this->update([
            'messages_sent_today' => 0,
            'last_reset_at' => now(),
        ]);
    }

    public function shouldResetCount(): bool
    {
        return !$this->last_reset_at || $this->last_reset_at->isYesterday();
    }

    public function updateStatus(string $status, string $message = null): void
    {
        $this->update([
            'status' => $status,
            'status_message' => $message,
        ]);
    }

    public static function getNextAvailable(): ?self
    {
        return static::available()->orderBy('messages_sent_today')->first();
    }

    public static function getPrimary(): ?self
    {
        return static::primary()->active()->first();
    }
}
