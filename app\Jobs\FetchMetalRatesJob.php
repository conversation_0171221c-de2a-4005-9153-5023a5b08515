<?php

namespace App\Jobs;

use App\Services\MetalRatesService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class FetchMetalRatesJob implements ShouldQueue
{
    use Queueable, InteractsWithQueue, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;

    protected ?array $metals;
    protected bool $checkAlerts;
    protected bool $broadcastUpdates;

    /**
     * Create a new job instance.
     */
    public function __construct(?array $metals = null, bool $checkAlerts = true, bool $broadcastUpdates = false)
    {
        $this->metals = $metals;
        $this->checkAlerts = $checkAlerts;
        $this->broadcastUpdates = $broadcastUpdates;
    }

    /**
     * Execute the job.
     */
    public function handle(MetalRatesService $metalRatesService): void
    {
        try {
            Log::info('Starting metal rates fetch job', [
                'metals' => $this->metals,
                'check_alerts' => $this->checkAlerts,
                'broadcast_updates' => $this->broadcastUpdates,
            ]);

            // Fetch current rates
            $result = $metalRatesService->fetchCurrentRates($this->metals);

            if (!$result['success']) {
                throw new \Exception('Failed to fetch metal rates');
            }

            Log::info('Metal rates fetched successfully', [
                'provider' => $result['provider'],
                'metals_count' => count($result['rates']),
                'timestamp' => $result['timestamp'],
            ]);

            // Check alerts if enabled
            if ($this->checkAlerts) {
                $triggeredAlerts = $metalRatesService->checkAlerts();

                if (!empty($triggeredAlerts)) {
                    Log::info('Metal rate alerts triggered', [
                        'alerts_count' => count($triggeredAlerts),
                    ]);
                }
            }

            // Broadcast updates if enabled
            if ($this->broadcastUpdates) {
                $broadcastResult = $metalRatesService->broadcastRateUpdates();

                Log::info('Metal rate updates broadcasted', [
                    'total_subscriptions' => $broadcastResult['total_subscriptions'],
                    'updates_sent' => $broadcastResult['updates_sent'],
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Metal rates fetch job failed', [
                'metals' => $this->metals,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Metal rates fetch job failed permanently', [
            'metals' => $this->metals,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);
    }
}
