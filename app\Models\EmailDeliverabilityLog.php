<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmailDeliverabilityLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'email_account_id',
        'contact_id',
        'email_campaign_id',
        'email_address',
        'status',
        'bounce_type',
        'bounce_reason',
        'spam_score',
        'delivery_time',
        'opened_at',
        'clicked_at',
        'unsubscribed_at',
        'complained_at',
        'metadata',
    ];

    protected $casts = [
        'delivery_time' => 'datetime',
        'opened_at' => 'datetime',
        'clicked_at' => 'datetime',
        'unsubscribed_at' => 'datetime',
        'complained_at' => 'datetime',
        'metadata' => 'array',
        'spam_score' => 'decimal:2',
    ];

    public function emailAccount(): BelongsTo
    {
        return $this->belongsTo(EmailAccount::class);
    }

    public function contact(): BelongsTo
    {
        return $this->belongsTo(Contact::class);
    }

    public function emailCampaign(): BelongsTo
    {
        return $this->belongsTo(EmailCampaign::class);
    }

    // Scopes for different email statuses
    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    public function scopeBounced($query)
    {
        return $query->where('status', 'bounced');
    }

    public function scopeOpened($query)
    {
        return $query->whereNotNull('opened_at');
    }

    public function scopeClicked($query)
    {
        return $query->whereNotNull('clicked_at');
    }

    public function scopeUnsubscribed($query)
    {
        return $query->whereNotNull('unsubscribed_at');
    }

    public function scopeComplained($query)
    {
        return $query->whereNotNull('complained_at');
    }

    public function scopeHighSpamScore($query)
    {
        return $query->where('spam_score', '>', 5.0);
    }

    // Calculate deliverability metrics
    public static function getDeliverabilityRate(EmailAccount $account, $days = 30): float
    {
        $total = static::where('email_account_id', $account->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->count();

        if ($total === 0) return 0;

        $delivered = static::where('email_account_id', $account->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->delivered()
            ->count();

        return ($delivered / $total) * 100;
    }

    public static function getBounceRate(EmailAccount $account, $days = 30): float
    {
        $total = static::where('email_account_id', $account->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->count();

        if ($total === 0) return 0;

        $bounced = static::where('email_account_id', $account->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->bounced()
            ->count();

        return ($bounced / $total) * 100;
    }

    public static function getOpenRate(EmailAccount $account, $days = 30): float
    {
        $delivered = static::where('email_account_id', $account->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->delivered()
            ->count();

        if ($delivered === 0) return 0;

        $opened = static::where('email_account_id', $account->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->opened()
            ->count();

        return ($opened / $delivered) * 100;
    }

    public static function getClickRate(EmailAccount $account, $days = 30): float
    {
        $delivered = static::where('email_account_id', $account->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->delivered()
            ->count();

        if ($delivered === 0) return 0;

        $clicked = static::where('email_account_id', $account->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->clicked()
            ->count();

        return ($clicked / $delivered) * 100;
    }

    public static function getComplaintRate(EmailAccount $account, $days = 30): float
    {
        $delivered = static::where('email_account_id', $account->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->delivered()
            ->count();

        if ($delivered === 0) return 0;

        $complained = static::where('email_account_id', $account->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->complained()
            ->count();

        return ($complained / $delivered) * 100;
    }

    public static function getAverageSpamScore(EmailAccount $account, $days = 30): float
    {
        return static::where('email_account_id', $account->id)
            ->where('created_at', '>=', now()->subDays($days))
            ->whereNotNull('spam_score')
            ->avg('spam_score') ?? 0;
    }

    // Check if account needs attention based on metrics
    public static function needsAttention(EmailAccount $account): array
    {
        $issues = [];
        
        $bounceRate = static::getBounceRate($account);
        $complaintRate = static::getComplaintRate($account);
        $avgSpamScore = static::getAverageSpamScore($account);
        $deliverabilityRate = static::getDeliverabilityRate($account);

        if ($bounceRate > 5) {
            $issues[] = "High bounce rate: {$bounceRate}%";
        }

        if ($complaintRate > 0.1) {
            $issues[] = "High complaint rate: {$complaintRate}%";
        }

        if ($avgSpamScore > 5) {
            $issues[] = "High spam score: {$avgSpamScore}";
        }

        if ($deliverabilityRate < 95) {
            $issues[] = "Low deliverability: {$deliverabilityRate}%";
        }

        return $issues;
    }
}
