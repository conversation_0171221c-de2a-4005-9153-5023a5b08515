<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('email');
            $table->string('company')->nullable()->after('phone');
            $table->enum('role', ['admin', 'customer', 'user'])->default('customer')->after('company');
            $table->text('address')->nullable()->after('role');
            $table->string('city')->nullable()->after('address');
            $table->string('state')->nullable()->after('city');
            $table->string('postal_code')->nullable()->after('state');
            $table->string('country')->nullable()->after('postal_code');
            $table->timestamp('last_login_at')->nullable()->after('email_verified_at');
            $table->string('timezone')->default('Asia/Kolkata')->after('last_login_at');
            $table->json('preferences')->nullable()->after('timezone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'phone',
                'company',
                'role',
                'address',
                'city',
                'state',
                'postal_code',
                'country',
                'last_login_at',
                'timezone',
                'preferences'
            ]);
        });
    }
};
