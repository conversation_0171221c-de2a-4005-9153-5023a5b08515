@extends('layouts.customer')

@section('title', $project->name . ' - Project Details')

@section('content')
<div class="space-y-6">
    <!-- Breadcrumb -->
    <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
            <li>
                <div>
                    <a href="{{ route('customer.dashboard') }}" class="text-gray-400 hover:text-gray-500">
                        <svg class="flex-shrink-0 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                        <span class="sr-only">Dashboard</span>
                    </a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                    <a href="{{ route('customer.projects') }}" class="ml-4 text-sm font-medium text-gray-500 hover:text-gray-700">Projects</a>
                </div>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="flex-shrink-0 h-5 w-5 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                    <span class="ml-4 text-sm font-medium text-gray-500">{{ $project->name }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Project Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-start justify-between">
            <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                    <h1 class="text-2xl font-bold text-gray-900">{{ $project->name }}</h1>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                        @if($project->status === 'completed') bg-green-100 text-green-800
                        @elseif($project->status === 'in_progress') bg-blue-100 text-blue-800
                        @elseif($project->status === 'on_hold') bg-yellow-100 text-yellow-800
                        @elseif($project->status === 'planning') bg-purple-100 text-purple-800
                        @else bg-gray-100 text-gray-800
                        @endif">
                        {{ ucfirst(str_replace('_', ' ', $project->status)) }}
                    </span>
                </div>
                <p class="text-gray-600">{{ $project->description }}</p>
            </div>
            <div class="ml-6">
                <a href="{{ route('customer.projects') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Projects
                </a>
            </div>
        </div>
    </div>

    <!-- Project Progress -->
    @if($project->progress_percentage !== null)
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Progress</h3>
        <div class="flex items-center justify-between text-sm mb-2">
            <span class="text-gray-600">Overall Completion</span>
            <span class="font-medium text-gray-900">{{ $project->progress_percentage }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-3">
            <div class="bg-primary-600 h-3 rounded-full transition-all duration-500" style="width: {{ $project->progress_percentage }}%"></div>
        </div>
        @if($project->progress_percentage < 100)
        <p class="text-sm text-gray-500 mt-2">
            @if($project->end_date)
                Expected completion: {{ $project->end_date->format('M d, Y') }}
            @else
                Project is in progress
            @endif
        </p>
        @endif
    </div>
    @endif

    <!-- Project Details Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Project Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Project Details -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Project Type</label>
                        <p class="text-gray-900">{{ ucfirst($project->type ?? 'General') }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Priority</label>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                            @if($project->priority === 'urgent') bg-red-100 text-red-800
                            @elseif($project->priority === 'high') bg-orange-100 text-orange-800
                            @elseif($project->priority === 'medium') bg-yellow-100 text-yellow-800
                            @else bg-gray-100 text-gray-800
                            @endif">
                            {{ ucfirst($project->priority ?? 'Medium') }}
                        </span>
                    </div>
                    @if($project->start_date)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Start Date</label>
                        <p class="text-gray-900">{{ $project->start_date->format('M d, Y') }}</p>
                    </div>
                    @endif
                    @if($project->end_date)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Due Date</label>
                        <p class="text-gray-900">{{ $project->end_date->format('M d, Y') }}</p>
                    </div>
                    @endif
                    @if($project->estimated_hours)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Estimated Hours</label>
                        <p class="text-gray-900">{{ $project->estimated_hours }} hours</p>
                    </div>
                    @endif
                    @if($project->actual_hours)
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Actual Hours</label>
                        <p class="text-gray-900">{{ $project->actual_hours }} hours</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Project Milestones -->
            @if($project->milestones && $project->milestones->count() > 0)
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Milestones</h3>
                <div class="space-y-4">
                    @foreach($project->milestones as $milestone)
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0 mt-1">
                            @if($milestone->is_completed)
                                <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                    <svg class="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                            @else
                                <div class="w-4 h-4 border-2 border-gray-300 rounded-full"></div>
                            @endif
                        </div>
                        <div class="flex-1">
                            <h4 class="text-sm font-medium text-gray-900">{{ $milestone->name }}</h4>
                            @if($milestone->description)
                            <p class="text-sm text-gray-600 mt-1">{{ $milestone->description }}</p>
                            @endif
                            @if($milestone->due_date)
                            <p class="text-xs text-gray-500 mt-1">Due: {{ $milestone->due_date->format('M d, Y') }}</p>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Project Updates -->
            @if($project->updates && $project->updates->count() > 0)
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Updates</h3>
                <div class="space-y-4">
                    @foreach($project->updates->take(5) as $update)
                    <div class="border-l-4 border-primary-500 pl-4">
                        <h4 class="text-sm font-medium text-gray-900">{{ $update->title }}</h4>
                        <p class="text-sm text-gray-600 mt-1">{{ $update->description }}</p>
                        <p class="text-xs text-gray-500 mt-2">{{ $update->created_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Project Team -->
            @if($project->projectManager || ($project->teamMembers && $project->teamMembers->count() > 0))
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Project Team</h3>
                <div class="space-y-3">
                    @if($project->projectManager)
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                            <span class="text-sm font-medium text-primary-600">{{ substr($project->projectManager->name, 0, 1) }}</span>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-900">{{ $project->projectManager->name }}</p>
                            <p class="text-xs text-gray-500">Project Manager</p>
                        </div>
                    </div>
                    @endif
                    @if($project->teamMembers)
                        @foreach($project->teamMembers as $member)
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                <span class="text-sm font-medium text-gray-600">{{ substr($member->user->name, 0, 1) }}</span>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900">{{ $member->user->name }}</p>
                                <p class="text-xs text-gray-500">{{ $member->role ?? 'Team Member' }}</p>
                            </div>
                        </div>
                        @endforeach
                    @endif
                </div>
            </div>
            @endif

            <!-- Project Budget -->
            @if($project->budget || $project->total_cost)
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Budget Information</h3>
                <div class="space-y-3">
                    @if($project->budget)
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Budget:</span>
                        <span class="text-sm font-medium text-gray-900">₹{{ number_format($project->budget) }}</span>
                    </div>
                    @endif
                    @if($project->total_cost)
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Cost:</span>
                        <span class="text-sm font-medium text-gray-900">₹{{ number_format($project->total_cost) }}</span>
                    </div>
                    @endif
                    @if($project->budget && $project->total_cost)
                    <div class="pt-2 border-t border-gray-100">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Remaining:</span>
                            <span class="text-sm font-medium {{ $project->budget - $project->total_cost >= 0 ? 'text-green-600' : 'text-red-600' }}">
                                ₹{{ number_format($project->budget - $project->total_cost) }}
                            </span>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-3">
                    <a href="{{ route('customer.support') }}" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        Get Support
                    </a>
                    @if($project->invoices && $project->invoices->count() > 0)
                    <a href="{{ route('customer.invoices') }}" 
                       class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        View Invoices
                    </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
