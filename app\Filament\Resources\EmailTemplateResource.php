<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EmailTemplateResource\Pages;
use App\Models\EmailTemplate;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Database\Eloquent\Builder;
use Filament\Tables\Actions\BulkAction;
use Illuminate\Database\Eloquent\Collection;
use Filament\Notifications\Notification;

class EmailTemplateResource extends Resource
{
    protected static ?string $model = EmailTemplate::class;
    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationGroup = 'Email Marketing';
    protected static ?int $navigationSort = 3;
    protected static ?string $recordTitleAttribute = 'name';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Template Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255)
                            ->unique(ignoreRecord: true),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(500)
                            ->rows(2),
                        Forms\Components\Select::make('category')
                            ->options([
                                'newsletter' => 'Newsletter',
                                'promotional' => 'Promotional',
                                'transactional' => 'Transactional',
                                'welcome' => 'Welcome Series',
                                'follow_up' => 'Follow Up',
                                'announcement' => 'Announcement',
                                'event' => 'Event Invitation',
                                'survey' => 'Survey/Feedback',
                                'abandoned_cart' => 'Abandoned Cart',
                                're_engagement' => 'Re-engagement',
                            ])
                            ->required(),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true)
                            ->helperText('Enable this template for use in campaigns'),
                    ])->columns(2),

                Forms\Components\Section::make('Email Content')
                    ->schema([
                        Forms\Components\TextInput::make('subject')
                            ->required()
                            ->maxLength(255)
                            ->helperText('Use {{first_name}}, {{company}}, etc. for personalization'),
                        Forms\Components\TextInput::make('preview_text')
                            ->maxLength(255)
                            ->helperText('Text that appears in email client preview'),
                        Forms\Components\RichEditor::make('content')
                            ->required()
                            ->columnSpanFull()
                            ->toolbarButtons([
                                'bold',
                                'italic',
                                'underline',
                                'strike',
                                'link',
                                'bulletList',
                                'orderedList',
                                'h2',
                                'h3',
                                'blockquote',
                                'codeBlock',
                            ])
                            ->helperText('Available variables: {{first_name}}, {{last_name}}, {{email}}, {{company}}, {{unsubscribe_url}}'),
                    ]),

                Forms\Components\Section::make('Design Settings')
                    ->schema([
                        Forms\Components\Select::make('layout')
                            ->options([
                                'single_column' => 'Single Column',
                                'two_column' => 'Two Column',
                                'three_column' => 'Three Column',
                                'sidebar' => 'Sidebar Layout',
                                'newsletter' => 'Newsletter Style',
                            ])
                            ->default('single_column'),
                        Forms\Components\ColorPicker::make('primary_color')
                            ->default('#3B82F6'),
                        Forms\Components\ColorPicker::make('secondary_color')
                            ->default('#6B7280'),
                        Forms\Components\ColorPicker::make('background_color')
                            ->default('#FFFFFF'),
                        Forms\Components\Select::make('font_family')
                            ->options([
                                'Arial, sans-serif' => 'Arial',
                                'Helvetica, sans-serif' => 'Helvetica',
                                'Georgia, serif' => 'Georgia',
                                'Times New Roman, serif' => 'Times New Roman',
                                'Verdana, sans-serif' => 'Verdana',
                            ])
                            ->default('Arial, sans-serif'),
                        Forms\Components\TextInput::make('font_size')
                            ->numeric()
                            ->default(14)
                            ->suffix('px'),
                    ])->columns(3),

                Forms\Components\Section::make('Advanced Settings')
                    ->schema([
                        Forms\Components\Toggle::make('track_opens')
                            ->label('Track Email Opens')
                            ->default(true),
                        Forms\Components\Toggle::make('track_clicks')
                            ->label('Track Link Clicks')
                            ->default(true),
                        Forms\Components\Toggle::make('include_unsubscribe')
                            ->label('Include Unsubscribe Link')
                            ->default(true),
                        Forms\Components\TagsInput::make('tags')
                            ->placeholder('Add tags for organization...'),
                        Forms\Components\KeyValue::make('custom_variables')
                            ->keyLabel('Variable Name')
                            ->valueLabel('Default Value')
                            ->addActionLabel('Add Custom Variable'),
                    ])->columns(2),

                Forms\Components\Section::make('A/B Testing')
                    ->schema([
                        Forms\Components\Toggle::make('enable_ab_testing')
                            ->label('Enable A/B Testing')
                            ->reactive(),
                        Forms\Components\TextInput::make('variant_b_subject')
                            ->label('Variant B Subject')
                            ->maxLength(255)
                            ->visible(fn (Forms\Get $get): bool => $get('enable_ab_testing')),
                        Forms\Components\RichEditor::make('variant_b_content')
                            ->label('Variant B Content')
                            ->visible(fn (Forms\Get $get): bool => $get('enable_ab_testing')),
                        Forms\Components\TextInput::make('test_percentage')
                            ->label('Test Percentage')
                            ->numeric()
                            ->minValue(10)
                            ->maxValue(50)
                            ->default(20)
                            ->suffix('%')
                            ->visible(fn (Forms\Get $get): bool => $get('enable_ab_testing')),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->weight('bold'),
                Tables\Columns\TextColumn::make('subject')
                    ->searchable()
                    ->limit(50),
                Tables\Columns\BadgeColumn::make('category')
                    ->colors([
                        'primary' => 'newsletter',
                        'success' => 'promotional',
                        'warning' => 'transactional',
                        'info' => 'welcome',
                        'secondary' => 'follow_up',
                    ]),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('usage_count')
                    ->label('Used')
                    ->getStateUsing(fn (EmailTemplate $record): int => 
                        $record->emailCampaigns()->count()
                    )
                    ->badge()
                    ->color('primary'),
                Tables\Columns\IconColumn::make('track_opens')
                    ->boolean()
                    ->toggleable(),
                Tables\Columns\IconColumn::make('track_clicks')
                    ->boolean()
                    ->toggleable(),
                Tables\Columns\IconColumn::make('enable_ab_testing')
                    ->label('A/B Test')
                    ->boolean()
                    ->toggleable(),
                Tables\Columns\TagsColumn::make('tags')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime('M j, Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'newsletter' => 'Newsletter',
                        'promotional' => 'Promotional',
                        'transactional' => 'Transactional',
                        'welcome' => 'Welcome',
                        'follow_up' => 'Follow Up',
                        'announcement' => 'Announcement',
                        'event' => 'Event',
                        'survey' => 'Survey',
                        'abandoned_cart' => 'Abandoned Cart',
                        're_engagement' => 'Re-engagement',
                    ]),
                Tables\Filters\Filter::make('is_active')
                    ->label('Active Templates')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', true)),
                Tables\Filters\Filter::make('inactive')
                    ->label('Inactive Templates')
                    ->query(fn (Builder $query): Builder => $query->where('is_active', false)),
                Tables\Filters\Filter::make('ab_testing')
                    ->query(fn (Builder $query): Builder => $query->where('enable_ab_testing', true))
                    ->label('A/B Testing Enabled'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('preview')
                    ->label('Preview')
                    ->icon('heroicon-o-eye')
                    ->color('secondary')
                    ->url(fn (EmailTemplate $record): string => 
                        route('admin.email-templates.preview', $record)
                    )
                    ->openUrlInNewTab(),
                Tables\Actions\Action::make('duplicate')
                    ->label('Duplicate')
                    ->icon('heroicon-o-document-duplicate')
                    ->color('gray')
                    ->action(function (EmailTemplate $record): void {
                        $duplicate = $record->replicate();
                        $duplicate->name = $record->name . ' (Copy)';
                        $duplicate->is_active = false;
                        $duplicate->save();
                        
                        Notification::make()
                            ->title('Template duplicated successfully')
                            ->success()
                            ->send();
                    }),
                Tables\Actions\Action::make('create_campaign')
                    ->label('Create Campaign')
                    ->icon('heroicon-o-paper-airplane')
                    ->color('primary')
                    ->url(fn (EmailTemplate $record): string => 
                        route('filament.admin.resources.email-campaigns.create', ['template' => $record->id])
                    ),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkAction::make('activate')
                        ->label('Activate')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->action(function (Collection $records): void {
                            $records->each->update(['status' => 'active']);
                            Notification::make()
                                ->title('Templates activated successfully')
                                ->success()
                                ->send();
                        }),
                    BulkAction::make('archive')
                        ->label('Archive')
                        ->icon('heroicon-o-archive-box')
                        ->color('warning')
                        ->action(function (Collection $records): void {
                            $records->each->update(['status' => 'archived']);
                            Notification::make()
                                ->title('Templates archived successfully')
                                ->success()
                                ->send();
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Template Overview')
                    ->schema([
                        Infolists\Components\TextEntry::make('name'),
                        Infolists\Components\TextEntry::make('description'),
                        Infolists\Components\TextEntry::make('category')
                            ->badge(),
                        Infolists\Components\TextEntry::make('status')
                            ->badge(),
                        Infolists\Components\TextEntry::make('subject'),
                        Infolists\Components\TextEntry::make('preview_text'),
                    ])->columns(3),

                Infolists\Components\Section::make('Content Preview')
                    ->schema([
                        Infolists\Components\TextEntry::make('content')
                            ->html()
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Design Settings')
                    ->schema([
                        Infolists\Components\TextEntry::make('layout'),
                        Infolists\Components\ColorEntry::make('primary_color'),
                        Infolists\Components\ColorEntry::make('secondary_color'),
                        Infolists\Components\TextEntry::make('font_family'),
                        Infolists\Components\TextEntry::make('font_size')
                            ->suffix('px'),
                    ])->columns(3),

                Infolists\Components\Section::make('Usage Statistics')
                    ->schema([
                        Infolists\Components\TextEntry::make('campaigns_count')
                            ->label('Campaigns Created')
                            ->getStateUsing(fn (EmailTemplate $record): int => 
                                $record->emailCampaigns()->count()
                            ),
                        Infolists\Components\TextEntry::make('total_sent')
                            ->label('Total Emails Sent')
                            ->getStateUsing(fn (EmailTemplate $record): int => 
                                $record->emailCampaigns()->sum('sent_count')
                            ),
                        Infolists\Components\TextEntry::make('avg_open_rate')
                            ->label('Average Open Rate')
                            ->getStateUsing(fn (EmailTemplate $record): string => 
                                number_format($record->getAverageOpenRate(), 2) . '%'
                            ),
                    ])->columns(3),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmailTemplates::route('/'),
            'create' => Pages\CreateEmailTemplate::route('/create'),
            'view' => Pages\ViewEmailTemplate::route('/{record}'),
            'edit' => Pages\EditEmailTemplate::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('is_active', true)->count() ?: null;
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'success';
    }
}
