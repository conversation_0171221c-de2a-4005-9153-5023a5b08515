<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('metal_rates', function (Blueprint $table) {
            $table->id();
            $table->enum('metal_type', ['gold', 'silver', 'platinum', 'palladium', 'copper']);
            $table->enum('purity', ['24k', '22k', '18k', '14k', '10k', '999', '925', '900'])->nullable();
            $table->enum('unit', ['gram', 'ounce', 'kg', 'tola', 'sovereign']);
            $table->string('currency', 3)->default('INR'); // ISO currency code
            $table->decimal('buy_price', 10, 2);
            $table->decimal('sell_price', 10, 2);
            $table->decimal('spot_price', 10, 2)->nullable(); // International spot price
            $table->string('source')->nullable(); // API source or manual
            $table->string('market')->default('domestic'); // domestic, international
            $table->string('location')->nullable(); // City/region specific rates
            $table->decimal('change_amount', 10, 2)->nullable(); // Price change
            $table->decimal('change_percentage', 5, 2)->nullable(); // Percentage change
            $table->timestamp('rate_date'); // Date/time of the rate
            $table->boolean('is_active')->default(true);
            $table->json('additional_data')->nullable(); // Extra rate information
            $table->timestamps();

            $table->unique(['metal_type', 'purity', 'unit', 'currency', 'market', 'rate_date'], 'metal_rates_unique_idx');
            $table->index(['metal_type', 'rate_date']);
            $table->index(['is_active', 'rate_date']);
            $table->index('location');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('metal_rates');
    }
};
