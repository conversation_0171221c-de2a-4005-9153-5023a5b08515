<?php

namespace App\Services;

use App\Models\EmailCampaign;
use App\Models\EmailTemplate;
use App\Models\EmailAccount;
use App\Models\EmailCampaignRecipient;
use App\Models\Lead;
use App\Models\Contact;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class EmailMarketingService
{
    public function createCampaign(array $data): EmailCampaign
    {
        $campaign = EmailCampaign::create([
            'name' => $data['name'],
            'subject' => $data['subject'],
            'content' => $data['content'],
            'template_id' => $data['template_id'] ?? null,
            'status' => 'draft',
            'settings' => $data['settings'] ?? [],
            'ab_test_config' => $data['ab_test_config'] ?? null,
            'created_by' => auth()->id(),
        ]);

        // Add recipients
        if (isset($data['recipients'])) {
            $this->addRecipientsToCampaign($campaign, $data['recipients']);
        }

        return $campaign;
    }

    public function addRecipientsToCampaign(EmailCampaign $campaign, array $recipients): int
    {
        $added = 0;

        foreach ($recipients as $recipient) {
            $existing = $campaign->recipients()
                ->where('email', $recipient['email'])
                ->first();

            if (!$existing) {
                $campaign->recipients()->create([
                    'email' => $recipient['email'],
                    'name' => $recipient['name'] ?? null,
                    'personalization_data' => $recipient['data'] ?? [],
                ]);
                $added++;
            }
        }

        $campaign->update(['recipient_count' => $campaign->recipients()->count()]);

        return $added;
    }

    public function addLeadsToCampaign(EmailCampaign $campaign, array $leadIds = null): int
    {
        $query = Lead::query();
        
        if ($leadIds) {
            $query->whereIn('id', $leadIds);
        } else {
            // Add all qualified leads by default
            $query->where('score', '>=', 40);
        }

        $leads = $query->get();
        $recipients = [];

        foreach ($leads as $lead) {
            $recipients[] = [
                'email' => $lead->email,
                'name' => $lead->name,
                'data' => [
                    'lead_id' => $lead->id,
                    'company' => $lead->company,
                    'service_interest' => $lead->service_interest,
                    'score' => $lead->score,
                ],
            ];
        }

        return $this->addRecipientsToCampaign($campaign, $recipients);
    }

    public function scheduleCampaign(EmailCampaign $campaign, \DateTime $scheduledAt): bool
    {
        if ($campaign->recipients()->count() === 0) {
            throw new \Exception('Cannot schedule campaign without recipients');
        }

        $campaign->update([
            'status' => 'scheduled',
            'scheduled_at' => $scheduledAt,
        ]);

        return true;
    }

    public function sendCampaign(EmailCampaign $campaign): array
    {
        if (!$campaign->isReadyToSend()) {
            throw new \Exception('Campaign is not ready to send');
        }

        $results = [
            'sent' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        $recipients = $campaign->recipients()
            ->where('status', 'pending')
            ->get();

        foreach ($recipients as $recipient) {
            try {
                $this->sendEmailToRecipient($campaign, $recipient);
                $results['sent']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'email' => $recipient->email,
                    'error' => $e->getMessage(),
                ];

                $recipient->markAsBounced($e->getMessage());
            }
        }

        // Update campaign statistics
        $campaign->update([
            'sent_count' => $results['sent'],
            'status' => 'sent',
        ]);

        $campaign->markAsSent();

        return $results;
    }

    protected function sendEmailToRecipient(EmailCampaign $campaign, EmailCampaignRecipient $recipient): void
    {
        $emailAccount = $this->getAvailableEmailAccount();
        
        if (!$emailAccount) {
            throw new \Exception('No available email accounts');
        }

        // Prepare email content
        $content = $this->prepareEmailContent($campaign, $recipient);
        $subject = $this->prepareEmailSubject($campaign, $recipient);

        // Configure mail settings for this account
        $this->configureMailSettings($emailAccount);

        // Send email
        Mail::html($content, function ($message) use ($recipient, $subject, $emailAccount) {
            $message->to($recipient->email, $recipient->name)
                    ->subject($subject)
                    ->from($emailAccount->email, $emailAccount->name);
        });

        // Mark as sent and update account usage
        $recipient->markAsSent();
        $emailAccount->incrementEmailCount();

        Log::info('Email sent successfully', [
            'campaign_id' => $campaign->id,
            'recipient_email' => $recipient->email,
            'email_account' => $emailAccount->email,
        ]);
    }

    protected function prepareEmailContent(EmailCampaign $campaign, EmailCampaignRecipient $recipient): string
    {
        $content = $campaign->content;

        // Use template if available
        if ($campaign->template) {
            $variables = array_merge(
                $recipient->personalization_data ?? [],
                [
                    'recipient_name' => $recipient->name,
                    'recipient_email' => $recipient->email,
                    'unsubscribe_url' => $this->generateUnsubscribeUrl($recipient),
                    'tracking_pixel' => $this->generateTrackingPixel($recipient),
                ]
            );

            $content = $campaign->template->renderContent($variables);
        }

        // Add tracking pixel
        $content .= $this->generateTrackingPixel($recipient);

        return $content;
    }

    protected function prepareEmailSubject(EmailCampaign $campaign, EmailCampaignRecipient $recipient): string
    {
        $subject = $campaign->subject;

        if ($campaign->template) {
            $variables = array_merge(
                $recipient->personalization_data ?? [],
                [
                    'recipient_name' => $recipient->name,
                    'recipient_email' => $recipient->email,
                ]
            );

            $subject = $campaign->template->renderSubject($variables);
        }

        return $subject;
    }

    protected function getAvailableEmailAccount(): ?EmailAccount
    {
        return EmailAccount::getNextAvailable();
    }

    protected function configureMailSettings(EmailAccount $emailAccount): void
    {
        $config = $emailAccount->getMailConfig();
        
        config(['mail.mailers.smtp' => $config]);
        config(['mail.from' => $config['from']]);
    }

    protected function generateUnsubscribeUrl(EmailCampaignRecipient $recipient): string
    {
        return route('email.unsubscribe', [
            'token' => encrypt($recipient->id),
        ]);
    }

    protected function generateTrackingPixel(EmailCampaignRecipient $recipient): string
    {
        $trackingUrl = route('email.track.open', [
            'token' => encrypt($recipient->id),
        ]);

        return "<img src=\"{$trackingUrl}\" width=\"1\" height=\"1\" style=\"display:none;\" />";
    }

    public function trackEmailOpen(string $token): void
    {
        try {
            $recipientId = decrypt($token);
            $recipient = EmailCampaignRecipient::find($recipientId);

            if ($recipient) {
                $recipient->markAsOpened();
                
                // Update campaign stats
                $campaign = $recipient->campaign;
                $campaign->increment('opened_count');
            }
        } catch (\Exception $e) {
            Log::warning('Failed to track email open', ['token' => $token, 'error' => $e->getMessage()]);
        }
    }

    public function trackEmailClick(string $token, string $url): void
    {
        try {
            $recipientId = decrypt($token);
            $recipient = EmailCampaignRecipient::find($recipientId);

            if ($recipient) {
                $recipient->markAsClicked();
                
                // Update campaign stats
                $campaign = $recipient->campaign;
                $campaign->increment('clicked_count');

                // Track the specific URL clicked
                $trackingData = $recipient->tracking_data ?? [];
                $trackingData['clicked_urls'][] = [
                    'url' => $url,
                    'clicked_at' => now()->toISOString(),
                ];
                $recipient->update(['tracking_data' => $trackingData]);
            }
        } catch (\Exception $e) {
            Log::warning('Failed to track email click', ['token' => $token, 'error' => $e->getMessage()]);
        }
    }

    public function unsubscribeRecipient(string $token): bool
    {
        try {
            $recipientId = decrypt($token);
            $recipient = EmailCampaignRecipient::find($recipientId);

            if ($recipient) {
                $recipient->markAsUnsubscribed();
                
                // Update campaign stats
                $campaign = $recipient->campaign;
                $campaign->increment('unsubscribed_count');

                // Add to global unsubscribe list
                $this->addToUnsubscribeList($recipient->email);

                return true;
            }
        } catch (\Exception $e) {
            Log::warning('Failed to unsubscribe recipient', ['token' => $token, 'error' => $e->getMessage()]);
        }

        return false;
    }

    protected function addToUnsubscribeList(string $email): void
    {
        // Add email to global unsubscribe list
        // This could be a separate model or service
    }

    public function getCampaignAnalytics(EmailCampaign $campaign): array
    {
        return [
            'performance' => $campaign->getPerformanceMetrics(),
            'engagement_timeline' => $this->getEngagementTimeline($campaign),
            'top_clicked_links' => $this->getTopClickedLinks($campaign),
            'geographic_data' => $this->getGeographicData($campaign),
        ];
    }

    protected function getEngagementTimeline(EmailCampaign $campaign): array
    {
        // Implementation for engagement timeline
        return [];
    }

    protected function getTopClickedLinks(EmailCampaign $campaign): array
    {
        // Implementation for top clicked links
        return [];
    }

    protected function getGeographicData(EmailCampaign $campaign): array
    {
        // Implementation for geographic data
        return [];
    }
}
