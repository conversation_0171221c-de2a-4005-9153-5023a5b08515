<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\LeadManagementController;
use App\Http\Controllers\Api\SettingsController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Public Lead Capture (for frontend forms)
Route::post('/leads', [LeadManagementController::class, 'store'])
    ->middleware('throttle:10,1'); // 10 requests per minute

// Lead Management API Routes
Route::prefix('leads')->group(function () {
    // Lead insights and analytics
    Route::get('{lead}/insights', [LeadManagementController::class, 'getLeadInsights']);
    Route::get('{lead}/journey', [LeadManagementController::class, 'getLeadJourney']);
    Route::get('{lead}/predictions', [LeadManagementController::class, 'predictConversion']);
    Route::get('{lead}/recommendations', [LeadManagementController::class, 'getLeadRecommendations']);
    
    // Lead scoring
    Route::post('{lead}/update-score', [LeadManagementController::class, 'updateLeadScore']);
    Route::post('bulk-update-scores', [LeadManagementController::class, 'bulkUpdateScores']);
    
    // Lead distribution
    Route::post('{lead}/reassign', [LeadManagementController::class, 'reassignLead']);
    Route::get('distribution-stats', [LeadManagementController::class, 'getDistributionStats']);
    Route::post('rebalance', [LeadManagementController::class, 'rebalanceLeads']);
});

// Behavioral tracking
Route::post('track-event', [LeadManagementController::class, 'trackEvent']);

// Email Marketing API Routes
Route::prefix('email-campaigns')->group(function () {
    Route::get('/', [\App\Http\Controllers\Api\EmailCampaignController::class, 'index']);
    Route::post('/', [\App\Http\Controllers\Api\EmailCampaignController::class, 'store']);
    Route::get('{campaign}', [\App\Http\Controllers\Api\EmailCampaignController::class, 'show']);
    Route::put('{campaign}', [\App\Http\Controllers\Api\EmailCampaignController::class, 'update']);
    Route::delete('{campaign}', [\App\Http\Controllers\Api\EmailCampaignController::class, 'destroy']);

    // Campaign actions
    Route::post('{campaign}/send', [\App\Http\Controllers\Api\EmailCampaignController::class, 'send']);
    Route::post('{campaign}/schedule', [\App\Http\Controllers\Api\EmailCampaignController::class, 'schedule']);
    Route::post('{campaign}/recipients', [\App\Http\Controllers\Api\EmailCampaignController::class, 'addRecipients']);
    Route::post('{campaign}/leads', [\App\Http\Controllers\Api\EmailCampaignController::class, 'addLeads']);
    Route::get('{campaign}/analytics', [\App\Http\Controllers\Api\EmailCampaignController::class, 'analytics']);
});

// Email Templates
Route::get('email-templates', [\App\Http\Controllers\Api\EmailCampaignController::class, 'templates']);

// WhatsApp Marketing API Routes
Route::prefix('whatsapp-campaigns')->group(function () {
    Route::get('/', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'index']);
    Route::post('/', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'store']);
    Route::get('{campaign}', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'show']);
    Route::put('{campaign}', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'update']);
    Route::delete('{campaign}', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'destroy']);

    // Campaign actions
    Route::post('{campaign}/send', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'send']);
    Route::get('{campaign}/analytics', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'analytics']);
});

// WhatsApp messaging
Route::prefix('whatsapp')->group(function () {
    Route::post('send-to-leads', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'sendToLeads']);
    Route::post('send-bulk', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'sendBulk']);
    Route::get('numbers', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'getNumbers']);
    Route::get('templates', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'getTemplates']);
    Route::post('webhook', [\App\Http\Controllers\Api\WhatsAppCampaignController::class, 'webhook']);
});

// Social Media Management API Routes
Route::prefix('social-media')->group(function () {
    // Posts management
    Route::get('posts', [\App\Http\Controllers\Api\SocialMediaController::class, 'index']);
    Route::post('posts', [\App\Http\Controllers\Api\SocialMediaController::class, 'store']);
    Route::get('posts/{post}', [\App\Http\Controllers\Api\SocialMediaController::class, 'show']);
    Route::put('posts/{post}', [\App\Http\Controllers\Api\SocialMediaController::class, 'update']);
    Route::delete('posts/{post}', [\App\Http\Controllers\Api\SocialMediaController::class, 'destroy']);

    // Post actions
    Route::post('posts/{post}/publish', [\App\Http\Controllers\Api\SocialMediaController::class, 'publish']);
    Route::post('posts/{post}/schedule', [\App\Http\Controllers\Api\SocialMediaController::class, 'schedule']);

    // Content generation
    Route::post('generate-content', [\App\Http\Controllers\Api\SocialMediaController::class, 'generateContent']);
    Route::get('content-calendar', [\App\Http\Controllers\Api\SocialMediaController::class, 'generateCalendar']);
    Route::get('content-suggestions', [\App\Http\Controllers\Api\SocialMediaController::class, 'getContentSuggestions']);
    Route::get('optimal-times', [\App\Http\Controllers\Api\SocialMediaController::class, 'getOptimalTimes']);

    // Accounts and analytics
    Route::get('accounts', [\App\Http\Controllers\Api\SocialMediaController::class, 'getAccounts']);
    Route::get('analytics', [\App\Http\Controllers\Api\SocialMediaController::class, 'getAnalytics']);
    Route::get('accounts/{account}/analytics', [\App\Http\Controllers\Api\SocialMediaController::class, 'getAccountAnalytics']);
    Route::get('reports', [\App\Http\Controllers\Api\SocialMediaController::class, 'generateReport']);
});

// Analytics endpoints
Route::prefix('analytics')->group(function () {
    Route::get('dashboard-stats', function () {
        return response()->json([
            'total_leads' => \App\Models\Lead::count(),
            'new_leads_today' => \App\Models\Lead::whereDate('created_at', today())->count(),
            'conversion_rate' => \App\Models\AnalyticsEvent::getConversionRate(),
            'top_sources' => \App\Models\AnalyticsEvent::getTrafficSources(30),
            'email_campaigns' => \App\Models\EmailCampaign::count(),
            'emails_sent_today' => \App\Models\EmailCampaignRecipient::whereDate('sent_at', today())->count(),
            'whatsapp_campaigns' => \App\Models\WhatsAppCampaign::count(),
            'whatsapp_messages_today' => \App\Models\WhatsAppMessage::whereDate('sent_at', today())->count(),
            'whatsapp_numbers' => \App\Models\WhatsAppNumber::active()->count(),
            'social_media_posts' => \App\Models\SocialMediaPost::count(),
            'posts_published_today' => \App\Models\SocialMediaPost::whereDate('published_at', today())->count(),
            'social_media_accounts' => \App\Models\SocialMediaAccount::active()->count(),
        ]);
    });

    Route::get('lead-quality-distribution', function () {
        $distribution = \App\Models\Lead::selectRaw('
            CASE
                WHEN score >= 80 THEN "hot"
                WHEN score >= 60 THEN "warm"
                WHEN score >= 40 THEN "qualified"
                WHEN score >= 20 THEN "cold"
                ELSE "unqualified"
            END as quality,
            COUNT(*) as count
        ')
        ->groupBy('quality')
        ->get();

        return response()->json($distribution);
    });

    Route::get('email-performance', function () {
        $campaigns = \App\Models\EmailCampaign::sent()
            ->selectRaw('
                COUNT(*) as total_campaigns,
                SUM(sent_count) as total_sent,
                SUM(opened_count) as total_opened,
                SUM(clicked_count) as total_clicked,
                AVG(CASE WHEN sent_count > 0 THEN (opened_count / sent_count) * 100 ELSE 0 END) as avg_open_rate,
                AVG(CASE WHEN sent_count > 0 THEN (clicked_count / sent_count) * 100 ELSE 0 END) as avg_click_rate
            ')
            ->first();

        return response()->json($campaigns);
    });

    Route::get('whatsapp-performance', function () {
        $campaigns = \App\Models\WhatsAppCampaign::sent()
            ->selectRaw('
                COUNT(*) as total_campaigns,
                SUM(sent_count) as total_sent,
                SUM(delivered_count) as total_delivered,
                SUM(read_count) as total_read,
                SUM(response_count) as total_responses,
                AVG(CASE WHEN sent_count > 0 THEN (delivered_count / sent_count) * 100 ELSE 0 END) as avg_delivery_rate,
                AVG(CASE WHEN sent_count > 0 THEN (read_count / sent_count) * 100 ELSE 0 END) as avg_read_rate,
                AVG(CASE WHEN sent_count > 0 THEN (response_count / sent_count) * 100 ELSE 0 END) as avg_response_rate
            ')
            ->first();

        return response()->json($campaigns);
    });

    Route::get('social-media-performance', function () {
        $posts = \App\Models\SocialMediaPost::where('status', 'published')
            ->get();

        $totalEngagement = $posts->sum(function ($post) {
            $analytics = $post->analytics ?? [];
            return ($analytics['likes'] ?? 0) +
                   ($analytics['comments'] ?? 0) +
                   ($analytics['shares'] ?? 0);
        });

        $totalReach = $posts->sum(function ($post) {
            return $post->analytics['reach'] ?? 0;
        });

        $totalImpressions = $posts->sum(function ($post) {
            return $post->analytics['impressions'] ?? 0;
        });

        return response()->json([
            'total_posts' => $posts->count(),
            'total_engagement' => $totalEngagement,
            'total_reach' => $totalReach,
            'total_impressions' => $totalImpressions,
            'avg_engagement_rate' => $totalImpressions > 0 ? ($totalEngagement / $totalImpressions) * 100 : 0,
            'avg_engagement_per_post' => $posts->count() > 0 ? $totalEngagement / $posts->count() : 0,
        ]);
    });
});

// Advanced Analytics Dashboard API Routes
Route::prefix('analytics-dashboard')->group(function () {
    // Main dashboard endpoints
    Route::get('overview', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'overview']);
    Route::get('real-time', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'realTime']);

    // Detailed analytics
    Route::get('leads', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'leadAnalytics']);
    Route::get('marketing', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'marketingPerformance']);
    Route::get('conversion-funnel', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'conversionFunnel']);
    Route::get('revenue', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'revenueAnalytics']);
    Route::get('traffic', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'trafficAnalytics']);
    Route::get('engagement', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'engagementMetrics']);
    Route::get('growth', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'growthTrends']);

    // Real-time specific endpoints
    Route::get('live-visitors', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'liveVisitors']);
    Route::get('active-sessions', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'activeSessions']);
    Route::get('recent-leads', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'recentLeads']);
    Route::get('live-events', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'liveEvents']);
    Route::get('current-campaigns', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'currentCampaigns']);
    Route::get('real-time-metrics', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'realTimeMetrics']);
    Route::get('geographic-activity', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'geographicActivity']);
    Route::get('device-breakdown', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'deviceBreakdown']);
    Route::get('alerts', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'alerts']);

    // Predictive analytics
    Route::get('predictive-insights', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'predictiveInsights']);
    Route::post('custom-report', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'customReport']);

    // Event tracking
    Route::post('track-event', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'trackEvent']);

    // Data export
    Route::get('export', [\App\Http\Controllers\Api\AnalyticsDashboardController::class, 'exportData']);
});

// Automated Lead Scoring API Routes
Route::prefix('lead-scoring')->group(function () {
    // Individual lead scoring
    Route::get('leads/{lead}/score', [\App\Http\Controllers\Api\LeadScoringController::class, 'getLeadScore']);
    Route::post('leads/{lead}/update-score', [\App\Http\Controllers\Api\LeadScoringController::class, 'updateLeadScore']);
    Route::get('leads/{lead}/insights', [\App\Http\Controllers\Api\LeadScoringController::class, 'getLeadInsights']);
    Route::get('leads/{lead}/profile', [\App\Http\Controllers\Api\LeadScoringController::class, 'getLeadProfile']);
    Route::get('leads/{lead}/timeline', [\App\Http\Controllers\Api\LeadScoringController::class, 'getEngagementTimeline']);
    Route::get('leads/{lead}/behavior', [\App\Http\Controllers\Api\LeadScoringController::class, 'getBehavioralPatterns']);
    Route::get('leads/{lead}/intent', [\App\Http\Controllers\Api\LeadScoringController::class, 'getIntentSignals']);
    Route::get('leads/{lead}/conversion-probability', [\App\Http\Controllers\Api\LeadScoringController::class, 'getConversionProbability']);
    Route::get('leads/{lead}/optimal-contact-time', [\App\Http\Controllers\Api\LeadScoringController::class, 'getOptimalContactTime']);
    Route::get('leads/{lead}/personalization', [\App\Http\Controllers\Api\LeadScoringController::class, 'getPersonalizationData']);
    Route::get('leads/{lead}/risks', [\App\Http\Controllers\Api\LeadScoringController::class, 'getRiskFactors']);
    Route::get('leads/{lead}/opportunity', [\App\Http\Controllers\Api\LeadScoringController::class, 'getOpportunityScore']);
    Route::get('leads/{lead}/recommendations', [\App\Http\Controllers\Api\LeadScoringController::class, 'getLeadRecommendations']);

    // Batch operations
    Route::post('batch-update', [\App\Http\Controllers\Api\LeadScoringController::class, 'batchUpdateScores']);

    // Lead lists and filtering
    Route::get('leads-by-score', [\App\Http\Controllers\Api\LeadScoringController::class, 'getLeadsByScore']);
    Route::get('top-leads', [\App\Http\Controllers\Api\LeadScoringController::class, 'getTopLeads']);

    // Analytics and reporting
    Route::get('score-distribution', [\App\Http\Controllers\Api\LeadScoringController::class, 'getScoreDistribution']);
    Route::get('scoring-rules', [\App\Http\Controllers\Api\LeadScoringController::class, 'getScoringRules']);
    Route::get('export', [\App\Http\Controllers\Api\LeadScoringController::class, 'exportLeadScores']);
});

// Metal Rates API Routes
Route::prefix('metal-rates')->group(function () {
    // Current rates and data
    Route::get('current', [\App\Http\Controllers\Api\MetalRatesController::class, 'getCurrentRates']);
    Route::get('historical/{metal}', [\App\Http\Controllers\Api\MetalRatesController::class, 'getHistoricalRates']);
    Route::get('supported-metals', [\App\Http\Controllers\Api\MetalRatesController::class, 'getSupportedMetals']);
    Route::get('market-summary', [\App\Http\Controllers\Api\MetalRatesController::class, 'getMarketSummary']);
    Route::get('widget', [\App\Http\Controllers\Api\MetalRatesController::class, 'getWidget']);

    // Rate management
    Route::post('refresh', [\App\Http\Controllers\Api\MetalRatesController::class, 'refreshRates']);
    Route::get('reports', [\App\Http\Controllers\Api\MetalRatesController::class, 'generateReport']);

    // Alerts management
    Route::post('alerts', [\App\Http\Controllers\Api\MetalRatesController::class, 'createAlert']);
    Route::get('alerts', [\App\Http\Controllers\Api\MetalRatesController::class, 'getAlerts']);
    Route::put('alerts/{alert}', [\App\Http\Controllers\Api\MetalRatesController::class, 'updateAlert']);
    Route::delete('alerts/{alert}', [\App\Http\Controllers\Api\MetalRatesController::class, 'deleteAlert']);
    Route::post('check-alerts', [\App\Http\Controllers\Api\MetalRatesController::class, 'checkAlerts']);

    // Subscriptions management
    Route::post('subscribe', [\App\Http\Controllers\Api\MetalRatesController::class, 'subscribe']);
    Route::get('subscriptions', [\App\Http\Controllers\Api\MetalRatesController::class, 'getSubscriptions']);
    Route::put('subscriptions/{subscription}', [\App\Http\Controllers\Api\MetalRatesController::class, 'updateSubscription']);
    Route::post('subscriptions/{subscription}/unsubscribe', [\App\Http\Controllers\Api\MetalRatesController::class, 'unsubscribe']);
    Route::post('broadcast-updates', [\App\Http\Controllers\Api\MetalRatesController::class, 'broadcastUpdates']);
});

// Customer Portal API Routes
Route::prefix('customer-portal')->middleware('auth:sanctum')->group(function () {
    // Dashboard and overview
    Route::get('dashboard', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'dashboard']);
    Route::get('quick-stats', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'getQuickStats']);
    Route::get('search', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'searchContent']);

    // Projects
    Route::get('projects', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'projects']);
    Route::get('projects/{project}', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'getProject']);

    // Invoices
    Route::get('invoices', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'invoices']);
    Route::get('invoices/{invoice}', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'getInvoice']);

    // Support tickets
    Route::get('support-tickets', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'supportTickets']);
    Route::post('support-tickets', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'createSupportTicket']);
    Route::get('support-tickets/{ticket}', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'getSupportTicket']);
    Route::post('support-tickets/{ticket}/responses', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'addTicketResponse']);

    // Documents
    Route::get('documents', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'documents']);

    // Notifications
    Route::get('notifications', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'notifications']);
    Route::post('notifications/{notification}/mark-read', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'markNotificationAsRead']);
    Route::post('notifications/mark-all-read', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'markAllNotificationsAsRead']);

    // Profile management
    Route::get('profile', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'getProfile']);
    Route::put('profile', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'updateProfile']);
    Route::post('change-password', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'changePassword']);

    // Activity tracking
    Route::get('activity', [\App\Http\Controllers\Api\CustomerPortalApiController::class, 'getActivity']);
});

// Public Settings API Routes
Route::prefix('settings')->group(function () {
    Route::get('/', [SettingsController::class, 'index']);
    Route::get('/categories', [SettingsController::class, 'categories']);
    Route::get('/system-info', [SettingsController::class, 'systemInfo']);
    Route::get('/email-config', [SettingsController::class, 'emailConfig']);
    Route::get('/geographic-config', [SettingsController::class, 'geographicConfig']);
    Route::get('/performance-config', [SettingsController::class, 'performanceConfig']);
    Route::get('/category/{category}', [SettingsController::class, 'category']);
    Route::get('/{key}', [SettingsController::class, 'show']);
});

// Chat API Routes
Route::prefix('chat')->group(function () {
    Route::post('sessions', [\App\Http\Controllers\Api\ChatController::class, 'startSession']);
    Route::get('sessions/{sessionId}', [\App\Http\Controllers\Api\ChatController::class, 'getSession']);
    Route::post('messages', [\App\Http\Controllers\Api\ChatController::class, 'sendMessage']);
    Route::get('sessions/{sessionId}/messages', [\App\Http\Controllers\Api\ChatController::class, 'getMessages']);
    Route::post('sessions/{sessionId}/close', [\App\Http\Controllers\Api\ChatController::class, 'closeSession']);
    Route::post('typing', [\App\Http\Controllers\Api\ChatController::class, 'sendTypingIndicator']);
});
