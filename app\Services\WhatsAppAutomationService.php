<?php

namespace App\Services;

use App\Models\WhatsAppMessage;
use App\Models\Lead;
use App\Models\BusinessSetting;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class WhatsAppAutomationService
{
    protected WhatsAppMarketingService $whatsappService;

    public function __construct(WhatsAppMarketingService $whatsappService)
    {
        $this->whatsappService = $whatsappService;
    }

    public function handleIncomingMessage(array $messageData): void
    {
        $phone = $this->formatPhoneNumber($messageData['from']);
        $messageText = $messageData['text']['body'] ?? '';
        $messageType = $messageData['type'] ?? 'text';

        // Check if auto-responses are enabled
        if (!BusinessSetting::get('whatsapp_auto_responses', true)) {
            return;
        }

        // Check business hours
        if (!$this->isBusinessHours() && BusinessSetting::get('whatsapp_business_hours_only', false)) {
            $this->sendBusinessHoursMessage($phone);
            return;
        }

        // Process the message based on content
        $response = $this->generateAutoResponse($messageText, $phone);
        
        if ($response) {
            $this->whatsappService->sendMessage([
                'phone' => $phone,
                'message' => $response['message'],
                'type' => $response['type'] ?? 'text',
                'interactive_data' => $response['interactive_data'] ?? null,
                'is_automated' => true,
            ]);
        }
    }

    protected function generateAutoResponse(string $message, string $phone): ?array
    {
        $message = strtolower(trim($message));
        
        // Greeting responses
        if ($this->isGreeting($message)) {
            return $this->getGreetingResponse($phone);
        }

        // Service inquiries
        if ($this->isServiceInquiry($message)) {
            return $this->getServiceResponse($message);
        }

        // Pricing inquiries
        if ($this->isPricingInquiry($message)) {
            return $this->getPricingResponse();
        }

        // Contact information requests
        if ($this->isContactInquiry($message)) {
            return $this->getContactResponse();
        }

        // FAQ responses
        $faqResponse = $this->getFAQResponse($message);
        if ($faqResponse) {
            return $faqResponse;
        }

        // Default response for unrecognized messages
        return $this->getDefaultResponse();
    }

    protected function isGreeting(string $message): bool
    {
        $greetings = ['hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening', 'namaste'];
        
        foreach ($greetings as $greeting) {
            if (str_contains($message, $greeting)) {
                return true;
            }
        }
        
        return false;
    }

    protected function getGreetingResponse(string $phone): array
    {
        $lead = Lead::where('phone', $phone)->first();
        $name = $lead ? $lead->name : 'there';
        
        $responses = [
            "Hello {$name}! 👋 Welcome to Bhavitech. How can we help you today?",
            "Hi {$name}! 😊 Thanks for reaching out to Bhavitech. What can we do for you?",
            "Hey {$name}! Great to hear from you. How can Bhavitech assist you today?",
        ];

        return [
            'message' => $responses[array_rand($responses)],
            'type' => 'interactive',
            'interactive_data' => [
                'type' => 'button',
                'body' => ['text' => 'What would you like to know about?'],
                'action' => [
                    'buttons' => [
                        ['type' => 'reply', 'reply' => ['id' => 'services', 'title' => 'Our Services']],
                        ['type' => 'reply', 'reply' => ['id' => 'pricing', 'title' => 'Pricing Info']],
                        ['type' => 'reply', 'reply' => ['id' => 'contact', 'title' => 'Contact Us']],
                    ]
                ]
            ]
        ];
    }

    protected function isServiceInquiry(string $message): bool
    {
        $serviceKeywords = [
            'website', 'web development', 'mobile app', 'app development',
            'digital marketing', 'seo', 'graphic design', 'logo design',
            'services', 'what do you do', 'what services'
        ];
        
        foreach ($serviceKeywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }
        
        return false;
    }

    protected function getServiceResponse(string $message): array
    {
        $services = [
            'web' => 'Website Development',
            'mobile' => 'Mobile App Development', 
            'marketing' => 'Digital Marketing',
            'design' => 'Graphic Design'
        ];

        $detectedService = null;
        foreach ($services as $key => $service) {
            if (str_contains($message, $key) || str_contains($message, strtolower($service))) {
                $detectedService = $service;
                break;
            }
        }

        if ($detectedService) {
            $response = "Great! You're interested in our {$detectedService} services. ";
        } else {
            $response = "We offer a comprehensive range of digital services including:\n\n";
        }

        $response .= "🌐 Website Development\n📱 Mobile App Development\n📈 Digital Marketing & SEO\n🎨 Graphic Design & Branding\n\n";
        $response .= "Would you like to know more about any specific service or get a free quote?";

        return [
            'message' => $response,
            'type' => 'interactive',
            'interactive_data' => [
                'type' => 'list',
                'body' => ['text' => 'Choose a service to learn more:'],
                'action' => [
                    'button' => 'View Services',
                    'sections' => [
                        [
                            'title' => 'Our Services',
                            'rows' => [
                                ['id' => 'web_dev', 'title' => 'Website Development', 'description' => 'Custom websites & web apps'],
                                ['id' => 'mobile_dev', 'title' => 'Mobile Apps', 'description' => 'iOS & Android development'],
                                ['id' => 'digital_marketing', 'title' => 'Digital Marketing', 'description' => 'SEO, social media, ads'],
                                ['id' => 'graphic_design', 'title' => 'Graphic Design', 'description' => 'Logos, branding, UI/UX'],
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }

    protected function isPricingInquiry(string $message): bool
    {
        $pricingKeywords = ['price', 'cost', 'quote', 'estimate', 'budget', 'how much', 'pricing'];
        
        foreach ($pricingKeywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }
        
        return false;
    }

    protected function getPricingResponse(): array
    {
        return [
            'message' => "💰 Our pricing depends on your specific requirements. Here's what we offer:\n\n" .
                        "✅ Free consultation & project analysis\n" .
                        "✅ Transparent, fixed-price quotes\n" .
                        "✅ Flexible payment plans\n" .
                        "✅ No hidden costs\n\n" .
                        "Would you like to get a free quote for your project?",
            'type' => 'interactive',
            'interactive_data' => [
                'type' => 'button',
                'body' => ['text' => 'Get your free quote today!'],
                'action' => [
                    'buttons' => [
                        ['type' => 'reply', 'reply' => ['id' => 'get_quote', 'title' => 'Get Free Quote']],
                        ['type' => 'reply', 'reply' => ['id' => 'call_us', 'title' => 'Call Us Now']],
                        ['type' => 'reply', 'reply' => ['id' => 'more_info', 'title' => 'More Info']],
                    ]
                ]
            ]
        ];
    }

    protected function isContactInquiry(string $message): bool
    {
        $contactKeywords = ['contact', 'phone', 'email', 'address', 'location', 'office', 'reach you'];
        
        foreach ($contactKeywords as $keyword) {
            if (str_contains($message, $keyword)) {
                return true;
            }
        }
        
        return false;
    }

    protected function getContactResponse(): array
    {
        $companyPhone = BusinessSetting::get('company_phone', '**********');
        $companyEmail = BusinessSetting::get('company_email', '<EMAIL>');
        $companyAddress = BusinessSetting::get('company_address', 'Convent Road, Fairlands, Salem - 636016');

        return [
            'message' => "📞 Contact Information:\n\n" .
                        "📱 Phone: +91 {$companyPhone}\n" .
                        "📧 Email: {$companyEmail}\n" .
                        "📍 Address: {$companyAddress}\n\n" .
                        "🕒 Business Hours: Mon-Sat, 9 AM - 6 PM\n\n" .
                        "Feel free to call us directly or continue chatting here!",
        ];
    }

    protected function getFAQResponse(string $message): ?array
    {
        $faqs = [
            'how long' => [
                'question' => 'Project timeline',
                'answer' => 'Project timelines vary based on complexity:\n\n' .
                          '🌐 Simple websites: 1-2 weeks\n' .
                          '📱 Mobile apps: 4-8 weeks\n' .
                          '🎨 Design projects: 3-7 days\n' .
                          '📈 Marketing campaigns: 2-4 weeks\n\n' .
                          'We provide detailed timelines during consultation.'
            ],
            'support' => [
                'question' => 'Support and maintenance',
                'answer' => 'We provide comprehensive support:\n\n' .
                          '✅ 30 days free support after project completion\n' .
                          '✅ Annual maintenance packages available\n' .
                          '✅ 24/7 emergency support for critical issues\n' .
                          '✅ Regular updates and security patches'
            ],
            'payment' => [
                'question' => 'Payment terms',
                'answer' => 'Flexible payment options:\n\n' .
                          '💳 50% advance, 50% on completion\n' .
                          '💰 EMI options available for larger projects\n' .
                          '🏦 Bank transfer, UPI, or cash payments\n' .
                          '📄 GST invoices provided'
            ]
        ];

        foreach ($faqs as $keyword => $faq) {
            if (str_contains($message, $keyword)) {
                return ['message' => $faq['answer']];
            }
        }

        return null;
    }

    protected function getDefaultResponse(): array
    {
        return [
            'message' => "Thanks for your message! 😊\n\n" .
                        "I'm here to help you with information about our services. " .
                        "For detailed assistance, our team will get back to you shortly.\n\n" .
                        "In the meantime, you can:\n" .
                        "• Ask about our services\n" .
                        "• Request a quote\n" .
                        "• Get our contact information\n\n" .
                        "Or call us directly at +91 **********",
        ];
    }

    protected function sendBusinessHoursMessage(string $phone): void
    {
        $message = "🕒 Thank you for contacting Bhavitech!\n\n" .
                  "We're currently outside business hours (Mon-Sat, 9 AM - 6 PM).\n\n" .
                  "Your message is important to us and we'll respond first thing during business hours.\n\n" .
                  "For urgent matters, please call +91 **********";

        $this->whatsappService->sendMessage([
            'phone' => $phone,
            'message' => $message,
            'is_automated' => true,
        ]);
    }

    protected function isBusinessHours(): bool
    {
        $now = Carbon::now('Asia/Kolkata');
        $dayOfWeek = $now->dayOfWeek; // 0 = Sunday, 6 = Saturday
        $hour = $now->hour;

        // Monday to Saturday, 9 AM to 6 PM
        return $dayOfWeek >= 1 && $dayOfWeek <= 6 && $hour >= 9 && $hour < 18;
    }

    protected function formatPhoneNumber(string $phone): string
    {
        return preg_replace('/[^0-9]/', '', $phone);
    }

    public function sendWelcomeMessage(Lead $lead): void
    {
        if (!$lead->phone) {
            return;
        }

        $message = "🎉 Welcome to Bhavitech, {$lead->name}!\n\n" .
                  "Thank you for your interest in our {$lead->service_interest} services.\n\n" .
                  "Our team will review your requirements and get back to you within 24 hours.\n\n" .
                  "In the meantime, feel free to ask any questions here!";

        $this->whatsappService->sendMessage([
            'phone' => $lead->phone,
            'name' => $lead->name,
            'message' => $message,
            'lead_id' => $lead->id,
            'is_automated' => true,
        ]);
    }

    public function sendFollowUpMessage(Lead $lead, int $daysAfter = 3): void
    {
        if (!$lead->phone) {
            return;
        }

        $message = "Hi {$lead->name}! 👋\n\n" .
                  "It's been {$daysAfter} days since you inquired about our {$lead->service_interest} services.\n\n" .
                  "Do you have any questions about your project? We're here to help!\n\n" .
                  "Reply to this message or call us at +91 **********";

        $this->whatsappService->sendMessage([
            'phone' => $lead->phone,
            'name' => $lead->name,
            'message' => $message,
            'lead_id' => $lead->id,
            'is_automated' => true,
        ]);
    }
}
