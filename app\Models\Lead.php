<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Lead extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'company',
        'source',
        'status',
        'score',
        'grade',
        'priority',
        'service_interest',
        'message',
        'metadata',
        'scoring_data',
        'visitor_id',
        'city',
        'state',
        'country',
        'last_contacted_at',
        'qualified_at',
        'converted_at',
        'assigned_to',
    ];

    protected $casts = [
        'metadata' => 'array',
        'scoring_data' => 'array',
        'last_contacted_at' => 'datetime',
        'qualified_at' => 'datetime',
        'converted_at' => 'datetime',
    ];

    // Relationships
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function interactions(): HasMany
    {
        return $this->hasMany(LeadInteraction::class);
    }

    public function whatsappMessages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class);
    }

    public function analyticsEvents(): HasMany
    {
        return $this->hasMany(AnalyticsEvent::class, 'user_id', 'id');
    }

    // Scopes
    public function scopeNew($query)
    {
        return $query->where('status', 'new');
    }

    public function scopeQualified($query)
    {
        return $query->where('status', 'qualified');
    }

    public function scopeConverted($query)
    {
        return $query->where('status', 'converted');
    }

    public function scopeHighScore($query, $threshold = 70)
    {
        return $query->where('score', '>=', $threshold);
    }

    // Methods
    public function calculateScore(): int
    {
        $score = 0;

        // Email domain scoring
        if ($this->email) {
            $domain = substr(strrchr($this->email, "@"), 1);
            if (in_array($domain, ['gmail.com', 'yahoo.com', 'hotmail.com'])) {
                $score += 10;
            } else {
                $score += 20; // Business email
            }
        }

        // Company presence
        if ($this->company) {
            $score += 15;
        }

        // Phone number
        if ($this->phone) {
            $score += 10;
        }

        // Service interest
        if ($this->service_interest) {
            $score += 15;
        }

        // Message quality (longer messages indicate higher interest)
        if ($this->message) {
            $messageLength = strlen($this->message);
            if ($messageLength > 100) {
                $score += 20;
            } elseif ($messageLength > 50) {
                $score += 10;
            } else {
                $score += 5;
            }
        }

        // Source scoring
        switch ($this->source) {
            case 'referral':
                $score += 25;
                break;
            case 'social':
                $score += 15;
                break;
            case 'website':
                $score += 10;
                break;
        }

        return min($score, 100); // Cap at 100
    }

    public function updateScore(): void
    {
        // Use the new AI-powered scoring system
        $score = LeadScoringRule::calculateLeadScore($this);
        $this->update(['score' => $score]);
    }

    public function markAsContacted(): void
    {
        $this->update([
            'last_contacted_at' => now(),
            'status' => $this->status === 'new' ? 'contacted' : $this->status,
        ]);
    }

    public function markAsQualified(): void
    {
        $this->update([
            'status' => 'qualified',
            'qualified_at' => now(),
        ]);
    }

    public function markAsConverted(): void
    {
        $this->update([
            'status' => 'converted',
            'converted_at' => now(),
        ]);
    }
}
