<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\WhatsAppCampaign;
use App\Models\User;

class WhatsAppTestSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('Creating WhatsApp test data...');

        $admin = User::where('role', 'admin')->first();

        // Create WhatsApp campaigns
        WhatsAppCampaign::firstOrCreate(['name' => 'Welcome WhatsApp Campaign'], [
            'message' => 'Hi {{name}}! Welcome to Bhavitech. We are excited to help transform your business with our digital solutions. Reply STOP to unsubscribe.',
            'type' => 'promotional',
            'status' => 'draft',
            'created_by' => $admin->id,
        ]);

        WhatsAppCampaign::firstOrCreate(['name' => 'Follow-up Campaign'], [
            'message' => 'Hi {{name}}, thank you for your interest in our {{service}} services. Our team will contact you shortly to discuss your requirements.',
            'type' => 'follow_up',
            'status' => 'active',
            'created_by' => $admin->id,
        ]);

        WhatsAppCampaign::firstOrCreate(['name' => 'Promotional Offer'], [
            'message' => '🎉 Special offer for {{name}}! Get 20% off on all our services this month. Contact us at +917010860889 for more details.',
            'type' => 'promotional',
            'status' => 'scheduled',
            'created_by' => $admin->id,
        ]);

        $this->command->info('✅ WhatsApp test data created successfully!');
        $this->command->info('📱 WhatsApp Campaigns: 3');
    }
}
