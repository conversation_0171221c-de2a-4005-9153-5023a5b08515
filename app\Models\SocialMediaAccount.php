<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;

class SocialMediaAccount extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'platform',
        'account_id',
        'username',
        'access_token',
        'refresh_token',
        'token_expires_at',
        'account_info',
        'permissions',
        'is_active',
        'auto_post',
        'posting_schedule',
        'daily_post_limit',
        'last_posted_at',
        'created_by',
    ];

    protected $casts = [
        'account_info' => 'array',
        'permissions' => 'array',
        'posting_schedule' => 'array',
        'is_active' => 'boolean',
        'auto_post' => 'boolean',
        'token_expires_at' => 'datetime',
        'last_posted_at' => 'datetime',
    ];

    // Relationships
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function posts(): HasMany
    {
        return $this->hasMany(SocialMediaPost::class, 'account_id');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByPlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }

    public function scopeAutoPost($query)
    {
        return $query->where('auto_post', true);
    }

    // Accessors & Mutators
    protected function accessToken(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? decrypt($value) : null,
            set: fn ($value) => $value ? encrypt($value) : null,
        );
    }

    protected function refreshToken(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $value ? decrypt($value) : null,
            set: fn ($value) => $value ? encrypt($value) : null,
        );
    }

    // Methods
    public function isTokenExpired(): bool
    {
        return $this->token_expires_at && $this->token_expires_at->isPast();
    }

    public function canPost(): bool
    {
        return $this->is_active && !$this->isTokenExpired() && $this->hasPostingPermission();
    }

    public function hasPostingPermission(): bool
    {
        $requiredPermissions = [
            'facebook' => ['pages_manage_posts', 'pages_read_engagement'],
            'instagram' => ['instagram_basic', 'instagram_content_publish'],
            'linkedin' => ['w_member_social'],
            'twitter' => ['tweet.write'],
            'youtube' => ['youtube.upload'],
        ];

        $required = $requiredPermissions[$this->platform] ?? [];
        $current = $this->permissions ?? [];

        return empty(array_diff($required, $current));
    }

    public function getRemainingPostsToday(): int
    {
        $postsToday = $this->posts()
            ->whereDate('published_at', today())
            ->count();

        return max(0, $this->daily_post_limit - $postsToday);
    }
}
